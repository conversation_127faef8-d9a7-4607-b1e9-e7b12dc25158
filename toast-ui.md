# [![logo](https://uicdn.toast.com/toastui/img/tui-chart-bi-white.png)](https://nhn.github.io/tui.chart/latest/)
/[github](https://github.com/nhn/tui.chart)|v4.6.1
* * *
APIExamples
## CLASSES
  * [AreaChart](https://nhn.github.io/tui.chart/latest/AreaChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/AreaChart#addData)
    * [addPlotBand](https://nhn.github.io/tui.chart/latest/AreaChart#addPlotBand)
    * [addPlotLine](https://nhn.github.io/tui.chart/latest/AreaChart#addPlotLine)
    * [addSeries](https://nhn.github.io/tui.chart/latest/AreaChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/AreaChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/AreaChart#hideTooltip)
    * [removePlotBand](https://nhn.github.io/tui.chart/latest/AreaChart#removePlotBand)
    * [removePlotLine](https://nhn.github.io/tui.chart/latest/AreaChart#removePlotLine)
    * [setData](https://nhn.github.io/tui.chart/latest/AreaChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/AreaChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/AreaChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/AreaChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/AreaChart#updateOptions)
  * [BarChart](https://nhn.github.io/tui.chart/latest/BarChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/BarChart#addData)
    * [addSeries](https://nhn.github.io/tui.chart/latest/BarChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/BarChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/BarChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/BarChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/BarChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/BarChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/BarChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/BarChart#updateOptions)
  * [BoxPlotChart](https://nhn.github.io/tui.chart/latest/BoxPlotChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/BoxPlotChart#addData)
    * [addOutlier](https://nhn.github.io/tui.chart/latest/BoxPlotChart#addOutlier)
    * [addSeries](https://nhn.github.io/tui.chart/latest/BoxPlotChart#addSeries)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/BoxPlotChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/BoxPlotChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/BoxPlotChart#setOptions)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/BoxPlotChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/BoxPlotChart#updateOptions)
  * [BubbleChart](https://nhn.github.io/tui.chart/latest/BubbleChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/BubbleChart#addData)
    * [addSeries](https://nhn.github.io/tui.chart/latest/BubbleChart#addSeries)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/BubbleChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/BubbleChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/BubbleChart#setOptions)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/BubbleChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/BubbleChart#updateOptions)
  * [BulletChart](https://nhn.github.io/tui.chart/latest/BulletChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addSeries](https://nhn.github.io/tui.chart/latest/BulletChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/BulletChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/BulletChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/BulletChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/BulletChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/BulletChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/BulletChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/BulletChart#updateOptions)
  * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [destroy](https://nhn.github.io/tui.chart/latest/Chart#destroy)
    * [getCheckedLegend](https://nhn.github.io/tui.chart/latest/Chart#getCheckedLegend)
    * [getOptions](https://nhn.github.io/tui.chart/latest/Chart#getOptions)
    * [on](https://nhn.github.io/tui.chart/latest/Chart#on)
    * [resize](https://nhn.github.io/tui.chart/latest/Chart#resize)
    * [selectSeries](https://nhn.github.io/tui.chart/latest/Chart#selectSeries)
    * [setTooltipOffset](https://nhn.github.io/tui.chart/latest/Chart#setTooltipOffset)
    * [unselectSeries](https://nhn.github.io/tui.chart/latest/Chart#unselectSeries)
  * [ColumnChart](https://nhn.github.io/tui.chart/latest/ColumnChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/ColumnChart#addData)
    * [addSeries](https://nhn.github.io/tui.chart/latest/ColumnChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/ColumnChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/ColumnChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/ColumnChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/ColumnChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/ColumnChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/ColumnChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/ColumnChart#updateOptions)
  * [ColumnLineChart](https://nhn.github.io/tui.chart/latest/ColumnLineChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/ColumnLineChart#addData)
    * [addPlotBand](https://nhn.github.io/tui.chart/latest/ColumnLineChart#addPlotBand)
    * [addPlotLine](https://nhn.github.io/tui.chart/latest/ColumnLineChart#addPlotLine)
    * [addSeries](https://nhn.github.io/tui.chart/latest/ColumnLineChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/ColumnLineChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/ColumnLineChart#hideTooltip)
    * [removePlotBand](https://nhn.github.io/tui.chart/latest/ColumnLineChart#removePlotBand)
    * [removePlotLine](https://nhn.github.io/tui.chart/latest/ColumnLineChart#removePlotLine)
    * [setData](https://nhn.github.io/tui.chart/latest/ColumnLineChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/ColumnLineChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/ColumnLineChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/ColumnLineChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/ColumnLineChart#updateOptions)
  * [GaugeChart](https://nhn.github.io/tui.chart/latest/GaugeChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/GaugeChart#addData)
    * [addPlotBand](https://nhn.github.io/tui.chart/latest/GaugeChart#addPlotBand)
    * [addSeries](https://nhn.github.io/tui.chart/latest/GaugeChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/GaugeChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/GaugeChart#hideTooltip)
    * [removePlotBand](https://nhn.github.io/tui.chart/latest/GaugeChart#removePlotBand)
    * [setData](https://nhn.github.io/tui.chart/latest/GaugeChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/GaugeChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/GaugeChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/GaugeChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/GaugeChart#updateOptions)
  * [HeatmapChart](https://nhn.github.io/tui.chart/latest/HeatmapChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/HeatmapChart#addData)
    * [addSeries](https://nhn.github.io/tui.chart/latest/HeatmapChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/HeatmapChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/HeatmapChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/HeatmapChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/HeatmapChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/HeatmapChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/HeatmapChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/HeatmapChart#updateOptions)
  * [LineAreaChart](https://nhn.github.io/tui.chart/latest/LineAreaChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/LineAreaChart#addData)
    * [addPlotBand](https://nhn.github.io/tui.chart/latest/LineAreaChart#addPlotBand)
    * [addPlotLine](https://nhn.github.io/tui.chart/latest/LineAreaChart#addPlotLine)
    * [addSeries](https://nhn.github.io/tui.chart/latest/LineAreaChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/LineAreaChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/LineAreaChart#hideTooltip)
    * [removePlotBand](https://nhn.github.io/tui.chart/latest/LineAreaChart#removePlotBand)
    * [removePlotLine](https://nhn.github.io/tui.chart/latest/LineAreaChart#removePlotLine)
    * [setData](https://nhn.github.io/tui.chart/latest/LineAreaChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/LineAreaChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/LineAreaChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/LineAreaChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/LineAreaChart#updateOptions)
  * [LineChart](https://nhn.github.io/tui.chart/latest/LineChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/LineChart#addData)
    * [addPlotBand](https://nhn.github.io/tui.chart/latest/LineChart#addPlotBand)
    * [addPlotLine](https://nhn.github.io/tui.chart/latest/LineChart#addPlotLine)
    * [addSeries](https://nhn.github.io/tui.chart/latest/LineChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/LineChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/LineChart#hideTooltip)
    * [removePlotBand](https://nhn.github.io/tui.chart/latest/LineChart#removePlotBand)
    * [removePlotLine](https://nhn.github.io/tui.chart/latest/LineChart#removePlotLine)
    * [setData](https://nhn.github.io/tui.chart/latest/LineChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/LineChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/LineChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/LineChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/LineChart#updateOptions)
  * [LineScatterChart](https://nhn.github.io/tui.chart/latest/LineScatterChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/LineScatterChart#addData)
    * [addSeries](https://nhn.github.io/tui.chart/latest/LineScatterChart#addSeries)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/LineScatterChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/LineScatterChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/LineScatterChart#setOptions)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/LineScatterChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/LineScatterChart#updateOptions)
  * [NestedPieChart](https://nhn.github.io/tui.chart/latest/NestedPieChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addSeries](https://nhn.github.io/tui.chart/latest/NestedPieChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/NestedPieChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/NestedPieChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/NestedPieChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/NestedPieChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/NestedPieChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/NestedPieChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/NestedPieChart#updateOptions)
  * [PieChart](https://nhn.github.io/tui.chart/latest/PieChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addSeries](https://nhn.github.io/tui.chart/latest/PieChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/PieChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/PieChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/PieChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/PieChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/PieChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/PieChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/PieChart#updateOptions)
  * [RadarChart](https://nhn.github.io/tui.chart/latest/RadarChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/RadarChart#addData)
    * [addSeries](https://nhn.github.io/tui.chart/latest/RadarChart#addSeries)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/RadarChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/RadarChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/RadarChart#setOptions)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/RadarChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/RadarChart#updateOptions)
  * [RadialBarChart](https://nhn.github.io/tui.chart/latest/RadialBarChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addSeries](https://nhn.github.io/tui.chart/latest/RadialBarChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/RadialBarChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/RadialBarChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/RadialBarChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/RadialBarChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/RadialBarChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/RadialBarChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/RadialBarChart#updateOptions)
  * [ScatterChart](https://nhn.github.io/tui.chart/latest/ScatterChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addData](https://nhn.github.io/tui.chart/latest/ScatterChart#addData)
    * [addSeries](https://nhn.github.io/tui.chart/latest/ScatterChart#addSeries)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/ScatterChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/ScatterChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/ScatterChart#setOptions)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/ScatterChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/ScatterChart#updateOptions)
  * [TreemapChart](https://nhn.github.io/tui.chart/latest/TreemapChart)
### EXTENDS
    * [Chart](https://nhn.github.io/tui.chart/latest/Chart)
### INSTANCE METHODS
    * [addSeries](https://nhn.github.io/tui.chart/latest/TreemapChart#addSeries)
    * [hideSeriesDataLabel](https://nhn.github.io/tui.chart/latest/TreemapChart#hideSeriesDataLabel)
    * [hideTooltip](https://nhn.github.io/tui.chart/latest/TreemapChart#hideTooltip)
    * [setData](https://nhn.github.io/tui.chart/latest/TreemapChart#setData)
    * [setOptions](https://nhn.github.io/tui.chart/latest/TreemapChart#setOptions)
    * [showSeriesDataLabel](https://nhn.github.io/tui.chart/latest/TreemapChart#showSeriesDataLabel)
    * [showTooltip](https://nhn.github.io/tui.chart/latest/TreemapChart#showTooltip)
    * [updateOptions](https://nhn.github.io/tui.chart/latest/TreemapChart#updateOptions)


# ![Toast UI Chart](https://user-images.githubusercontent.com/35218826/37320160-c4d6dec4-26b5-11e8-9a91-79bb2b882410.png)
> 🍞📈 Spread your data on TOAST UI Chart. TOAST UI Chart is Beautiful Statistical Data Visualization library
[![npm](https://img.shields.io/npm/v/@toast-ui/chart.svg)](https://www.npmjs.com/package/@toast-ui/chart)
## 🚩 Table of Contents
  * [Toast UI Chart](https://nhn.github.io/tui.chart/latest/)
    * [🚩 Table of Contents](https://nhn.github.io/tui.chart/latest/#-table-of-contents)
    * [Collect statistics on the use of open source](https://nhn.github.io/tui.chart/latest/#collect-statistics-on-the-use-of-open-source)
    * [💾 Install](https://nhn.github.io/tui.chart/latest/#-install)
    * [Via Package Manager](https://nhn.github.io/tui.chart/latest/#via-package-manager)
      * [npm](https://nhn.github.io/tui.chart/latest/#npm)
    * [Via Contents Delivery Network (CDN)](https://nhn.github.io/tui.chart/latest/#via-contents-delivery-network-cdn)
    * [Download Source Files](https://nhn.github.io/tui.chart/latest/#download-source-files)
    * [🔨 Usage](https://nhn.github.io/tui.chart/latest/#-usage)
    * [HTML](https://nhn.github.io/tui.chart/latest/#html)
    * [JavaScript](https://nhn.github.io/tui.chart/latest/#javascript)
      * [Load](https://nhn.github.io/tui.chart/latest/#load)


## Collect statistics on the use of open source
TOAST UI Chart applies Google Analytics (GA) to collect statistics on the use of open source, in order to identify how widely TOAST UI Chart is used throughout the world. It also serves as important index to determine the future course of projects. location.hostname (e.g. > “ui.toast.com") is to be collected and the sole purpose is nothing but to measure statistics on the usage. To disable GA, use the following `usageStatistics` option when creating charts.
```
const options = {
  // ...
  usageStatistics: false,
};

toastui.Chart.barChart({ el, data, options });
```

## 💾 Install
The TOAST UI products can be installed by using the package manager or downloading the source directly. However, we highly recommend using the package manager.
### Via Package Manager
The TOAST UI products are registered in [npm](https://www.npmjs.com/) package manager. Install by using the commands provided by a package manager. When using npm, be sure [Node.js](https://nodejs.org) is installed in the environment.
#### npm
```
$ npm install --save @toast-ui/chart # Latest version
$ npm install --save @toast-ui/chart@<version> # Specific version
```

### Via Contents Delivery Network (CDN)
The TOAST UI Chart is available over a CDN.
  * You can use cdn as below.


```
<link rel="stylesheet" href="https://uicdn.toast.com/chart/latest/toastui-chart.min.css" />
<script src="https://uicdn.toast.com/chart/latest/toastui-chart.min.js"></script>
```

  * Within the download you'll find the following directories


```
- uicdn.toast.com/
  ├─ chart/
  │  ├─ latest
  │  │  ├─ toastui-chart.js
  │  │  ├─ toastui-chart.min.js
  │  │  ├─ toastui-chart.css
  │  │  ├─ toastui-chart.min.css
  │  ├─ v4.0.0/
```

### Download Source Files
  * [Download all sources for each version](https://github.com/nhn/tui.chart/releases)


## 🔨 Usage
### HTML
Add the container element where TOAST UI Chart will be created.
```
<div id="chart"></div>
```

### JavaScript
#### Load
The TOAST UI Chart can be used by creating an instance with the constructor function. To access the constructor function, import the module using one of the three following methods depending on your environment.
```
/* namespace */
const chart = toastui.Chart;

/* CommonJS in Node.js */
const chart = require('@toast-ui/chart');

/* ES6 in Node.js */
import Chart from '@toast-ui/chart';
import { LineChart } from '@toast-ui/chart';
```

[In Webpack 4, when importing package modules, the parts that are defined in the module field have higher priority than the parts defined in the main field](https://webpack.js.org/configuration/resolve/#resolvemainfields). To use the Webpack 4 with the `require` syntax to import `@toast-ui/chart`, the ESM file defined in the `module` field will be loaded, and the file will be transpiled to be compatible with the `require` syntax. In order to use the **bundle file for UMD** , the user must personally load and use the `@toast-ui/chart/dist/toastui-chart.js` or `@toast-ui/chart/dist/toastui-chart.min.js`.
```
const Chart = require('@toast-ui/chart/dist/toastui-chart.min.js'); // loading the bundle file for UMD
```

Webpack 5 supports the `exports` field. The entry point can be determined by the `exports` field defined in the package. Furthermore, the necessary chart can be loaded through a sub-path, as presented below.
```
const Chart = require('@toast-ui/chart');  // ./dist/toastui-chart.js

import { BarChart } from '@toast-ui/chart';  // ./dist/esm/index.js

import BarChart from '@toast-ui/chart/bar';
import ColumnChart from '@toast-ui/chart/column';
import LineChart from '@toast-ui/chart/line';
import AreaChart from '@toast-ui/chart/area';
import LineAreaChart from '@toast-ui/chart/lineArea';
import ColumnLineChart from '@toast-ui/chart/columnLine';
import BulletChart from '@toast-ui/chart/bullet';
import BoxPlotChart from '@toast-ui/chart/boxPlot';
import TreemapChart from '@toast-ui/chart/treemap';
import HeatmapChart from '@toast-ui/chart/heatmap';
import ScatterChart from '@toast-ui/chart/scatter';
import LineScatterChart from '@toast-ui/chart/lineScatter';
import BubbleChart from '@toast-ui/chart/bubble';
import PieChart from '@toast-ui/chart/pie';
import NestedPieChart from '@toast-ui/chart/nestedPie';
import RadarChart from '@toast-ui/chart/radar';
```

Constructor function needs three parameters: el, data, options
  * el: Wrapper HTML element that will contain the chart as a child.
  * data: Numerical data the chart will be based on.
  * options: Functional options including legend, alignment, and tooltip formatter.


```
const el = document.getElementById('chart');
const data = {
  categories: [
    //...
  ],
  series: [
    // ...
  ],
};
const options = {
  chart: { width: 700, height: 400 },
};

chart.barChart({ el, data, options });
// or
new BarChart({ el, data, options });
```

Refer to [details](https://nhn.github.io/tui.chart/latest) for additional information.
Resizable
[NHN Cloud](https://github.com/nhn)[FE Development Lab](https://ui.toast.com)

