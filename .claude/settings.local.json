{"permissions": {"allow": ["Bash(git add:*)", "Bash(npm install:*)", "Bash(npm start)", "Bash(npm test:*)", "<PERSON><PERSON>(python manage.py help:*)", "Bash(python3 manage.py help create_predefined_templates:*)", "Bash(python3 manage.py create_predefined_templates:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python manage.py:*)", "Ba<PERSON>(python3 manage.py makemigrations:*)", "Bash(python3 manage.py migrate:*)", "<PERSON><PERSON>(python3 manage.py showmigrations:*)", "Bash(rm:*)", "<PERSON><PERSON>(python3:*)", "Bash(docker compose logs:*)", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "<PERSON><PERSON>(source:*)", "mcp__ide__executeCode", "mcp__serena__find_file", "mcp__serena__activate_project", "Bash(PORT=3001 npm start)", "mcp__serena__replace_symbol_body", "mcp__serena__check_onboarding_performed", "mcp__serena__list_dir", "mcp__serena__get_symbols_overview", "Bash(npx tailwindcss init:*)", "Bash(./node_modules/.bin/tailwindcss:*)", "Bash(npm uninstall:*)", "mcp__ide__getDiagnostics", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(grep:*)", "<PERSON><PERSON>(timeout 30s npm start)", "<PERSON><PERSON>(true)", "Bash(npm ls:*)", "Bash(find:*)", "Bash(open http://localhost:3000)", "mcp__chrome-mcp__getConsoleLogs"], "deny": [], "defaultMode": "acceptEdits"}}