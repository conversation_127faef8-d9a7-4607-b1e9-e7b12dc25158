#!/usr/bin/env python
"""
Organization-Page 접근 권한 테스트 스크립트
"""
import os
import django
import sys

# Django 설정
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dynamic_bi_dashboard.settings')
django.setup()

from accounts.models import User, Organization
from dashboard.models import Page, PageOrganizationAccess

def test_organization_access():
    print("🧪 Organization-Page 접근 권한 테스트 시작\n")
    
    # 1. 테스트 데이터 생성
    print("1. 테스트 데이터 생성")
    
    # 조직 생성
    org1, created = Organization.objects.get_or_create(name="테스트 조직 A")
    org2, created = Organization.objects.get_or_create(name="테스트 조직 B")
    print(f"   ✅ 조직 생성: {org1.name}, {org2.name}")
    
    # 관리자 사용자 생성
    admin_user, created = User.objects.get_or_create(
        email="<EMAIL>",
        defaults={
            'username': 'admin',
            'role': 'super_admin',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    print(f"   ✅ 관리자 사용자: {admin_user.email}")
    
    # 일반 사용자들 생성
    user1, created = User.objects.get_or_create(
        email="<EMAIL>",
        defaults={
            'username': 'user1',
            'role': 'regular',
            'organization': org1
        }
    )
    user2, created = User.objects.get_or_create(
        email="<EMAIL>",
        defaults={
            'username': 'user2',
            'role': 'regular',
            'organization': org2
        }
    )
    print(f"   ✅ 일반 사용자: {user1.email} (조직: {org1.name})")
    print(f"   ✅ 일반 사용자: {user2.email} (조직: {org2.name})")
    
    # 페이지 생성
    page1, created = Page.objects.get_or_create(
        name="테스트 페이지 1",
        defaults={
            'permission_level': 'regular',
            'created_by': admin_user,
            'layout_config': {}
        }
    )
    page2, created = Page.objects.get_or_create(
        name="테스트 페이지 2", 
        defaults={
            'permission_level': 'org_admin',
            'created_by': admin_user,
            'layout_config': {}
        }
    )
    print(f"   ✅ 페이지: {page1.name} (권한: {page1.permission_level})")
    print(f"   ✅ 페이지: {page2.name} (권한: {page2.permission_level})")
    print()
    
    # 2. 권한 레벨 기반 접근 테스트
    print("2. 권한 레벨 기반 접근 테스트")
    print(f"   사용자1({user1.email}) -> 페이지1: {page1.user_has_access(user1)}")
    print(f"   사용자1({user1.email}) -> 페이지2: {page1.user_has_access(user1)}")
    print(f"   관리자({admin_user.email}) -> 페이지1: {page1.user_has_access(admin_user)}")
    print(f"   관리자({admin_user.email}) -> 페이지2: {page2.user_has_access(admin_user)}")
    print()
    
    # 3. 조직 기반 접근 권한 부여
    print("3. 조직 기반 접근 권한 부여")
    access1, created = PageOrganizationAccess.objects.get_or_create(
        organization=org1,
        page=page2,
        defaults={'granted_by': admin_user}
    )
    print(f"   ✅ 조직 '{org1.name}'에게 페이지 '{page2.name}' 접근 권한 부여")
    print()
    
    # 4. 조직 권한 기반 접근 테스트
    print("4. 조직 권한 기반 접근 테스트")
    print(f"   사용자1({user1.email}) -> 페이지2: {page2.user_has_access(user1)} (조직 권한)")
    print(f"   사용자2({user2.email}) -> 페이지2: {page2.user_has_access(user2)} (조직 권한 없음)")
    print()
    
    # 5. 접근 가능한 페이지 목록 테스트
    print("5. 사용자별 접근 가능한 페이지 목록")
    
    # 사용자1이 접근 가능한 페이지
    user1_pages = []
    for page in Page.objects.filter(is_active=True):
        if page.user_has_access(user1):
            user1_pages.append(page.name)
    print(f"   사용자1 접근 가능 페이지: {user1_pages}")
    
    # 사용자2가 접근 가능한 페이지
    user2_pages = []
    for page in Page.objects.filter(is_active=True):
        if page.user_has_access(user2):
            user2_pages.append(page.name)
    print(f"   사용자2 접근 가능 페이지: {user2_pages}")
    print()
    
    print("🎉 테스트 완료!")

if __name__ == '__main__':
    test_organization_access()