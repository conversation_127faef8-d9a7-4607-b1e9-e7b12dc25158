"""
Google OAuth2 인증 뷰
Google OAuth2 로그인 및 콜백 처리를 담당
"""

import logging
from django.conf import settings
from django.http import JsonResponse
from django.shortcuts import redirect
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from .oauth_services import GoogleOAuth2Service, GoogleOAuth2URLGenerator

logger = logging.getLogger(__name__)


class GoogleOAuth2LoginView(APIView):
    """
    Google OAuth2 로그인 시작점
    프론트엔드에서 호출하여 Google 인증 URL을 받아가는 API
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        """
        Google OAuth2 인증 URL 생성 및 반환
        
        Request Body:
            redirect_uri: 인증 후 리다이렉트될 URI
            state: CSRF 방지용 상태 값 (선택사항)
        
        Response:
            auth_url: Google OAuth2 인증 URL
        """
        try:
            redirect_uri = request.data.get('redirect_uri')
            if not redirect_uri:
                return Response({
                    'error': 'redirect_uri가 필요합니다.'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            state = request.data.get('state')
            
            # Google OAuth2 인증 URL 생성
            url_generator = GoogleOAuth2URLGenerator()
            auth_url = url_generator.generate_auth_url(redirect_uri, state)
            
            logger.info("Google OAuth2 인증 URL 생성 요청 처리 완료")
            
            return Response({
                'auth_url': auth_url,
                'message': 'Google 인증 URL이 생성되었습니다.'
            }, status=status.HTTP_200_OK)
            
        except ValueError as e:
            logger.error(f"Google OAuth2 설정 오류: {str(e)}")
            return Response({
                'error': 'Google OAuth2 설정이 올바르지 않습니다.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        except Exception as e:
            logger.error(f"Google OAuth2 URL 생성 오류: {str(e)}")
            return Response({
                'error': '인증 URL 생성 중 오류가 발생했습니다.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class GoogleOAuth2CallbackView(APIView):
    """
    Google OAuth2 콜백 처리
    Google에서 인증 완료 후 리다이렉트되는 엔드포인트
    """
    permission_classes = [AllowAny]
    
    def get(self, request):
        """
        Google OAuth2 콜백 처리 (GET 방식)
        Google에서 인증 코드와 함께 리다이렉트됨
        
        Query Parameters:
            code: Google 인증 코드
            state: CSRF 방지용 상태 값
            error: 인증 실패 시 에러 코드
        """
        try:
            # 에러 체크
            error = request.GET.get('error')
            if error:
                logger.warning(f"Google OAuth2 인증 거부: {error}")
                return self._redirect_with_error('사용자가 인증을 거부했습니다.')
            
            # 인증 코드 확인
            code = request.GET.get('code')
            if not code:
                logger.error("Google OAuth2 인증 코드가 없습니다.")
                return self._redirect_with_error('인증 코드가 없습니다.')
            
            # 리다이렉트 URI 구성 (현재 요청 URL 기반)
            redirect_uri = request.build_absolute_uri().split('?')[0]
            
            # Google OAuth2 인증 처리
            oauth_service = GoogleOAuth2Service()
            auth_result = oauth_service.authenticate_user(code, redirect_uri)
            
            logger.info(f"Google OAuth2 인증 성공: {auth_result['user']['email']}")
            
            # 프론트엔드로 리다이렉트 (토큰 정보 포함)
            return self._redirect_with_success(auth_result)
            
        except Exception as e:
            logger.error(f"Google OAuth2 콜백 처리 오류: {str(e)}")
            return self._redirect_with_error('인증 처리 중 오류가 발생했습니다.')
    
    def post(self, request):
        """
        Google OAuth2 콜백 처리 (POST 방식)
        프론트엔드에서 직접 인증 코드를 전송하는 경우
        
        Request Body:
            code: Google 인증 코드
            redirect_uri: 리다이렉트 URI
        """
        try:
            code = request.data.get('code')
            redirect_uri = request.data.get('redirect_uri')
            
            if not code:
                return Response({
                    'error': '인증 코드가 필요합니다.'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if not redirect_uri:
                return Response({
                    'error': 'redirect_uri가 필요합니다.'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Google OAuth2 인증 처리
            oauth_service = GoogleOAuth2Service()
            auth_result = oauth_service.authenticate_user(code, redirect_uri)
            
            logger.info(f"Google OAuth2 POST 인증 성공: {auth_result['user']['email']}")
            
            return Response({
                'message': 'Google 로그인이 완료되었습니다.',
                'access': auth_result['access'],
                'refresh': auth_result['refresh'],
                'user': auth_result['user']
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Google OAuth2 POST 콜백 처리 오류: {str(e)}")
            return Response({
                'error': '인증 처리 중 오류가 발생했습니다.',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _redirect_with_success(self, auth_result):
        """
        인증 성공 시 프론트엔드로 리다이렉트
        
        Args:
            auth_result: 인증 결과 (토큰 및 사용자 정보)
        """
        # 프론트엔드 URL 구성 (환경 변수에서 가져오거나 기본값 사용)
        frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
        
        # 토큰 정보를 URL 파라미터로 전달 (보안상 권장하지 않지만 간단한 구현을 위해)
        # 실제 프로덕션에서는 세션이나 다른 안전한 방법 사용 권장
        redirect_url = (
            f"{frontend_url}/#/auth/google/callback"
            f"?access={auth_result['access']}"
            f"&refresh={auth_result['refresh']}"
            f"&user_id={auth_result['user']['id']}"
            f"&email={auth_result['user']['email']}"
        )
        
        return redirect(redirect_url)
    
    def _redirect_with_error(self, error_message):
        """
        인증 실패 시 프론트엔드로 리다이렉트
        
        Args:
            error_message: 에러 메시지
        """
        frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
        redirect_url = f"{frontend_url}/#/auth/google/callback?error={error_message}"
        
        return redirect(redirect_url)


@api_view(['POST'])
@permission_classes([AllowAny])
def google_oauth2_login_api(request):
    """
    Google OAuth2 로그인 API (함수형 뷰)
    프론트엔드에서 직접 호출하는 간단한 API
    
    Request Body:
        code: Google 인증 코드
        redirect_uri: 리다이렉트 URI (선택사항)
    
    Response:
        access: JWT 액세스 토큰
        refresh: JWT 리프레시 토큰
        user: 사용자 정보
    """
    try:
        code = request.data.get('code')
        if not code:
            return Response({
                'error': '인증 코드가 필요합니다.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 기본 리다이렉트 URI 설정
        redirect_uri = request.data.get('redirect_uri', 
                                      f"{request.scheme}://{request.get_host()}/api/auth/google/callback/")
        
        # Google OAuth2 인증 처리
        oauth_service = GoogleOAuth2Service()
        auth_result = oauth_service.authenticate_user(code, redirect_uri)
        
        logger.info(f"Google OAuth2 API 인증 성공: {auth_result['user']['email']}")
        
        return Response({
            'message': 'Google 로그인이 완료되었습니다.',
            'access': auth_result['access'],
            'refresh': auth_result['refresh'],
            'user': auth_result['user']
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Google OAuth2 API 인증 오류: {str(e)}")
        return Response({
            'error': '인증 처리 중 오류가 발생했습니다.',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def google_oauth2_config_api(request):
    """
    Google OAuth2 설정 정보 API
    프론트엔드에서 Google OAuth2 설정 정보를 가져오는 API
    
    Response:
        client_id: Google OAuth2 클라이언트 ID
        redirect_uri: 기본 리다이렉트 URI
    """
    try:
        client_id = settings.GOOGLE_OAUTH2_CLIENT_ID
        if not client_id:
            return Response({
                'error': 'Google OAuth2가 설정되지 않았습니다.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        redirect_uri = f"{request.scheme}://{request.get_host()}/api/auth/google/callback/"
        
        return Response({
            'client_id': client_id,
            'redirect_uri': redirect_uri,
            'auth_url': 'https://accounts.google.com/o/oauth2/v2/auth',
            'scope': 'openid email profile'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Google OAuth2 설정 조회 오류: {str(e)}")
        return Response({
            'error': '설정 조회 중 오류가 발생했습니다.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)