"""
Authentication system tests
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


class AuthenticationTestCase(APITestCase):
    """인증 시스템 테스트"""
    
    def setUp(self):
        """테스트 데이터 설정"""
        self.register_url = reverse('accounts:register')
        self.login_url = reverse('accounts:login')
        self.logout_url = reverse('accounts:logout')
        self.profile_url = reverse('accounts:profile')
        self.token_refresh_url = reverse('accounts:token-refresh')
        self.token_verify_url = reverse('accounts:token-verify')
        
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpassword123',
            'password_confirm': 'testpassword123'
        }
        
        self.login_data = {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }
    
    def test_user_registration(self):
        """사용자 회원가입 테스트"""
        response = self.client.post(self.register_url, self.user_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('message', response.data)
        self.assertIn('user', response.data)
        self.assertEqual(response.data['user']['email'], self.user_data['email'])
    
    def test_user_registration_password_mismatch(self):
        """비밀번호 불일치 시 회원가입 실패 테스트"""
        data = self.user_data.copy()
        data['password_confirm'] = 'differentpassword'
        response = self.client.post(self.register_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password_confirm', response.data)
    
    def test_user_registration_weak_password(self):
        """약한 비밀번호로 회원가입 실패 테스트"""
        data = self.user_data.copy()
        data['password'] = '123'
        data['password_confirm'] = '123'
        response = self.client.post(self.register_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)
    
    def test_user_login(self):
        """사용자 로그인 테스트"""
        # 먼저 사용자 생성
        User.objects.create_user(
            email=self.login_data['email'],
            username='testuser',
            password=self.login_data['password']
        )
        
        response = self.client.post(self.login_url, self.login_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        self.assertIn('message', response.data)
        
        # Verify token field names are consistent
        self.assertIsInstance(response.data['access'], str)
        self.assertIsInstance(response.data['refresh'], str)
        self.assertTrue(len(response.data['access']) > 0)
        self.assertTrue(len(response.data['refresh']) > 0)
    
    def test_user_login_invalid_credentials(self):
        """잘못된 인증 정보로 로그인 실패 테스트"""
        response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_token_verification(self):
        """JWT 토큰 검증 테스트"""
        # 사용자 생성 및 토큰 발급
        user = User.objects.create_user(
            email=self.login_data['email'],
            username='testuser',
            password=self.login_data['password']
        )
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        # 토큰으로 인증된 요청
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = self.client.post(self.token_verify_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('user', response.data)
    
    def test_token_refresh(self):
        """JWT 토큰 갱신 테스트"""
        user = User.objects.create_user(
            email=self.login_data['email'],
            username='testuser',
            password=self.login_data['password']
        )
        
        refresh = RefreshToken.for_user(user)
        
        response = self.client.post(self.token_refresh_url, {
            'refresh': str(refresh)
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
    
    def test_user_logout(self):
        """사용자 로그아웃 테스트"""
        user = User.objects.create_user(
            email=self.login_data['email'],
            username='testuser',
            password=self.login_data['password']
        )
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        # 인증된 상태로 로그아웃 요청
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = self.client.post(self.logout_url, {
            'refresh': str(refresh)
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
    
    def test_protected_endpoint_without_token(self):
        """토큰 없이 보호된 엔드포인트 접근 테스트"""
        response = self.client.get(self.profile_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_user_profile_access(self):
        """사용자 프로필 접근 테스트"""
        user = User.objects.create_user(
            email=self.login_data['email'],
            username='testuser',
            password=self.login_data['password']
        )
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], user.email)
    
    def test_specific_user_login(self):
        """특정 사용자 로그인 테스트 (<EMAIL>)"""
        # 특정 사용자 생성
        specific_email = '<EMAIL>'
        specific_password = 'vosjf8152'
        
        User.objects.create_user(
            email=specific_email,
            username='kimjj81',
            password=specific_password
        )
        
        # 로그인 시도
        login_data = {
            'email': specific_email,
            'password': specific_password
        }
        
        response = self.client.post(self.login_url, login_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        self.assertEqual(response.data['user']['email'], specific_email)
    
    def test_token_field_consistency(self):
        """토큰 필드명 일관성 테스트"""
        # 사용자 생성
        User.objects.create_user(
            email=self.login_data['email'],
            username='testuser',
            password=self.login_data['password']
        )
        
        # 로그인 응답 확인
        response = self.client.post(self.login_url, self.login_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 필드명이 'access'와 'refresh'인지 확인 (access_token, refresh_token이 아님)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertNotIn('access_token', response.data)
        self.assertNotIn('refresh_token', response.data)
        
        # 토큰 갱신 테스트
        refresh_token = response.data['refresh']
        refresh_response = self.client.post(self.token_refresh_url, {
            'refresh': refresh_token
        })
        self.assertEqual(refresh_response.status_code, status.HTTP_200_OK)
        self.assertIn('access', refresh_response.data)
        self.assertNotIn('access_token', refresh_response.data)


class UserModelTestCase(TestCase):
    """사용자 모델 테스트"""
    
    def test_create_user(self):
        """일반 사용자 생성 테스트"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpassword123'
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, 'regular')
        self.assertTrue(user.is_regular_user())
        self.assertFalse(user.is_org_admin())
        self.assertFalse(user.is_super_admin())
        self.assertFalse(user.has_admin_permissions())
    
    def test_create_org_admin(self):
        """조직 관리자 생성 테스트"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='adminuser',
            password='testpassword123',
            role='org_admin'
        )
        
        self.assertEqual(user.role, 'org_admin')
        self.assertFalse(user.is_regular_user())
        self.assertTrue(user.is_org_admin())
        self.assertFalse(user.is_super_admin())
        self.assertTrue(user.has_admin_permissions())
    
    def test_create_super_admin(self):
        """슈퍼 관리자 생성 테스트"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='superuser',
            password='testpassword123',
            role='super_admin'
        )
        
        self.assertEqual(user.role, 'super_admin')
        self.assertFalse(user.is_regular_user())
        self.assertFalse(user.is_org_admin())
        self.assertTrue(user.is_super_admin())
        self.assertTrue(user.has_admin_permissions())