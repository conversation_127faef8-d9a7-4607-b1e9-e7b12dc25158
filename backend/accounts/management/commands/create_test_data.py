from django.core.management.base import BaseCommand
from accounts.models import User, Organization
from dashboard.models import Page, ComponentTemplate, PageComponent


class Command(BaseCommand):
    help = 'Create test data for development'

    def handle(self, *args, **options):
        # Create test organization
        org, created = Organization.objects.get_or_create(
            name="테스트 조직",
            defaults={'name': "테스트 조직"}
        )
        if created:
            self.stdout.write(f"Created organization: {org.name}")

        # Create test users
        users_data = [
            {
                'email': '<EMAIL>',
                'username': 'regular_user',
                'role': 'regular',
                'organization': org
            },
            {
                'email': '<EMAIL>',
                'username': 'org_admin',
                'role': 'org_admin',
                'organization': org
            },
            {
                'email': '<EMAIL>',
                'username': 'super_admin',
                'role': 'super_admin',
                'organization': None
            }
        ]

        for user_data in users_data:
            user, created = User.objects.get_or_create(
                email=user_data['email'],
                defaults=user_data
            )
            if created:
                user.set_password('testpass123')
                user.save()
                self.stdout.write(f"Created user: {user.email} ({user.role})")

        # Create test component templates
        templates_data = [
            {
                'name': '월별 매출 차트',
                'chart_type': 'bar',
                'api_endpoint': '/api/components/monthly-sales/data/',
                'api_params': {'aggregation': 'month'},
                'bigquery_sql': 'SELECT DATE_TRUNC(date, MONTH) as month, SUM(amount) as total FROM sales GROUP BY month ORDER BY month'
            },
            {
                'name': '일별 방문자 수',
                'chart_type': 'line',
                'api_endpoint': '/api/components/daily-visitors/data/',
                'api_params': {'aggregation': 'day'},
                'bigquery_sql': 'SELECT DATE(timestamp) as date, COUNT(DISTINCT user_id) as visitors FROM visits GROUP BY date ORDER BY date'
            }
        ]

        for template_data in templates_data:
            template, created = ComponentTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f"Created component template: {template.name}")

        # Create test pages
        admin_user = User.objects.filter(role='super_admin').first()
        if admin_user:
            pages_data = [
                {
                    'name': '매출 대시보드',
                    'permission_level': 'regular',
                    'layout_config': {'columns': 100, 'gap': '16px'},
                    'created_by': admin_user
                },
                {
                    'name': '관리자 대시보드',
                    'permission_level': 'org_admin',
                    'layout_config': {'columns': 100, 'gap': '20px'},
                    'created_by': admin_user
                }
            ]

            for page_data in pages_data:
                page, created = Page.objects.get_or_create(
                    name=page_data['name'],
                    defaults=page_data
                )
                if created:
                    self.stdout.write(f"Created page: {page.name}")

        self.stdout.write(self.style.SUCCESS('Test data created successfully!'))