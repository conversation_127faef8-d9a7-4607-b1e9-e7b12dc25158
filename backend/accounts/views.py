from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes, throttle_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework.throttling import AnonRateThrottle, UserRateThrottle
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from django.contrib.auth import authenticate, login, logout
from django.utils import timezone
from django.core.cache import cache
import logging
from .models import User, Organization
from .serializers import (
    UserSerializer, UserCreateSerializer, LoginSerializer, 
    OrganizationSerializer, TokenSerializer
)
from .permissions import (
    IsAdminUser, IsSuperAdminUser, IsOwnerOrAdmin, 
    IsSameOrganizationOrAdmin
)

logger = logging.getLogger(__name__)


class AuthRateThrottle(AnonRateThrottle):
    """인증 관련 API에 대한 특별한 rate limiting"""
    scope = 'auth'


class LoginRateThrottle(AnonRateThrottle):
    """로그인 시도에 대한 더 엄격한 rate limiting"""
    scope = 'login'


class OrganizationListView(generics.ListCreateAPIView):
    """조직 목록 조회 및 생성"""
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """생성은 관리자만 가능"""
        if self.request.method == 'POST':
            return [permissions.IsAuthenticated(), IsAdminUser()]
        return [permissions.IsAuthenticated()]


class OrganizationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """조직 상세 조회, 수정, 삭제"""
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]


class UserListView(generics.ListCreateAPIView):
    """사용자 목록 조회 및 생성"""
    queryset = User.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return UserCreateSerializer
        return UserSerializer

    def get_queryset(self):
        """권한에 따른 사용자 목록 필터링"""
        user = self.request.user
        if user.is_super_admin():
            return User.objects.all()
        elif user.is_org_admin() and user.organization:
            return User.objects.filter(organization=user.organization)
        else:
            return User.objects.filter(id=user.id)


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """사용자 상세 조회, 수정, 삭제"""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        """권한에 따른 접근 제어"""
        obj = super().get_object()
        user = self.request.user
        
        # 본인 정보는 항상 접근 가능
        if obj.id == user.id:
            return obj
        
        # 슈퍼 관리자는 모든 사용자 접근 가능
        if user.is_super_admin():
            return obj
        
        # 조직 관리자는 같은 조직 사용자만 접근 가능
        if user.is_org_admin() and user.organization == obj.organization:
            return obj
        
        # 권한이 없는 경우
        from rest_framework.exceptions import PermissionDenied
        raise PermissionDenied('이 사용자 정보에 접근할 권한이 없습니다.')


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@throttle_classes([AuthRateThrottle])
def register_view(request):
    """사용자 회원가입"""
    try:
        serializer = UserCreateSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            logger.info(f"New user registered: {user.email}")
            return Response({
                'message': '회원가입이 완료되었습니다.',
                'user': UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        
        logger.warning(f"Registration failed for email: {request.data.get('email', 'unknown')}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        return Response({
            'error': '회원가입 중 오류가 발생했습니다.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@throttle_classes([LoginRateThrottle])
def login_view(request):
    """사용자 로그인 (JWT 토큰 발급)"""
    email = request.data.get('email', '')
    
    # 로그인 시도 횟수 체크 (브루트 포스 공격 방지)
    cache_key = f"login_attempts_{email}"
    attempts = cache.get(cache_key, 0)
    
    if attempts >= 5:  # 5회 실패 시 15분 잠금
        return Response({
            'error': '로그인 시도 횟수를 초과했습니다. 15분 후 다시 시도해주세요.'
        }, status=status.HTTP_429_TOO_MANY_REQUESTS)
    
    try:
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # 성공 시 시도 횟수 초기화
            cache.delete(cache_key)
            
            # 마지막 로그인 시간 업데이트
            user.last_login = timezone.now()
            user.save(update_fields=['last_login'])
            
            # JWT 토큰 생성
            token_data = TokenSerializer.get_token_for_user(user)
            
            logger.info(f"User logged in: {user.email}")
            
            return Response({
                'message': '로그인되었습니다.',
                'access': token_data['access'],
                'refresh': token_data['refresh'],
                'user': token_data['user']
            }, status=status.HTTP_200_OK)
        
        # 실패 시 시도 횟수 증가
        cache.set(cache_key, attempts + 1, 900)  # 15분 동안 유지
        logger.warning(f"Login failed for email: {email}")
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    except Exception as e:
        logger.error(f"Login error for {email}: {str(e)}")
        return Response({
            'error': '로그인 중 오류가 발생했습니다.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """사용자 로그아웃 (토큰 블랙리스트 처리)"""
    try:
        refresh_token = request.data.get('refresh')
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()
        
        # 기존 토큰도 삭제 (호환성을 위해)
        try:
            request.user.auth_token.delete()
        except:
            pass
        
        logger.info(f"User logged out: {request.user.email}")
        
        return Response({
            'message': '로그아웃되었습니다.'
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Logout error for {request.user.email}: {str(e)}")
        return Response({
            'message': '로그아웃되었습니다.'
        }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile_view(request):
    """현재 사용자 프로필 조회"""
    serializer = UserSerializer(request.user)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def token_refresh_view(request):
    """JWT 토큰 갱신"""
    refresh_token = request.data.get('refresh')
    if not refresh_token:
        return Response({
            'error': 'refresh 토큰이 필요합니다.'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        refresh = RefreshToken(refresh_token)
        access_token = str(refresh.access_token)
        
        return Response({
            'access': access_token
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({
            'error': '유효하지 않은 refresh 토큰입니다.'
        }, status=status.HTTP_401_UNAUTHORIZED)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def token_verify_view(request):
    """JWT 토큰 검증"""
    return Response({
        'message': '유효한 토큰입니다.',
        'user': UserSerializer(request.user).data
    }, status=status.HTTP_200_OK)


@api_view(['PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_theme_view(request):
    """사용자 테마 설정 업데이트"""
    try:
        ui_theme = request.data.get('ui_theme')
        if not ui_theme:
            return Response({
                'error': 'ui_theme 필드가 필요합니다.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 유효한 테마인지 확인
        from .models import UI_THEMES
        valid_themes = [theme[0] for theme in UI_THEMES]
        if ui_theme not in valid_themes:
            return Response({
                'error': f'유효하지 않은 테마입니다. 사용 가능한 테마: {valid_themes}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 사용자 테마 업데이트
        user = request.user
        user.ui_theme = ui_theme
        user.save(update_fields=['ui_theme'])
        
        logger.info(f"User {user.email} updated theme to: {ui_theme}")
        
        return Response({
            'message': '테마가 성공적으로 업데이트되었습니다.',
            'user': UserSerializer(user).data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Theme update error for {request.user.email}: {str(e)}")
        return Response({
            'error': '테마 업데이트 중 오류가 발생했습니다.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


