# Authentication API Documentation

## Overview

이 문서는 동적 BI 대시보드 앱의 인증 API 엔드포인트에 대한 설명입니다.

## Base URL

```
http://localhost:8000/api/auth/
```

## Authentication

JWT (JSON Web Token) 기반 인증을 사용합니다.

### Headers

인증이 필요한 엔드포인트에는 다음 헤더를 포함해야 합니다:

```
Authorization: Bearer <access_token>
```

## Endpoints

### 1. 회원가입

**POST** `/register/`

새로운 사용자 계정을 생성합니다.

#### Request Body

```json
{
  "email": "<EMAIL>",
  "username": "username",
  "first_name": "First",
  "last_name": "Last",
  "password": "securepassword123",
  "password_confirm": "securepassword123",
  "organization_id": 1  // optional
}
```

#### Response (201 Created)

```json
{
  "message": "회원가입이 완료되었습니다.",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "First",
    "last_name": "Last",
    "role": "regular",
    "organization": null,
    "is_active": true,
    "created_at": "2025-08-08T20:35:07.728535+09:00"
  }
}
```

#### Error Response (400 Bad Request)

```json
{
  "password": ["비밀번호가 일치하지 않습니다."],
  "email": ["사용자의 이메일은/는 이미 존재합니다."]
}
```

### 2. 로그인

**POST** `/login/`

사용자 인증 후 JWT 토큰을 발급합니다.

#### Request Body

```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

#### Response (200 OK)

```json
{
  "message": "로그인되었습니다.",
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "First",
    "last_name": "Last",
    "role": "regular",
    "organization": null,
    "is_active": true,
    "created_at": "2025-08-08T20:35:07.728535+09:00"
  }
}
```

#### Error Response (400 Bad Request)

```json
{
  "non_field_errors": ["이메일 또는 비밀번호가 올바르지 않습니다."]
}
```

#### Rate Limiting

- 로그인 시도는 시간당 10회로 제한됩니다.
- 동일 이메일로 5회 연속 실패 시 15분간 잠금됩니다.

### 3. 로그아웃

**POST** `/logout/`

현재 사용자를 로그아웃하고 토큰을 블랙리스트에 추가합니다.

**Authentication Required**

#### Request Body

```json
{
  "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Response (200 OK)

```json
{
  "message": "로그아웃되었습니다."
}
```

### 4. 토큰 갱신

**POST** `/token/refresh/`

Refresh 토큰을 사용하여 새로운 Access 토큰을 발급받습니다.

#### Request Body

```json
{
  "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Response (200 OK)

```json
{
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Error Response (401 Unauthorized)

```json
{
  "error": "유효하지 않은 refresh 토큰입니다."
}
```

### 5. 토큰 검증

**POST** `/token/verify/`

현재 Access 토큰의 유효성을 검증합니다.

**Authentication Required**

#### Response (200 OK)

```json
{
  "message": "유효한 토큰입니다.",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "First",
    "last_name": "Last",
    "role": "regular",
    "organization": null,
    "is_active": true,
    "created_at": "2025-08-08T20:35:07.728535+09:00"
  }
}
```

### 6. 사용자 프로필

**GET** `/profile/`

현재 인증된 사용자의 프로필 정보를 조회합니다.

**Authentication Required**

#### Response (200 OK)

```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "username",
  "first_name": "First",
  "last_name": "Last",
  "role": "regular",
  "organization": null,
  "is_active": true,
  "created_at": "2025-08-08T20:35:07.728535+09:00"
}
```

## User Roles

시스템에서 지원하는 사용자 역할:

- `regular`: 일반 사용자 (기본값)
- `org_admin`: 조직 관리자
- `super_admin`: 슈퍼 관리자

## Password Requirements

비밀번호는 다음 조건을 만족해야 합니다:

- 최소 8자 이상
- 사용자 정보와 유사하지 않아야 함
- 일반적인 비밀번호가 아니어야 함
- 숫자로만 구성되면 안됨

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | 성공 |
| 201 | 생성됨 |
| 400 | 잘못된 요청 |
| 401 | 인증 필요 |
| 403 | 권한 없음 |
| 429 | 요청 횟수 초과 |
| 500 | 서버 오류 |

## Rate Limiting

| Endpoint | Limit |
|----------|-------|
| 인증 관련 API | 20/hour |
| 로그인 | 10/hour |
| 기타 API | 100/hour (익명), 1000/hour (인증됨) |

## Security Features

1. **JWT 토큰 기반 인증**
   - Access 토큰: 1시간 유효
   - Refresh 토큰: 7일 유효
   - 토큰 블랙리스트 지원

2. **브루트 포스 공격 방지**
   - 로그인 시도 횟수 제한
   - IP 기반 rate limiting

3. **비밀번호 보안**
   - Django 기본 비밀번호 검증
   - 해시화된 비밀번호 저장

4. **로깅 및 모니터링**
   - 모든 인증 시도 로깅
   - 보안 이벤트 추적
## Go
ogle OAuth2 Authentication

Google OAuth2를 통한 소셜 로그인 기능을 제공합니다.

### 7. Google OAuth2 인증 URL 생성

**POST** `/google/`

Google OAuth2 인증을 위한 URL을 생성합니다.

#### Request Body

```json
{
  "redirect_uri": "http://localhost:8000/api/auth/google/callback/",
  "state": "optional_csrf_token"  // optional
}
```

#### Response (200 OK)

```json
{
  "auth_url": "https://accounts.google.com/o/oauth2/v2/auth?client_id=...",
  "message": "Google 인증 URL이 생성되었습니다."
}
```

#### Error Response (400 Bad Request)

```json
{
  "error": "redirect_uri가 필요합니다."
}
```

### 8. Google OAuth2 콜백 처리

**POST** `/google/callback/`

Google에서 받은 인증 코드를 처리하여 JWT 토큰을 발급합니다.

#### Request Body

```json
{
  "code": "4/0AX4XfWh...",  // Google authorization code
  "redirect_uri": "http://localhost:8000/api/auth/google/callback/"
}
```

#### Response (200 OK)

```json
{
  "message": "Google 로그인이 완료되었습니다.",
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "role": "regular",
    "organization": null,
    "is_active": true,
    "created_at": "2025-08-08T20:35:07.728535+09:00"
  }
}
```

#### Error Response (400 Bad Request)

```json
{
  "error": "인증 코드가 필요합니다."
}
```

#### Error Response (500 Internal Server Error)

```json
{
  "error": "인증 처리 중 오류가 발생했습니다.",
  "detail": "토큰 교환 실패: invalid_grant"
}
```

### 9. Google OAuth2 간편 로그인

**POST** `/google/login/`

Google 인증 코드로 직접 로그인합니다. (콜백 처리와 동일한 기능)

#### Request Body

```json
{
  "code": "4/0AX4XfWh...",  // Google authorization code
  "redirect_uri": "http://localhost:8000/api/auth/google/callback/"  // optional
}
```

#### Response

콜백 처리와 동일한 응답 형식

### 10. Google OAuth2 설정 정보

**GET** `/google/config/`

프론트엔드에서 사용할 Google OAuth2 설정 정보를 조회합니다.

#### Response (200 OK)

```json
{
  "client_id": "*********-abcdefghijklmnop.apps.googleusercontent.com",
  "redirect_uri": "http://localhost:8000/api/auth/google/callback/",
  "auth_url": "https://accounts.google.com/o/oauth2/v2/auth",
  "scope": "openid email profile"
}
```

#### Error Response (500 Internal Server Error)

```json
{
  "error": "Google OAuth2가 설정되지 않았습니다."
}
```

## Google OAuth2 Flow

### 전체 인증 플로우

1. **설정 정보 조회**: `GET /api/auth/google/config/`
2. **Google 인증 페이지로 리다이렉트**: 사용자가 Google 계정으로 로그인
3. **인증 코드 수신**: Google에서 콜백 URL로 인증 코드 전송
4. **토큰 교환**: `POST /api/auth/google/callback/` 또는 `POST /api/auth/google/login/`
5. **JWT 토큰 수신**: 앱에서 사용할 JWT 토큰 발급

### 프론트엔드 구현 예시

```javascript
// 1. Google OAuth2 설정 정보 가져오기
const getGoogleConfig = async () => {
  const response = await fetch('/api/auth/google/config/');
  return await response.json();
};

// 2. Google 인증 URL 생성 및 리다이렉트
const initiateGoogleLogin = async () => {
  const config = await getGoogleConfig();
  
  const authUrl = `${config.auth_url}?` +
    `client_id=${config.client_id}&` +
    `redirect_uri=${encodeURIComponent(config.redirect_uri)}&` +
    `response_type=code&` +
    `scope=${encodeURIComponent(config.scope)}&` +
    `access_type=offline&` +
    `prompt=consent`;
  
  window.location.href = authUrl;
};

// 3. 콜백에서 인증 코드 처리
const handleGoogleCallback = async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const error = urlParams.get('error');
  
  if (error) {
    console.error('Google OAuth2 error:', error);
    return;
  }
  
  if (code) {
    try {
      const response = await fetch('/api/auth/google/login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code }),
      });
      
      const result = await response.json();
      
      if (response.ok) {
        // JWT 토큰 저장
        localStorage.setItem('access_token', result.access);
        localStorage.setItem('refresh_token', result.refresh);
        
        // 사용자 정보 저장
        localStorage.setItem('user', JSON.stringify(result.user));
        
        // 대시보드로 리다이렉트
        window.location.href = '/dashboard';
      } else {
        console.error('Login failed:', result.error);
      }
    } catch (error) {
      console.error('Network error:', error);
    }
  }
};
```

### React 컴포넌트 예시

```jsx
import React from 'react';

const GoogleLoginButton = ({ onSuccess, onError }) => {
  const handleGoogleLogin = async () => {
    try {
      const response = await fetch('/api/auth/google/config/');
      const config = await response.json();
      
      if (!response.ok) {
        throw new Error(config.error);
      }
      
      const authUrl = `${config.auth_url}?` +
        `client_id=${config.client_id}&` +
        `redirect_uri=${encodeURIComponent(config.redirect_uri)}&` +
        `response_type=code&` +
        `scope=${encodeURIComponent(config.scope)}&` +
        `access_type=offline&` +
        `prompt=consent`;
      
      window.location.href = authUrl;
    } catch (error) {
      onError?.(error.message);
    }
  };

  return (
    <button 
      onClick={handleGoogleLogin}
      className="google-login-btn"
      style={{
        backgroundColor: '#4285f4',
        color: 'white',
        border: 'none',
        padding: '10px 20px',
        borderRadius: '4px',
        cursor: 'pointer'
      }}
    >
      <svg width="18" height="18" viewBox="0 0 18 18" style={{ marginRight: '8px' }}>
        {/* Google 로고 SVG */}
      </svg>
      Google로 로그인
    </button>
  );
};

export default GoogleLoginButton;
```

## Google OAuth2 사용자 생성 규칙

### 새 사용자 생성

Google OAuth2로 처음 로그인하는 사용자는 자동으로 계정이 생성됩니다:

- **이메일**: Google 계정 이메일
- **사용자명**: 이메일과 동일
- **이름**: Google 프로필의 given_name, family_name
- **역할**: `regular` (기본값)
- **조직**: `null` (기본값)
- **활성 상태**: `true`

### 기존 사용자 업데이트

이미 존재하는 이메일로 Google OAuth2 로그인 시:

- 기존 사용자 정보 유지
- 이름 정보가 비어있는 경우에만 Google 프로필 정보로 업데이트
- 마지막 로그인 시간 업데이트

## Environment Variables

Google OAuth2 기능을 사용하려면 다음 환경 변수를 설정해야 합니다:

```bash
# .env 파일
GOOGLE_OAUTH2_CLIENT_ID=your_google_client_id_here
GOOGLE_OAUTH2_CLIENT_SECRET=your_google_client_secret_here
FRONTEND_URL=http://localhost:3000
```

## Google Cloud Console 설정

1. **프로젝트 생성**: Google Cloud Console에서 새 프로젝트 생성
2. **OAuth 동의 화면 설정**: 앱 정보 및 스코프 설정
3. **OAuth 2.0 클라이언트 ID 생성**: 웹 애플리케이션 타입으로 생성
4. **승인된 리디렉션 URI 추가**:
   - 개발: `http://localhost:8000/api/auth/google/callback/`
   - 프로덕션: `https://yourdomain.com/api/auth/google/callback/`

## Security Considerations

1. **클라이언트 시크릿 보안**: 서버에서만 사용, 프론트엔드에 노출 금지
2. **리다이렉트 URI 검증**: Google Console에 등록된 URI만 사용
3. **State 파라미터**: CSRF 공격 방지를 위해 사용 권장
4. **HTTPS 사용**: 프로덕션 환경에서는 반드시 HTTPS 사용
5. **토큰 보안**: JWT 토큰은 안전한 저장소에 보관