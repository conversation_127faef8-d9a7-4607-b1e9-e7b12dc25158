"""
Google OAuth2 서비스 모듈
Google OAuth2 인증 처리 및 사용자 정보 동기화를 담당
"""

import requests
import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from .models import Organization

logger = logging.getLogger(__name__)
User = get_user_model()


class GoogleOAuth2Service:
    """Google OAuth2 인증 서비스"""
    
    GOOGLE_TOKEN_URL = 'https://oauth2.googleapis.com/token'
    GOOGLE_USER_INFO_URL = 'https://www.googleapis.com/oauth2/v2/userinfo'
    
    def __init__(self):
        self.client_id = settings.GOOGLE_OAUTH2_CLIENT_ID
        self.client_secret = settings.GOOGLE_OAUTH2_CLIENT_SECRET
        
        if not self.client_id or not self.client_secret:
            raise ValueError("Google OAuth2 클라이언트 정보가 설정되지 않았습니다.")
    
    def exchange_code_for_token(self, code: str, redirect_uri: str) -> dict:
        """
        인증 코드를 액세스 토큰으로 교환
        
        Args:
            code: Google에서 받은 인증 코드
            redirect_uri: 리다이렉트 URI
            
        Returns:
            dict: 토큰 정보 (access_token, refresh_token 등)
            
        Raises:
            Exception: 토큰 교환 실패 시
        """
        try:
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'code': code,
                'grant_type': 'authorization_code',
                'redirect_uri': redirect_uri,
            }
            
            response = requests.post(self.GOOGLE_TOKEN_URL, data=data)
            response.raise_for_status()
            
            token_data = response.json()
            logger.info("Google OAuth2 토큰 교환 성공")
            
            return token_data
            
        except requests.RequestException as e:
            logger.error(f"Google OAuth2 토큰 교환 실패: {str(e)}")
            raise Exception(f"토큰 교환 실패: {str(e)}")
    
    def get_user_info(self, access_token: str) -> dict:
        """
        액세스 토큰으로 사용자 정보 조회
        
        Args:
            access_token: Google 액세스 토큰
            
        Returns:
            dict: 사용자 정보 (email, name, picture 등)
            
        Raises:
            Exception: 사용자 정보 조회 실패 시
        """
        try:
            headers = {'Authorization': f'Bearer {access_token}'}
            response = requests.get(self.GOOGLE_USER_INFO_URL, headers=headers)
            response.raise_for_status()
            
            user_info = response.json()
            logger.info(f"Google 사용자 정보 조회 성공: {user_info.get('email')}")
            
            return user_info
            
        except requests.RequestException as e:
            logger.error(f"Google 사용자 정보 조회 실패: {str(e)}")
            raise Exception(f"사용자 정보 조회 실패: {str(e)}")
    
    def create_or_update_user(self, user_info: dict) -> User:
        """
        Google 사용자 정보로 사용자 생성 또는 업데이트
        
        Args:
            user_info: Google에서 받은 사용자 정보
            
        Returns:
            User: 생성되거나 업데이트된 사용자 객체
        """
        email = user_info.get('email')
        if not email:
            raise ValueError("이메일 정보가 없습니다.")
        
        # 기존 사용자 확인
        try:
            user = User.objects.get(email=email)
            logger.info(f"기존 사용자 로그인: {email}")
            
            # 사용자 정보 업데이트 (필요시)
            updated = False
            if user_info.get('name') and not user.first_name:
                user.first_name = user_info.get('given_name', '')
                user.last_name = user_info.get('family_name', '')
                updated = True
            
            if updated:
                user.save()
                logger.info(f"사용자 정보 업데이트: {email}")
            
            return user
            
        except User.DoesNotExist:
            # 새 사용자 생성
            user_data = {
                'email': email,
                'username': email,  # 이메일을 username으로 사용
                'first_name': user_info.get('given_name', ''),
                'last_name': user_info.get('family_name', ''),
                'is_active': True,
                'role': 'regular',  # 기본 역할은 일반 사용자
            }
            
            user = User.objects.create_user(**user_data)
            logger.info(f"새 사용자 생성: {email}")
            
            return user
    
    def authenticate_user(self, code: str, redirect_uri: str) -> dict:
        """
        Google OAuth2 인증 전체 프로세스 처리
        
        Args:
            code: Google 인증 코드
            redirect_uri: 리다이렉트 URI
            
        Returns:
            dict: JWT 토큰과 사용자 정보
        """
        try:
            # 1. 인증 코드를 액세스 토큰으로 교환
            token_data = self.exchange_code_for_token(code, redirect_uri)
            access_token = token_data.get('access_token')
            
            if not access_token:
                raise Exception("액세스 토큰을 받지 못했습니다.")
            
            # 2. 액세스 토큰으로 사용자 정보 조회
            user_info = self.get_user_info(access_token)
            
            # 3. 사용자 생성 또는 업데이트
            user = self.create_or_update_user(user_info)
            
            # 4. JWT 토큰 생성
            refresh = RefreshToken.for_user(user)
            
            return {
                'access': str(refresh.access_token),
                'refresh': str(refresh),
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'role': user.role,
                    'organization': user.organization.name if user.organization else None,
                }
            }
            
        except Exception as e:
            logger.error(f"Google OAuth2 인증 실패: {str(e)}")
            raise e


class GoogleOAuth2URLGenerator:
    """Google OAuth2 인증 URL 생성기"""
    
    GOOGLE_AUTH_URL = 'https://accounts.google.com/o/oauth2/v2/auth'
    
    def __init__(self):
        self.client_id = settings.GOOGLE_OAUTH2_CLIENT_ID
        if not self.client_id:
            raise ValueError("Google OAuth2 클라이언트 ID가 설정되지 않았습니다.")
    
    def generate_auth_url(self, redirect_uri: str, state: str = None) -> str:
        """
        Google OAuth2 인증 URL 생성
        
        Args:
            redirect_uri: 인증 후 리다이렉트될 URI
            state: CSRF 방지를 위한 상태 값 (선택사항)
            
        Returns:
            str: Google OAuth2 인증 URL
        """
        params = {
            'client_id': self.client_id,
            'redirect_uri': redirect_uri,
            'response_type': 'code',
            'scope': 'openid email profile',
            'access_type': 'offline',
            'prompt': 'consent',
        }
        
        if state:
            params['state'] = state
        
        # URL 파라미터 생성
        param_string = '&'.join([f'{key}={value}' for key, value in params.items()])
        auth_url = f'{self.GOOGLE_AUTH_URL}?{param_string}'
        
        logger.info("Google OAuth2 인증 URL 생성 완료")
        return auth_url