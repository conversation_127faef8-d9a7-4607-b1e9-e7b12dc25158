"""
Google OAuth2 기능 테스트
"""

import json
from unittest.mock import patch, Mock
from django.test import TestCase, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from .oauth_services import GoogleOAuth2Service, GoogleOAuth2URLGenerator
from .models import Organization

User = get_user_model()


class GoogleOAuth2ServiceTest(TestCase):
    """Google OAuth2 서비스 테스트"""
    
    @override_settings(
        GOOGLE_OAUTH2_CLIENT_ID='test_client_id',
        GOOGLE_OAUTH2_CLIENT_SECRET='test_client_secret'
    )
    def setUp(self):
        self.oauth_service = GoogleOAuth2Service()
        self.test_user_info = {
            'email': '<EMAIL>',
            'given_name': 'Test',
            'family_name': 'User',
            'name': 'Test User',
            'picture': 'https://example.com/photo.jpg'
        }
    
    def test_service_initialization_success(self):
        """OAuth2 서비스 초기화 성공 테스트"""
        self.assertEqual(self.oauth_service.client_id, 'test_client_id')
        self.assertEqual(self.oauth_service.client_secret, 'test_client_secret')
    
    @override_settings(GOOGLE_OAUTH2_CLIENT_ID='', GOOGLE_OAUTH2_CLIENT_SECRET='')
    def test_service_initialization_failure(self):
        """OAuth2 서비스 초기화 실패 테스트"""
        with self.assertRaises(ValueError):
            GoogleOAuth2Service()
    
    @patch('accounts.oauth_services.requests.post')
    def test_exchange_code_for_token_success(self, mock_post):
        """인증 코드를 토큰으로 교환 성공 테스트"""
        # Mock 응답 설정
        mock_response = Mock()
        mock_response.json.return_value = {
            'access_token': 'test_access_token',
            'refresh_token': 'test_refresh_token',
            'expires_in': 3600
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # 테스트 실행
        result = self.oauth_service.exchange_code_for_token('test_code', 'http://localhost:8000/callback/')
        
        # 검증
        self.assertEqual(result['access_token'], 'test_access_token')
        self.assertEqual(result['refresh_token'], 'test_refresh_token')
        mock_post.assert_called_once()
    
    @patch('accounts.oauth_services.requests.post')
    def test_exchange_code_for_token_failure(self, mock_post):
        """인증 코드를 토큰으로 교환 실패 테스트"""
        # Mock 응답 설정 (실패)
        mock_post.side_effect = Exception('Network error')
        
        # 테스트 실행 및 검증
        with self.assertRaises(Exception):
            self.oauth_service.exchange_code_for_token('invalid_code', 'http://localhost:8000/callback/')
    
    @patch('accounts.oauth_services.requests.get')
    def test_get_user_info_success(self, mock_get):
        """사용자 정보 조회 성공 테스트"""
        # Mock 응답 설정
        mock_response = Mock()
        mock_response.json.return_value = self.test_user_info
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # 테스트 실행
        result = self.oauth_service.get_user_info('test_access_token')
        
        # 검증
        self.assertEqual(result['email'], '<EMAIL>')
        self.assertEqual(result['given_name'], 'Test')
        mock_get.assert_called_once()
    
    def test_create_new_user(self):
        """새 사용자 생성 테스트"""
        # 테스트 실행
        user = self.oauth_service.create_or_update_user(self.test_user_info)
        
        # 검증
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
        self.assertEqual(user.role, 'regular')
        self.assertTrue(user.is_active)
    
    def test_update_existing_user(self):
        """기존 사용자 업데이트 테스트"""
        # 기존 사용자 생성
        existing_user = User.objects.create_user(
            email='<EMAIL>',
            username='<EMAIL>',
            first_name='',
            last_name=''
        )
        
        # 테스트 실행
        user = self.oauth_service.create_or_update_user(self.test_user_info)
        
        # 검증
        self.assertEqual(user.id, existing_user.id)
        self.assertEqual(user.first_name, 'Test')
        self.assertEqual(user.last_name, 'User')
    
    def test_create_user_without_email(self):
        """이메일 없는 사용자 정보로 생성 시도 테스트"""
        user_info_without_email = {
            'given_name': 'Test',
            'family_name': 'User'
        }
        
        with self.assertRaises(ValueError):
            self.oauth_service.create_or_update_user(user_info_without_email)


class GoogleOAuth2URLGeneratorTest(TestCase):
    """Google OAuth2 URL 생성기 테스트"""
    
    @override_settings(GOOGLE_OAUTH2_CLIENT_ID='test_client_id')
    def setUp(self):
        self.url_generator = GoogleOAuth2URLGenerator()
    
    def test_generate_auth_url_basic(self):
        """기본 인증 URL 생성 테스트"""
        redirect_uri = 'http://localhost:8000/callback/'
        auth_url = self.url_generator.generate_auth_url(redirect_uri)
        
        # URL 구성 요소 검증
        self.assertIn('accounts.google.com/o/oauth2/v2/auth', auth_url)
        self.assertIn('client_id=test_client_id', auth_url)
        self.assertIn('redirect_uri=http://localhost:8000/callback/', auth_url)
        self.assertIn('response_type=code', auth_url)
        self.assertIn('scope=openid email profile', auth_url)
    
    def test_generate_auth_url_with_state(self):
        """상태 값이 포함된 인증 URL 생성 테스트"""
        redirect_uri = 'http://localhost:8000/callback/'
        state = 'test_state_value'
        auth_url = self.url_generator.generate_auth_url(redirect_uri, state)
        
        self.assertIn(f'state={state}', auth_url)
    
    @override_settings(GOOGLE_OAUTH2_CLIENT_ID='')
    def test_generate_auth_url_without_client_id(self):
        """클라이언트 ID 없이 URL 생성 시도 테스트"""
        with self.assertRaises(ValueError):
            GoogleOAuth2URLGenerator()


@override_settings(
    GOOGLE_OAUTH2_CLIENT_ID='test_client_id',
    GOOGLE_OAUTH2_CLIENT_SECRET='test_client_secret'
)
class GoogleOAuth2ViewsTest(APITestCase):
    """Google OAuth2 뷰 테스트"""
    
    def setUp(self):
        self.login_url = reverse('accounts:google-oauth2-login')
        self.callback_url = reverse('accounts:google-oauth2-callback')
        self.login_api_url = reverse('accounts:google-oauth2-login-api')
        self.config_url = reverse('accounts:google-oauth2-config')
    
    def test_google_oauth2_login_view_success(self):
        """Google OAuth2 로그인 뷰 성공 테스트"""
        data = {
            'redirect_uri': 'http://localhost:8000/callback/',
            'state': 'test_state'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('auth_url', response.data)
        self.assertIn('accounts.google.com', response.data['auth_url'])
    
    def test_google_oauth2_login_view_missing_redirect_uri(self):
        """리다이렉트 URI 누락 시 테스트"""
        data = {}
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    @patch('accounts.oauth_views.GoogleOAuth2Service.authenticate_user')
    def test_google_oauth2_callback_post_success(self, mock_authenticate):
        """Google OAuth2 콜백 POST 성공 테스트"""
        # Mock 설정
        mock_authenticate.return_value = {
            'access': 'test_access_token',
            'refresh': 'test_refresh_token',
            'user': {
                'id': 1,
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User',
                'role': 'regular',
                'organization': None
            }
        }
        
        data = {
            'code': 'test_auth_code',
            'redirect_uri': 'http://localhost:8000/callback/'
        }
        
        response = self.client.post(self.callback_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
    
    def test_google_oauth2_callback_post_missing_code(self):
        """인증 코드 누락 시 테스트"""
        data = {
            'redirect_uri': 'http://localhost:8000/callback/'
        }
        
        response = self.client.post(self.callback_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    @patch('accounts.oauth_views.GoogleOAuth2Service.authenticate_user')
    def test_google_oauth2_login_api_success(self, mock_authenticate):
        """Google OAuth2 로그인 API 성공 테스트"""
        # Mock 설정
        mock_authenticate.return_value = {
            'access': 'test_access_token',
            'refresh': 'test_refresh_token',
            'user': {
                'id': 1,
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User',
                'role': 'regular',
                'organization': None
            }
        }
        
        data = {
            'code': 'test_auth_code'
        }
        
        response = self.client.post(self.login_api_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
    
    def test_google_oauth2_config_api(self):
        """Google OAuth2 설정 API 테스트"""
        response = self.client.get(self.config_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('client_id', response.data)
        self.assertIn('redirect_uri', response.data)
        self.assertIn('auth_url', response.data)
        self.assertIn('scope', response.data)
    
    @override_settings(GOOGLE_OAUTH2_CLIENT_ID='')
    def test_google_oauth2_config_api_not_configured(self):
        """Google OAuth2 미설정 시 설정 API 테스트"""
        response = self.client.get(self.config_url)
        
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn('error', response.data)


@override_settings(
    GOOGLE_OAUTH2_CLIENT_ID='test_client_id',
    GOOGLE_OAUTH2_CLIENT_SECRET='test_client_secret'
)
class GoogleOAuth2IntegrationTest(APITestCase):
    """Google OAuth2 통합 테스트"""
    
    @patch('accounts.oauth_services.requests.post')
    @patch('accounts.oauth_services.requests.get')
    def test_full_oauth2_flow(self, mock_get, mock_post):
        """전체 OAuth2 플로우 통합 테스트"""
        # Mock 토큰 교환 응답
        mock_token_response = Mock()
        mock_token_response.json.return_value = {
            'access_token': 'test_access_token',
            'refresh_token': 'test_refresh_token',
            'expires_in': 3600
        }
        mock_token_response.raise_for_status.return_value = None
        mock_post.return_value = mock_token_response
        
        # Mock 사용자 정보 응답
        mock_user_response = Mock()
        mock_user_response.json.return_value = {
            'email': '<EMAIL>',
            'given_name': 'Integration',
            'family_name': 'Test',
            'name': 'Integration Test'
        }
        mock_user_response.raise_for_status.return_value = None
        mock_get.return_value = mock_user_response
        
        # 1. 인증 URL 생성
        login_data = {
            'redirect_uri': 'http://localhost:8000/callback/'
        }
        login_response = self.client.post(
            reverse('accounts:google-oauth2-login'), 
            login_data, 
            format='json'
        )
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)
        
        # 2. 콜백 처리 (인증 완료)
        callback_data = {
            'code': 'test_auth_code',
            'redirect_uri': 'http://localhost:8000/callback/'
        }
        callback_response = self.client.post(
            reverse('accounts:google-oauth2-callback'),
            callback_data,
            format='json'
        )
        
        # 검증
        self.assertEqual(callback_response.status_code, status.HTTP_200_OK)
        self.assertIn('access', callback_response.data)
        self.assertIn('refresh', callback_response.data)
        self.assertIn('user', callback_response.data)
        
        # 사용자가 데이터베이스에 생성되었는지 확인
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.first_name, 'Integration')
        self.assertEqual(user.last_name, 'Test')
        self.assertEqual(user.role, 'regular')