from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from rest_framework_simplejwt.tokens import RefreshToken
from .models import User, Organization


class OrganizationSerializer(serializers.ModelSerializer):
    """조직 시리얼라이저"""

    class Meta:
        model = Organization
        fields = ["id", "name", "created_at"]
        read_only_fields = ["created_at"]


class UserSerializer(serializers.ModelSerializer):
    """사용자 시리얼라이저"""

    organization = OrganizationSerializer(read_only=True)
    organization_id = serializers.IntegerField(
        write_only=True, required=False, allow_null=True
    )

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "username",
            "first_name",
            "last_name",
            "role",
            "organization",
            "organization_id",
            "ui_theme",
            "is_active",
            "created_at",
        ]
        read_only_fields = ["created_at"]
        extra_kwargs = {"password": {"write_only": True}}

    def create(self, validated_data):
        """사용자 생성"""
        password = validated_data.pop("password", None)
        organization_id = validated_data.pop("organization_id", None)

        if organization_id:
            try:
                organization = Organization.objects.get(id=organization_id)
                validated_data["organization"] = organization
            except Organization.DoesNotExist:
                raise serializers.ValidationError(
                    {"organization_id": "존재하지 않는 조직입니다."}
                )

        user = User.objects.create_user(**validated_data)
        if password:
            user.set_password(password)
            user.save()

        return user

    def update(self, instance, validated_data):
        """사용자 정보 업데이트"""
        password = validated_data.pop("password", None)
        organization_id = validated_data.pop("organization_id", None)

        if organization_id:
            try:
                organization = Organization.objects.get(id=organization_id)
                validated_data["organization"] = organization
            except Organization.DoesNotExist:
                raise serializers.ValidationError(
                    {"organization_id": "존재하지 않는 조직입니다."}
                )

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()
        return instance


class UserCreateSerializer(serializers.ModelSerializer):
    """사용자 생성용 시리얼라이저"""

    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    organization_id = serializers.IntegerField(
        write_only=True, required=False, allow_null=True
    )

    class Meta:
        model = User
        fields = [
            "email",
            "username",
            "first_name",
            "last_name",
            "password",
            "password_confirm",
            "organization_id",
        ]

    def validate(self, attrs):
        """비밀번호 확인 검증"""
        password = attrs.get("password")
        password_confirm = attrs.get("password_confirm")

        if password != password_confirm:
            raise serializers.ValidationError(
                {"password_confirm": "비밀번호가 일치하지 않습니다."}
            )

        # Django 비밀번호 검증 규칙 적용
        try:
            validate_password(password)
        except Exception as e:
            raise serializers.ValidationError({"password": list(e.messages)})

        return attrs

    def create(self, validated_data):
        """사용자 생성"""
        validated_data.pop("password_confirm")
        organization_id = validated_data.pop("organization_id", None)

        if organization_id:
            try:
                organization = Organization.objects.get(id=organization_id)
                validated_data["organization"] = organization
            except Organization.DoesNotExist:
                raise serializers.ValidationError(
                    {"organization_id": "존재하지 않는 조직입니다."}
                )

        # 기본적으로 일반 사용자로 생성
        validated_data["role"] = "regular"

        user = User.objects.create_user(**validated_data)
        return user


class LoginSerializer(serializers.Serializer):
    """로그인 시리얼라이저"""

    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        """로그인 검증"""
        email = attrs.get("email")
        password = attrs.get("password")
        if email and password:
            user = authenticate(email=email, password=password)
            if not user:
                raise serializers.ValidationError(
                    "이메일 또는 비밀번호가 올바르지 않습니다."
                )
            if not user.is_active:
                raise serializers.ValidationError("비활성화된 계정입니다.")
            attrs["user"] = user
            return attrs
        else:
            raise serializers.ValidationError("이메일과 비밀번호를 모두 입력해주세요.")


class TokenSerializer(serializers.Serializer):
    """JWT 토큰 시리얼라이저"""

    access = serializers.CharField()
    refresh = serializers.CharField()
    user = UserSerializer(read_only=True)

    @classmethod
    def get_token_for_user(cls, user):
        """사용자를 위한 JWT 토큰 생성"""
        refresh = RefreshToken.for_user(user)
        return {
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "user": UserSerializer(user).data,
        }
