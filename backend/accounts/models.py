from django.contrib.auth.models import AbstractUser
from django.db import models


class Organization(models.Model):
    """조직 모델"""
    name = models.CharField(max_length=100, verbose_name="조직명")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    
    class Meta:
        verbose_name = "조직"
        verbose_name_plural = "조직"
        db_table = "organizations"
    
    def __str__(self):
        return self.name


# 사용자 역할 선택지
USER_ROLES = [
    ('regular', 'Regular User'),
    ('org_admin', 'Organization Admin'),
    ('super_admin', 'Super Admin'),
]

# UI 테마 선택지
UI_THEMES = [
    ('corporate', 'Corporate'),
    ('business', 'Business'),
    ('dark', 'Dark'),
    ('retro', 'Retro'),
    ('cyberpunk', 'Cyberpunk'),
]


class User(AbstractUser):
    """커스텀 사용자 모델"""
    email = models.EmailField(unique=True, verbose_name="이메일")
    role = models.Char<PERSON>ield(
        max_length=20, 
        choices=USER_ROLES, 
        default='regular',
        verbose_name="역할"
    )
    organization = models.ForeignKey(
        Organization, 
        on_delete=models.SET_NULL,
        null=True, 
        blank=True,
        verbose_name="소속 조직"
    )
    ui_theme = models.CharField(
        max_length=20,
        choices=UI_THEMES,
        default='corporate',
        verbose_name="UI 테마"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    
    # 이메일을 username으로 사용
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']
    
    class Meta:
        verbose_name = "사용자"
        verbose_name_plural = "사용자"
        db_table = "users"
    
    def __str__(self):
        return self.email
    
    def is_regular_user(self):
        """일반 사용자인지 확인"""
        return self.role == 'regular'
    
    def is_org_admin(self):
        """조직 관리자인지 확인"""
        return self.role == 'org_admin'
    
    def is_super_admin(self):
        """슈퍼 관리자인지 확인"""
        return self.role == 'super_admin'
    
    def has_admin_permissions(self):
        """관리자 권한이 있는지 확인"""
        return self.role in ['org_admin', 'super_admin']