from django.urls import path
from . import views
from .oauth_views import (
    GoogleOAuth2LoginView, GoogleOAuth2CallbackView,
    google_oauth2_login_api, google_oauth2_config_api
)

app_name = 'accounts'

urlpatterns = [
    # 인증 관련 URL
    path('register/', views.register_view, name='register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('profile/', views.user_profile_view, name='profile'),
    path('theme/', views.update_theme_view, name='update-theme'),
    path('token/refresh/', views.token_refresh_view, name='token-refresh'),
    path('token/verify/', views.token_verify_view, name='token-verify'),
    
    # Google OAuth2 관련 URL
    path('google/', GoogleOAuth2LoginView.as_view(), name='google-oauth2-login'),
    path('google/callback/', GoogleOAuth2CallbackView.as_view(), name='google-oauth2-callback'),
    path('google/login/', google_oauth2_login_api, name='google-oauth2-login-api'),
    path('google/config/', google_oauth2_config_api, name='google-oauth2-config'),
    
    # 조직 관련 URL
    path('organizations/', views.OrganizationListView.as_view(), name='organization-list'),
    path('organizations/<int:pk>/', views.OrganizationDetailView.as_view(), name='organization-detail'),
    
    # 사용자 관련 URL
    path('users/', views.UserListView.as_view(), name='user-list'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user-detail'),
]