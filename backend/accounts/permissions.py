"""
커스텀 권한 클래스들
"""
from rest_framework import permissions


class IsAdminUser(permissions.BasePermission):
    """관리자 권한 확인 (조직 관리자 또는 슈퍼 관리자)"""
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.has_admin_permissions()
        )


class IsSuperAdminUser(permissions.BasePermission):
    """슈퍼 관리자 권한 확인"""
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_super_admin()
        )


class IsOwnerOrAdmin(permissions.BasePermission):
    """소유자 또는 관리자 권한 확인"""
    
    def has_object_permission(self, request, view, obj):
        # 읽기 권한은 인증된 사용자에게 허용
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 쓰기 권한은 소유자 또는 관리자에게만 허용
        if hasattr(obj, 'created_by'):
            return (
                obj.created_by == request.user or 
                request.user.has_admin_permissions()
            )
        
        return request.user.has_admin_permissions()


class IsOwnerOrReadOnly(permissions.BasePermission):
    """소유자만 수정 가능, 나머지는 읽기 전용"""
    
    def has_object_permission(self, request, view, obj):
        # 읽기 권한은 인증된 사용자에게 허용
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 쓰기 권한은 소유자에게만 허용
        if hasattr(obj, 'created_by'):
            return obj.created_by == request.user
        
        return False


class IsSameOrganizationOrAdmin(permissions.BasePermission):
    """같은 조직 사용자 또는 관리자 권한 확인"""
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # 슈퍼 관리자는 모든 접근 허용
        if user.is_super_admin():
            return True
        
        # 조직 관리자는 같은 조직 내에서만 접근 허용
        if user.is_org_admin():
            if hasattr(obj, 'organization'):
                return obj.organization == user.organization
            elif hasattr(obj, 'created_by'):
                return obj.created_by.organization == user.organization
        
        # 일반 사용자는 본인 것만 접근 허용
        if hasattr(obj, 'created_by'):
            return obj.created_by == user
        
        return False


class CanAccessPage(permissions.BasePermission):
    """페이지 접근 권한 확인"""
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # 슈퍼 관리자는 모든 페이지 접근 가능
        if user.is_super_admin():
            return True
        
        # 조직 관리자는 일반 사용자 + 조직 관리자 페이지 접근 가능
        if user.is_org_admin():
            return obj.permission_level in ['regular', 'org_admin']
        
        # 일반 사용자는 일반 사용자 페이지만 접근 가능
        return obj.permission_level == 'regular'


class CanAccessComponent(permissions.BasePermission):
    """컴포넌트 접근 권한 확인"""
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        page = obj.page if hasattr(obj, 'page') else obj
        
        # 페이지 접근 권한을 확인
        can_access_page = CanAccessPage()
        return can_access_page.has_object_permission(request, view, page)