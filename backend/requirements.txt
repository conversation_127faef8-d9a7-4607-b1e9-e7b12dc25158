# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.in --output-file=requirements.txt
asgiref==3.9.1
    # via
    #   django
    #   django-cors-headers
build==1.3.0
    # via pip-tools
cachetools==5.5.2
    # via google-auth
certifi==2025.8.3
    # via requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via
    #   pip-tools
    #   uvicorn
cryptography==45.0.6
    # via jwcrypto
db-dtypes==1.4.3
    # via pandas-gbq
django==5.2.5
    # via
    #   -r requirements.in
    #   django-cors-headers
    #   django-oauth-toolkit
    #   djangorestframework
    #   djangorestframework-simplejwt
django-cors-headers==4.7.0
    # via -r requirements.in
django-oauth-toolkit==3.0.1
    # via -r requirements.in
djangorestframework==3.16.1
    # via
    #   -r requirements.in
    #   djangorestframework-simplejwt
djangorestframework-simplejwt==5.5.1
    # via -r requirements.in
faker==37.5.3
    # via -r requirements.in
google-api-core==2.25.1
    # via
    #   google-cloud-bigquery
    #   google-cloud-core
    #   pandas-gbq
google-auth==2.40.3
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-auth-httplib2
    #   google-auth-oauthlib
    #   google-cloud-bigquery
    #   google-cloud-core
    #   pandas-gbq
    #   pydata-google-auth
google-auth-httplib2==0.2.0
    # via -r requirements.in
google-auth-oauthlib==1.2.2
    # via
    #   -r requirements.in
    #   pandas-gbq
    #   pydata-google-auth
google-cloud-bigquery==3.35.1
    # via
    #   -r requirements.in
    #   pandas-gbq
google-cloud-core==2.4.3
    # via google-cloud-bigquery
google-crc32c==1.7.1
    # via google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-bigquery
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio==1.74.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.74.0
    # via google-api-core
h11==0.16.0
    # via uvicorn
httplib2==0.22.0
    # via google-auth-httplib2
idna==3.10
    # via requests
jwcrypto==1.5.6
    # via django-oauth-toolkit
numpy==2.3.2
    # via
    #   -r requirements.in
    #   db-dtypes
    #   pandas
    #   pandas-gbq
oauthlib==3.3.1
    # via
    #   django-oauth-toolkit
    #   requests-oauthlib
packaging==25.0
    # via
    #   build
    #   db-dtypes
    #   google-cloud-bigquery
    #   pandas-gbq
pandas==2.3.1
    # via
    #   -r requirements.in
    #   db-dtypes
    #   pandas-gbq
pandas-gbq==0.29.2
    # via -r requirements.in
pip==25.2
    # via pip-tools
pip-tools==7.5.0
    # via -r requirements.in
proto-plus==1.26.1
    # via google-api-core
protobuf==6.31.1
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psycopg2-binary==2.9.10
    # via -r requirements.in
pyarrow==21.0.0
    # via
    #   -r requirements.in
    #   db-dtypes
    #   pandas-gbq
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydata-google-auth==1.9.1
    # via pandas-gbq
pyjwt==2.10.1
    # via
    #   -r requirements.in
    #   djangorestframework-simplejwt
pyparsing==3.2.3
    # via httplib2
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
python-dateutil==2.9.0.post0
    # via
    #   google-cloud-bigquery
    #   pandas
python-decouple==3.8
    # via -r requirements.in
pytz==2025.2
    # via pandas
requests==2.31.0
    # via
    #   -r requirements.in
    #   django-oauth-toolkit
    #   google-api-core
    #   google-cloud-bigquery
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via google-auth-oauthlib
rsa==4.9.1
    # via google-auth
setuptools==80.9.0
    # via
    #   pandas-gbq
    #   pip-tools
    #   pydata-google-auth
six==1.17.0
    # via python-dateutil
sqlparse==0.5.3
    # via django
typing-extensions==4.14.1
    # via jwcrypto
tzdata==2025.2
    # via
    #   faker
    #   pandas
urllib3==2.5.0
    # via requests
uvicorn==0.35.0
    # via -r requirements.in
wheel==0.45.1
    # via pip-tools
