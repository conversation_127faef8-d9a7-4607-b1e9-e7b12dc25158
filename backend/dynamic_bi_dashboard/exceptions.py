"""
커스텀 예외 클래스 및 예외 핸들러
"""
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
import logging

logger = logging.getLogger(__name__)


class BigQueryError(Exception):
    """BigQuery 작업 실패"""
    pass


class PermissionDeniedError(Exception):
    """사용자 권한 부족"""
    pass


class DataValidationError(Exception):
    """잘못된 데이터 제공"""
    pass


class ComponentNotFoundError(Exception):
    """컴포넌트를 찾을 수 없음"""
    pass


class PageNotFoundError(Exception):
    """페이지를 찾을 수 없음"""
    pass


# 매핑된 에러 코드
ERROR_CODE_MAP = {
    BigQueryError: 'BIGQUERY_ERROR',
    PermissionDeniedError: 'PERMISSION_DENIED',
    DataValidationError: 'DATA_VALIDATION_ERROR',
    ComponentNotFoundError: 'COMPONENT_NOT_FOUND',
    PageNotFoundError: 'PAGE_NOT_FOUND',
}


def custom_exception_handler(exc, context):
    """커스텀 예외 핸들러"""
    # 기본 DRF 예외 핸들러 호출
    response = exception_handler(exc, context)
    
    if response is not None:
        # 표준화된 에러 응답 형식
        custom_response_data = {
            'error': True,
            'message': get_error_message(exc, response.data),
            'status_code': response.status_code,
            'error_code': ERROR_CODE_MAP.get(type(exc), getattr(exc, 'default_code', 'error')),
            'details': response.data if isinstance(response.data, dict) else None,
        }
        
        # 로깅
        logger.error(
            f"API Error: {response.status_code} - {custom_response_data['message']} "
            f"- View: {context.get('view', 'Unknown')} "
            f"- Request: {context.get('request', 'Unknown')}"
        )
        
        response.data = custom_response_data
    else:
        # DRF에서 처리되지 않은 예외들 처리
        if isinstance(exc, BigQueryError):
            response = Response({
                'error': True,
                'message': 'BigQuery 데이터 조회 중 오류가 발생했습니다.',
                'status_code': status.HTTP_503_SERVICE_UNAVAILABLE,
                'error_code': ERROR_CODE_MAP[BigQueryError],
                'details': str(exc),
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        elif isinstance(exc, PermissionDeniedError):
            response = Response({
                'error': True,
                'message': '접근 권한이 없습니다.',
                'status_code': status.HTTP_403_FORBIDDEN,
                'error_code': ERROR_CODE_MAP[PermissionDeniedError],
                'details': str(exc),
            }, status=status.HTTP_403_FORBIDDEN)
        
        elif isinstance(exc, DataValidationError):
            response = Response({
                'error': True,
                'message': '데이터 검증에 실패했습니다.',
                'status_code': status.HTTP_400_BAD_REQUEST,
                'error_code': ERROR_CODE_MAP[DataValidationError],
                'details': str(exc),
            }, status=status.HTTP_400_BAD_REQUEST)
        
        elif isinstance(exc, (ComponentNotFoundError, PageNotFoundError)):
            response = Response({
                'error': True,
                'message': '요청한 리소스를 찾을 수 없습니다.',
                'status_code': status.HTTP_404_NOT_FOUND,
                'error_code': ERROR_CODE_MAP[type(exc)],
                'details': str(exc),
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 예상치 못한 서버 오류
        else:
            logger.error(f"Unexpected error: {exc}", exc_info=True)
            response = Response({
                'error': True,
                'message': '서버 내부 오류가 발생했습니다.',
                'status_code': status.HTTP_500_INTERNAL_SERVER_ERROR,
                'error_code': 'SERVER_ERROR',
                'details': 'Internal server error',
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return response


def get_error_message(exc, data):
    """예외에서 사용자 친화적인 메시지 추출"""
    if hasattr(exc, 'detail'):
        if isinstance(exc.detail, dict):
            # 필드별 에러 메시지를 하나의 문자열로 결합
            messages = []
            for field, errors in exc.detail.items():
                if isinstance(errors, list):
                    field_errors = ', '.join(str(error) for error in errors)
                else:
                    field_errors = str(errors)
                messages.append(f"{field}: {field_errors}")
            return '; '.join(messages)
        else:
            return str(exc.detail)
    
    if isinstance(data, dict):
        if 'detail' in data:
            return str(data['detail'])
        elif 'message' in data:
            return str(data['message'])
        else:
            # 첫 번째 에러 메시지 반환
            for key, value in data.items():
                if isinstance(value, list) and value:
                    return f"{key}: {value[0]}"
                elif isinstance(value, str):
                    return f"{key}: {value}"
    
    return str(exc)