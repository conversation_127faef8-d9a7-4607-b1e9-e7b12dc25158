"""
커스텀 미들웨어
"""
import logging
import time
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from rest_framework import status

logger = logging.getLogger(__name__)


class APILoggingMiddleware(MiddlewareMixin):
    """API 요청/응답 로깅 미들웨어"""
    
    def process_request(self, request):
        """요청 처리 시작 시간 기록"""
        request.start_time = time.time()
        
        # API 요청 로깅
        if request.path.startswith('/api/'):
            logger.info(
                f"API Request: {request.method} {request.path} "
                f"- User: {getattr(request.user, 'email', 'Anonymous')} "
                f"- IP: {self.get_client_ip(request)}"
            )
    
    def process_response(self, request, response):
        """응답 처리 완료 시 로깅"""
        if hasattr(request, 'start_time') and request.path.startswith('/api/'):
            duration = time.time() - request.start_time
            logger.info(
                f"API Response: {request.method} {request.path} "
                f"- Status: {response.status_code} "
                f"- Duration: {duration:.3f}s "
                f"- User: {getattr(request.user, 'email', 'Anonymous')}"
            )
        
        return response
    
    def get_client_ip(self, request):
        """클라이언트 IP 주소 추출"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SecurityHeadersMiddleware(MiddlewareMixin):
    """보안 헤더 추가 미들웨어"""
    
    def process_response(self, request, response):
        """보안 헤더 추가"""
        # API 응답에 보안 헤더 추가
        if request.path.startswith('/api/'):
            response['X-Content-Type-Options'] = 'nosniff'
            response['X-Frame-Options'] = 'DENY'
            response['X-XSS-Protection'] = '1; mode=block'
            response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            
            # CORS 헤더 (django-cors-headers와 함께 사용)
            if hasattr(response, 'get') and response.get('Access-Control-Allow-Origin'):
                response['Access-Control-Allow-Credentials'] = 'true'
        
        return response


class RateLimitMiddleware(MiddlewareMixin):
    """간단한 속도 제한 미들웨어 (Redis 없이 메모리 기반)"""
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.request_counts = {}  # IP별 요청 수 저장
        self.last_reset = time.time()
        self.reset_interval = 3600  # 1시간마다 리셋
        self.max_requests = 1000  # 시간당 최대 요청 수
    
    def process_request(self, request):
        """요청 속도 제한 확인"""
        # API 요청에만 적용
        if not request.path.startswith('/api/'):
            return None
        
        # 주기적으로 카운터 리셋
        current_time = time.time()
        if current_time - self.last_reset > self.reset_interval:
            self.request_counts.clear()
            self.last_reset = current_time
        
        # 클라이언트 IP 확인
        client_ip = self.get_client_ip(request)
        
        # 요청 수 증가
        self.request_counts[client_ip] = self.request_counts.get(client_ip, 0) + 1
        
        # 제한 확인
        if self.request_counts[client_ip] > self.max_requests:
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return JsonResponse({
                'error': True,
                'message': '요청 한도를 초과했습니다. 잠시 후 다시 시도해주세요.',
                'status_code': status.HTTP_429_TOO_MANY_REQUESTS
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        
        return None
    
    def get_client_ip(self, request):
        """클라이언트 IP 주소 추출"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserActivityMiddleware(MiddlewareMixin):
    """사용자 활동 추적 미들웨어"""
    
    def process_request(self, request):
        """인증된 사용자의 마지막 활동 시간 업데이트"""
        if (request.user.is_authenticated and 
            request.path.startswith('/api/') and 
            request.method in ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']):
            
            # 사용자 마지막 활동 시간 업데이트 (비동기로 처리하는 것이 좋음)
            try:
                from django.utils import timezone
                request.user.last_login = timezone.now()
                request.user.save(update_fields=['last_login'])
            except Exception as e:
                logger.error(f"Failed to update user activity: {e}")
        
        return None