"""
API 문서화 및 스키마 정의
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.conf import settings


@api_view(['GET'])
@permission_classes([AllowAny])
def api_schema_view(request):
    """API 스키마 정보 제공"""
    schema = {
        'title': 'Dynamic BI Dashboard API',
        'version': '1.0.0',
        'description': '동적 BI 대시보드 생성 앱 API',
        'base_url': request.build_absolute_uri('/api/'),
        'authentication': {
            'types': ['Token', 'OAuth2', 'Session'],
            'description': 'API 인증은 토큰, OAuth2, 또는 세션 기반으로 지원됩니다.'
        },
        'endpoints': {
            'authentication': {
                'register': {
                    'url': '/api/auth/register/',
                    'method': 'POST',
                    'description': '사용자 회원가입',
                    'auth_required': False
                },
                'login': {
                    'url': '/api/auth/login/',
                    'method': 'POST',
                    'description': '사용자 로그인',
                    'auth_required': False
                },
                'logout': {
                    'url': '/api/auth/logout/',
                    'method': 'POST',
                    'description': '사용자 로그아웃',
                    'auth_required': True
                },
                'profile': {
                    'url': '/api/auth/profile/',
                    'method': 'GET',
                    'description': '현재 사용자 프로필 조회',
                    'auth_required': True
                }
            },
            'users': {
                'list': {
                    'url': '/api/users/',
                    'method': 'GET',
                    'description': '사용자 목록 조회 (권한별 필터링)',
                    'auth_required': True
                },
                'create': {
                    'url': '/api/users/',
                    'method': 'POST',
                    'description': '새 사용자 생성 (관리자만)',
                    'auth_required': True,
                    'admin_only': True
                },
                'detail': {
                    'url': '/api/users/{id}/',
                    'method': 'GET',
                    'description': '사용자 상세 정보 조회',
                    'auth_required': True
                }
            },
            'organizations': {
                'list': {
                    'url': '/api/organizations/',
                    'method': 'GET',
                    'description': '조직 목록 조회',
                    'auth_required': True
                },
                'create': {
                    'url': '/api/organizations/',
                    'method': 'POST',
                    'description': '새 조직 생성 (관리자만)',
                    'auth_required': True,
                    'admin_only': True
                }
            },
            'pages': {
                'list': {
                    'url': '/api/pages/',
                    'method': 'GET',
                    'description': '페이지 목록 조회 (권한별 필터링)',
                    'auth_required': True
                },
                'accessible': {
                    'url': '/api/pages/accessible/',
                    'method': 'GET',
                    'description': '현재 사용자가 접근 가능한 페이지 목록',
                    'auth_required': True
                },
                'detail': {
                    'url': '/api/pages/{id}/',
                    'method': 'GET',
                    'description': '페이지 상세 정보 및 컴포넌트 조회',
                    'auth_required': True
                }
            },
            'components': {
                'templates': {
                    'url': '/api/component-templates/',
                    'method': 'GET',
                    'description': '컴포넌트 템플릿 목록 조회',
                    'auth_required': True
                },
                'data': {
                    'url': '/api/components/{id}/data/',
                    'method': 'GET',
                    'description': '컴포넌트 데이터 조회 (BigQuery 연동)',
                    'auth_required': True,
                    'parameters': {
                        'start_date': 'YYYY-MM-DD 형식의 시작 날짜',
                        'end_date': 'YYYY-MM-DD 형식의 종료 날짜',
                        'aggregation': 'minute/hour/day/month 중 하나',
                        'aggregation_type': 'sum/avg/count/window 중 하나'
                    }
                }
            }
        },
        'error_format': {
            'structure': {
                'error': 'boolean (항상 true)',
                'message': 'string (사용자 친화적 에러 메시지)',
                'status_code': 'integer (HTTP 상태 코드)',
                'details': 'object (상세 에러 정보, 선택적)'
            },
            'example': {
                'error': True,
                'message': '이메일 또는 비밀번호가 올바르지 않습니다.',
                'status_code': 400,
                'details': {
                    'email': ['이 필드는 필수입니다.']
                }
            }
        },
        'permission_levels': {
            'regular': '일반 사용자 - 일반 사용자 페이지만 접근 가능',
            'org_admin': '조직 관리자 - 일반 사용자 + 조직 관리자 페이지 접근 가능',
            'super_admin': '슈퍼 관리자 - 모든 페이지 접근 가능 + Django 어드민'
        },
        'chart_types': [
            'bar', 'column', 'line', 'area', 'pie', 'bubble', 'scatter',
            'heatmap', 'treemap', 'radar', 'boxplot', 'radialbar', 'gauge', 'solidgauge'
        ]
    }
    
    return Response(schema)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check_view(request):
    """API 상태 확인"""
    return Response({
        'status': 'healthy',
        'version': '1.0.0',
        'debug': settings.DEBUG,
        'database': 'connected',  # 실제로는 DB 연결 상태 확인 필요
        'timestamp': request.META.get('HTTP_DATE', 'unknown')
    })