from django.db import models
from django.contrib.auth import get_user_model
from accounts.models import Organization

User = get_user_model()

# 권한 레벨 선택지
PERMISSION_LEVELS = [
    ('regular', 'Regular User'),
    ('org_admin', 'Organization Admin'),
]

# 차트 타입 선택지
CHART_TYPES = [
    ('bar', 'Bar Chart'),
    ('column', 'Column Chart'),
    ('line', 'Line Chart'),
    ('area', 'Area Chart'),
    ('pie', 'Pie Chart'),
    ('bubble', 'Bubble Chart'),
    ('scatter', 'Scatter Chart'),
    ('heatmap', 'Heatmap'),
    ('treemap', 'Treemap'),
    ('radar', 'Radar Chart'),
    ('boxplot', 'Box Plot'),
    ('radialbar', 'Radial Bar'),
    ('gauge', 'Gauge'),
    ('solidgauge', 'Solid Gauge'),
]


class Page(models.Model):
    """
    대시보드 페이지 모델
    
    layout_config 필드는 다음과 같은 DashboardLayout 스키마를 따릅니다:
    
    예시 JSON:
    {
      "grid": {
        "mode": "strict-grid",
        "gap": 12,
        "breakpoints": {
          "sm": {"minWidth": 0, "columns": 1, "rowUnit": 220},
          "md": {"minWidth": 640, "columns": 2, "rowUnit": 240},
          "lg": {"minWidth": 1024, "columns": 3, "rowUnit": 260},
          "xl": {"minWidth": 1440, "columns": 4, "rowUnit": 280}
        }
      },
      "rows": [
        {
          "id": "row-1",
          "rowHeight": "auto",
          "items": [
            {"widgetRef": "chart-1", "col": 1, "span": 1, "h": 1},
            {"widgetRef": "chart-2", "col": 2, "span": 1, "h": 1},
            {"widgetRef": "chart-3", "col": 3, "span": 1, "h": 1}
          ]
        },
        {
          "id": "row-2", 
          "rowHeight": 320,
          "items": [
            {"widgetRef": "chart-4", "col": 1, "span": 2, "h": 1},
            {"widgetRef": "chart-5", "col": 3, "span": 1, "h": 1}
          ]
        }
      ],
      "widgets": [
        {
          "id": "chart-1",
          "type": "radar",
          "title": "성과 지표",
          "dataSource": {"componentId": 101},
          "render": {"autoHeight": true, "aspectRatio": 1.0, "minHeight": 220}
        },
        {
          "id": "chart-2", 
          "type": "bar",
          "title": "매출 현황",
          "dataSource": {"componentId": 102},
          "render": {"autoHeight": false, "fixedHeight": 300}
        }
      ]
    }
    """
    name = models.CharField(max_length=100, verbose_name="페이지명")
    permission_level = models.CharField(
        max_length=20, 
        choices=PERMISSION_LEVELS,
        verbose_name="권한 레벨"
    )
    layout_config = models.JSONField(
        default=dict,
        verbose_name="레이아웃 설정",
        help_text="DashboardLayout 스키마 준수: {grid, rows, widgets}"
    )
    layout_version = models.CharField(
        max_length=10,
        default='2.0',
        choices=[('1.0', 'Legacy'), ('2.0', 'New Schema')],
        verbose_name="레이아웃 스키마 버전",
        help_text="레이아웃 설정의 스키마 버전 (1.0: 레거시, 2.0: 새 스키마)"
    )
    created_by = models.ForeignKey(
        User, 
        on_delete=models.CASCADE,
        verbose_name="생성자"
    )
    # N:N 관계: 조직별 페이지 접근 권한
    accessible_organizations = models.ManyToManyField(
        Organization,
        through='PageOrganizationAccess',
        related_name='accessible_pages',
        blank=True,
        verbose_name="접근 가능 조직"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="수정일시")
    is_active = models.BooleanField(default=True, verbose_name="활성 상태")
    
    class Meta:
        verbose_name = "페이지"
        verbose_name_plural = "페이지"
        db_table = "pages"
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def user_has_access(self, user):
        """사용자가 이 페이지에 접근할 수 있는지 확인"""
        # 1. 권한 레벨 기반 접근 확인
        if user.is_super_admin():
            return True
        elif user.is_org_admin() and self.permission_level in ['regular', 'org_admin']:
            return True
        elif user.is_regular_user() and self.permission_level == 'regular':
            return True
        
        # 2. 조직 기반 접근 권한 확인
        if user.organization:
            return self.accessible_organizations.filter(id=user.organization.id).exists()
        
        return False
    
    def get_normalized_layout(self):
        """
        레이아웃 설정을 표준화된 형식으로 반환
        
        Returns:
            dict: 표준화된 DashboardLayout 스키마 형식의 레이아웃 설정
        """
        from dashboard.services import LayoutConfigValidator
        
        validator = LayoutConfigValidator()
        
        try:
            # 레이아웃 설정 검증 및 표준화
            normalized_layout = validator.validate_dashboard_layout(self.layout_config)
            
            # 버전이 1.0이고 마이그레이션이 성공했다면 버전을 2.0으로 업데이트
            if self.layout_version == '1.0' and validator._is_legacy_layout(self.layout_config):
                # 자동으로 버전 업데이트 (save는 호출하지 않음)
                self.layout_version = '2.0'
            
            return normalized_layout
            
        except Exception as e:
            # 검증 실패 시 원본 레이아웃 반환 (로깅 추가 가능)
            return self.layout_config
    
    def migrate_to_new_schema(self, save=True):
        """
        레거시 레이아웃을 새 스키마로 마이그레이션
        
        Args:
            save (bool): 마이그레이션 후 모델을 저장할지 여부
            
        Returns:
            bool: 마이그레이션 성공 여부
        """
        from dashboard.services import LayoutConfigValidator
        
        if self.layout_version == '2.0':
            return True  # 이미 새 스키마
        
        try:
            validator = LayoutConfigValidator()
            
            # 레거시 레이아웃을 새 스키마로 마이그레이션
            migrated_layout = validator.migrate_legacy_layout(self.layout_config)
            
            # 레이아웃 설정 및 버전 업데이트
            self.layout_config = migrated_layout
            self.layout_version = '2.0'
            
            if save:
                self.save(update_fields=['layout_config', 'layout_version'])
            
            return True
            
        except Exception as e:
            # 마이그레이션 실패 시 로깅 (실제 환경에서는 logger 사용)
            return False


class ComponentTemplate(models.Model):
    """
    컴포넌트 템플릿 모델
    
    default_render_config 필드 예시:
    {
      "autoHeight": true,
      "aspectRatio": 0.6,
      "minHeight": 200,
      "maxHeight": 400,
      "chartOptions": {
        "theme": {
          "series": {"colors": ["#FF6B6B", "#4ECDC4", "#45B7D1"]}
        }
      }
    }
    
    차트 타입별 권장 aspectRatio:
    - radar, pie, gauge: 1.0 (정사각형)
    - bar, column: 0.6 (가로형)
    - line, area: 0.5 (와이드형)
    - heatmap, treemap: 0.8 (중간형)
    """
    name = models.CharField(max_length=100, verbose_name="템플릿명")
    chart_type = models.CharField(
        max_length=20, 
        choices=CHART_TYPES,
        verbose_name="차트 타입"
    )
    api_endpoint = models.CharField(
        max_length=200, 
        verbose_name="API 엔드포인트",
        help_text="데이터를 가져올 API 경로"
    )
    api_params = models.JSONField(
        default=dict,
        verbose_name="API 파라미터",
        help_text="API 호출 시 사용할 기본 파라미터"
    )
    bigquery_sql = models.TextField(
        verbose_name="BigQuery SQL",
        help_text="데이터 조회를 위한 SQL 쿼리"
    )
    default_render_config = models.JSONField(
        default=dict,
        verbose_name="기본 렌더링 설정",
        help_text="WidgetRenderConfig 스키마: {autoHeight, aspectRatio, minHeight, maxHeight, fixedHeight}"
    )
    recommended_render_config = models.JSONField(
        default=dict,
        verbose_name="권장 렌더링 설정",
        help_text="차트 타입별 권장 렌더링 설정 (자동 생성)"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="수정일시")
    is_active = models.BooleanField(default=True, verbose_name="활성 상태")
    
    class Meta:
        verbose_name = "컴포넌트 템플릿"
        verbose_name_plural = "컴포넌트 템플릿"
        db_table = "component_templates"
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.get_chart_type_display()})"
    
    def get_default_render_config(self):
        """
        차트 타입별 최적화된 기본 렌더링 설정 반환
        
        Returns:
            dict: 차트 타입에 맞는 기본 렌더링 설정
        """
        # 차트 타입별 권장 설정
        chart_type_defaults = {
            'radar': {
                'autoHeight': True,
                'aspectRatio': 1.0,
                'minHeight': 220,
                'maxHeight': 400,
                'overflow': 'hidden',
                'padding': 16
            },
            'pie': {
                'autoHeight': True,
                'aspectRatio': 1.0,
                'minHeight': 200,
                'maxHeight': 350,
                'overflow': 'hidden',
                'padding': 12
            },
            'gauge': {
                'autoHeight': True,
                'aspectRatio': 1.0,
                'minHeight': 180,
                'maxHeight': 300,
                'overflow': 'hidden',
                'padding': 8
            },
            'solidgauge': {
                'autoHeight': True,
                'aspectRatio': 1.0,
                'minHeight': 180,
                'maxHeight': 300,
                'overflow': 'hidden',
                'padding': 8
            },
            'bar': {
                'autoHeight': True,
                'aspectRatio': 0.6,
                'minHeight': 180,
                'maxHeight': 500,
                'overflow': 'hidden',
                'padding': 12
            },
            'column': {
                'autoHeight': True,
                'aspectRatio': 0.6,
                'minHeight': 180,
                'maxHeight': 500,
                'overflow': 'hidden',
                'padding': 12
            },
            'line': {
                'autoHeight': True,
                'aspectRatio': 0.5,
                'minHeight': 160,
                'maxHeight': 400,
                'overflow': 'hidden',
                'padding': 12
            },
            'area': {
                'autoHeight': True,
                'aspectRatio': 0.5,
                'minHeight': 160,
                'maxHeight': 400,
                'overflow': 'hidden',
                'padding': 12
            },
            'heatmap': {
                'autoHeight': True,
                'aspectRatio': 0.8,
                'minHeight': 200,
                'maxHeight': 450,
                'overflow': 'hidden',
                'padding': 16
            },
            'treemap': {
                'autoHeight': True,
                'aspectRatio': 0.8,
                'minHeight': 200,
                'maxHeight': 450,
                'overflow': 'hidden',
                'padding': 8
            },
            'bubble': {
                'autoHeight': True,
                'aspectRatio': 0.7,
                'minHeight': 200,
                'maxHeight': 400,
                'overflow': 'hidden',
                'padding': 12
            },
            'scatter': {
                'autoHeight': True,
                'aspectRatio': 0.7,
                'minHeight': 200,
                'maxHeight': 400,
                'overflow': 'hidden',
                'padding': 12
            },
            'boxplot': {
                'autoHeight': True,
                'aspectRatio': 0.7,
                'minHeight': 200,
                'maxHeight': 400,
                'overflow': 'hidden',
                'padding': 12
            },
            'radialbar': {
                'autoHeight': True,
                'aspectRatio': 1.0,
                'minHeight': 200,
                'maxHeight': 350,
                'overflow': 'hidden',
                'padding': 12
            }
        }
        
        # 기본 설정 (알려지지 않은 차트 타입용)
        default_config = {
            'autoHeight': True,
            'aspectRatio': 0.7,
            'minHeight': 200,
            'maxHeight': 400,
            'overflow': 'hidden',
            'padding': 12
        }
        
        # 차트 타입별 설정 반환, 없으면 기본 설정
        type_config = chart_type_defaults.get(self.chart_type, default_config)
        
        # 사용자 정의 설정이 있으면 병합
        if self.default_render_config:
            merged_config = type_config.copy()
            merged_config.update(self.default_render_config)
            return merged_config
        
        return type_config
    
    def save(self, *args, **kwargs):
        """저장 시 권장 렌더링 설정 자동 업데이트"""
        # 권장 설정을 자동으로 생성
        self.recommended_render_config = self.get_default_render_config()
        super().save(*args, **kwargs)


class PageTemplate(models.Model):
    """페이지 템플릿 모델"""
    name = models.CharField(max_length=100, verbose_name="템플릿명")
    description = models.TextField(
        blank=True,
        verbose_name="설명",
        help_text="페이지 템플릿에 대한 설명"
    )
    layout_config = models.JSONField(
        default=dict,
        verbose_name="레이아웃 설정",
        help_text="그리드 레이아웃 구성 정보"
    )
    permission_level = models.CharField(
        max_length=20, 
        choices=PERMISSION_LEVELS,
        verbose_name="권한 레벨"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="수정일시")
    is_active = models.BooleanField(default=True, verbose_name="활성 상태")
    
    class Meta:
        verbose_name = "페이지 템플릿"
        verbose_name_plural = "페이지 템플릿"
        db_table = "page_templates"
        ordering = ['name']
    
    def __str__(self):
        return self.name


class PageTemplateComponent(models.Model):
    """페이지 템플릿 내 컴포넌트 모델"""
    page_template = models.ForeignKey(
        PageTemplate, 
        on_delete=models.CASCADE,
        related_name='template_components',
        verbose_name="페이지 템플릿"
    )
    component_template = models.ForeignKey(
        ComponentTemplate, 
        on_delete=models.CASCADE,
        verbose_name="컴포넌트 템플릿"
    )
    grid_position = models.JSONField(
        verbose_name="그리드 위치",
        help_text="컴포넌트의 그리드 내 위치 및 크기 정보 {x, y, width, height}"
    )
    default_params = models.JSONField(
        default=dict,
        verbose_name="기본 파라미터",
        help_text="템플릿에서 사용할 기본 파라미터"
    )
    order = models.IntegerField(
        default=0,
        verbose_name="정렬 순서",
        help_text="템플릿 내에서의 컴포넌트 표시 순서"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="수정일시")
    is_active = models.BooleanField(default=True, verbose_name="활성 상태")
    
    class Meta:
        verbose_name = "페이지 템플릿 컴포넌트"
        verbose_name_plural = "페이지 템플릿 컴포넌트"
        db_table = "page_template_components"
        ordering = ['page_template', 'order']
        unique_together = ['page_template', 'order']
    
    def __str__(self):
        return f"{self.page_template.name} - {self.component_template.name}"


class PageComponent(models.Model):
    """페이지 내 컴포넌트 모델"""
    page = models.ForeignKey(
        Page, 
        on_delete=models.CASCADE,
        related_name='components',
        verbose_name="페이지"
    )
    component_template = models.ForeignKey(
        ComponentTemplate, 
        on_delete=models.CASCADE,
        verbose_name="컴포넌트 템플릿"
    )
    grid_position = models.JSONField(
        verbose_name="그리드 위치",
        help_text="컴포넌트의 그리드 내 위치 및 크기 정보 {x, y, width, height}"
    )
    custom_params = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="커스텀 파라미터",
        help_text="이 컴포넌트에만 적용되는 추가 파라미터"
    )
    order = models.IntegerField(
        default=0,
        verbose_name="정렬 순서",
        help_text="페이지 내에서의 컴포넌트 표시 순서"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="수정일시")
    is_active = models.BooleanField(default=True, verbose_name="활성 상태")
    
    class Meta:
        verbose_name = "페이지 컴포넌트"
        verbose_name_plural = "페이지 컴포넌트"
        db_table = "page_components"
        ordering = ['page', 'order']
        unique_together = ['page', 'order']
    
    def __str__(self):
        return f"{self.page.name} - {self.component_template.name}"


class LayoutPreset(models.Model):
    """
    재사용 가능한 레이아웃 프리셋
    
    layout_template 예시:
    {
      "name": "3열 대시보드",
      "grid": {
        "mode": "strict-grid",
        "gap": 16,
        "breakpoints": {
          "sm": {"minWidth": 0, "columns": 1, "rowUnit": 240},
          "md": {"minWidth": 768, "columns": 2, "rowUnit": 260},
          "lg": {"minWidth": 1024, "columns": 3, "rowUnit": 280},
          "xl": {"minWidth": 1440, "columns": 4, "rowUnit": 300}
        }
      },
      "rows": [
        {
          "id": "header-row",
          "rowHeight": 180,
          "items": [
            {"widgetRef": "kpi-1", "col": 1, "span": 1, "h": 1},
            {"widgetRef": "kpi-2", "col": 2, "span": 1, "h": 1}, 
            {"widgetRef": "kpi-3", "col": 3, "span": 1, "h": 1}
          ]
        },
        {
          "id": "main-row",
          "rowHeight": "auto",
          "items": [
            {"widgetRef": "main-chart", "col": 1, "span": 2, "h": 1},
            {"widgetRef": "side-chart", "col": 3, "span": 1, "h": 1}
          ]
        }
      ]
    }
    """
    name = models.CharField(max_length=100, verbose_name="프리셋명")
    description = models.TextField(
        blank=True,
        verbose_name="설명",
        help_text="레이아웃 프리셋에 대한 설명"
    )
    layout_template = models.JSONField(
        verbose_name="레이아웃 템플릿",
        help_text="DashboardLayout 스키마 (widgets 제외)"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="수정일시")
    is_active = models.BooleanField(default=True, verbose_name="활성 상태")
    
    class Meta:
        verbose_name = "레이아웃 프리셋"
        verbose_name_plural = "레이아웃 프리셋"
        db_table = "layout_presets"
        ordering = ['name']
    
    def __str__(self):
        return self.name


class WidgetPreset(models.Model):
    """재사용 가능한 위젯 프리셋"""
    name = models.CharField(max_length=100, verbose_name="위젯 프리셋명")
    chart_type = models.CharField(
        max_length=20, 
        choices=CHART_TYPES,
        verbose_name="차트 타입"
    )
    component_template = models.ForeignKey(
        ComponentTemplate, 
        on_delete=models.CASCADE,
        verbose_name="컴포넌트 템플릿"
    )
    default_render_config = models.JSONField(
        default=dict,
        verbose_name="기본 렌더링 설정",
        help_text="WidgetRenderConfig 스키마"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="생성일시")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="수정일시")
    is_active = models.BooleanField(default=True, verbose_name="활성 상태")
    
    class Meta:
        verbose_name = "위젯 프리셋"
        verbose_name_plural = "위젯 프리셋"
        db_table = "widget_presets"
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.get_chart_type_display()})"


class PageOrganizationAccess(models.Model):
    """페이지 조직별 접근 권한 중간 모델"""
    organization = models.ForeignKey(
        Organization, 
        on_delete=models.CASCADE,
        verbose_name="조직"
    )
    page = models.ForeignKey(
        Page, 
        on_delete=models.CASCADE,
        verbose_name="페이지"
    )
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name="권한 부여일시")
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='granted_organization_accesses',
        verbose_name="권한 부여자"
    )
    
    class Meta:
        verbose_name = "페이지 조직 접근 권한"
        verbose_name_plural = "페이지 조직 접근 권한"
        db_table = "page_organization_accesses"
        unique_together = ['organization', 'page']
        ordering = ['-granted_at']
    
    def __str__(self):
        return f"{self.organization.name} - {self.page.name}"