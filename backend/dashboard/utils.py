"""
Dashboard 레이아웃 관련 유틸리티 함수들
"""
from typing import Dict, List, Any, Optional


class LayoutValidator:
    """
    레이아웃 스키마 검증 및 관련 유틸리티 클래스
    """
    
    @staticmethod
    def validate_dashboard_layout(layout_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        DashboardLayout 스키마 검증
        
        검증 항목:
        - grid.breakpoints 필수 필드 확인
        - rows[].items[].col이 grid.columns 범위 내인지 확인  
        - widgetRef가 widgets 배열에 존재하는지 확인
        - rowHeight 값 유효성 검증
        
        Args:
            layout_config: 검증할 레이아웃 설정
            
        Returns:
            검증 결과 딕셔너리 {is_valid: bool, errors: List[str]}
        """
        errors = []
        
        # 필수 최상위 키 검증
        required_keys = ['grid', 'rows', 'widgets']
        for key in required_keys:
            if key not in layout_config:
                errors.append(f"필수 키 '{key}'가 누락되었습니다.")
        
        if errors:
            return {'is_valid': False, 'errors': errors}
        
        # Grid 설정 검증
        grid_errors = LayoutValidator._validate_grid_config(layout_config['grid'])
        errors.extend(grid_errors)
        
        # Widgets 검증 및 ID 수집
        widget_ids = set()
        for widget in layout_config.get('widgets', []):
            widget_errors = LayoutValidator._validate_widget(widget)
            errors.extend(widget_errors)
            if 'id' in widget:
                widget_ids.add(widget['id'])
        
        # Rows 검증
        for row_idx, row in enumerate(layout_config.get('rows', [])):
            row_errors = LayoutValidator._validate_row(
                row, 
                row_idx, 
                widget_ids, 
                layout_config['grid']
            )
            errors.extend(row_errors)
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors
        }
    
    @staticmethod
    def _validate_grid_config(grid: Dict[str, Any]) -> List[str]:
        """Grid 설정 검증"""
        errors = []
        
        # 필수 필드 확인
        if 'breakpoints' not in grid:
            errors.append("grid.breakpoints가 누락되었습니다.")
            return errors
        
        # Breakpoints 검증
        required_breakpoints = ['sm', 'md', 'lg', 'xl']
        for bp in required_breakpoints:
            if bp not in grid['breakpoints']:
                errors.append(f"breakpoints.{bp}가 누락되었습니다.")
                continue
            
            bp_config = grid['breakpoints'][bp]
            required_bp_fields = ['minWidth', 'columns', 'rowUnit']
            for field in required_bp_fields:
                if field not in bp_config:
                    errors.append(f"breakpoints.{bp}.{field}가 누락되었습니다.")
                elif not isinstance(bp_config[field], (int, float)):
                    errors.append(f"breakpoints.{bp}.{field}는 숫자여야 합니다.")
        
        # Mode 검증
        if 'mode' in grid and grid['mode'] not in ['strict-grid', 'masonry']:
            errors.append("grid.mode는 'strict-grid' 또는 'masonry'여야 합니다.")
        
        return errors
    
    @staticmethod
    def _validate_widget(widget: Dict[str, Any]) -> List[str]:
        """Widget 설정 검증"""
        errors = []
        
        required_fields = ['id', 'type', 'title']
        for field in required_fields:
            if field not in widget:
                errors.append(f"widget에 '{field}' 필드가 누락되었습니다.")
        
        # 차트 타입 검증
        valid_chart_types = [
            'bar', 'column', 'line', 'area', 'pie', 'bubble', 'scatter',
            'heatmap', 'treemap', 'radar', 'boxplot', 'radialbar', 'gauge', 'solidgauge'
        ]
        if 'type' in widget and widget['type'] not in valid_chart_types:
            errors.append(f"widget.type '{widget['type']}'는 유효하지 않습니다.")
        
        # DataSource 검증
        if 'dataSource' in widget:
            data_source = widget['dataSource']
            if not isinstance(data_source, dict):
                errors.append("widget.dataSource는 객체여야 합니다.")
            elif 'componentId' not in data_source and 'apiUrl' not in data_source:
                errors.append("widget.dataSource에 componentId 또는 apiUrl이 필요합니다.")
        
        return errors
    
    @staticmethod
    def _validate_row(row: Dict[str, Any], row_idx: int, widget_ids: set, grid: Dict[str, Any]) -> List[str]:
        """Row 설정 검증"""
        errors = []
        row_prefix = f"rows[{row_idx}]"
        
        # 필수 필드 확인
        if 'id' not in row:
            errors.append(f"{row_prefix}에 'id' 필드가 누락되었습니다.")
        
        if 'items' not in row:
            errors.append(f"{row_prefix}에 'items' 필드가 누락되었습니다.")
            return errors
        
        # rowHeight 검증
        if 'rowHeight' in row:
            row_height = row['rowHeight']
            if row_height != 'auto' and not isinstance(row_height, (int, float)):
                errors.append(f"{row_prefix}.rowHeight는 'auto' 또는 숫자여야 합니다.")
        
        # Items 검증
        for item_idx, item in enumerate(row['items']):
            item_errors = LayoutValidator._validate_row_item(
                item, 
                f"{row_prefix}.items[{item_idx}]", 
                widget_ids, 
                grid
            )
            errors.extend(item_errors)
        
        return errors
    
    @staticmethod
    def _validate_row_item(item: Dict[str, Any], item_prefix: str, widget_ids: set, grid: Dict[str, Any]) -> List[str]:
        """Row item 설정 검증"""
        errors = []
        
        required_fields = ['widgetRef', 'col', 'span']
        for field in required_fields:
            if field not in item:
                errors.append(f"{item_prefix}에 '{field}' 필드가 누락되었습니다.")
        
        # widgetRef 존재 확인
        if 'widgetRef' in item and item['widgetRef'] not in widget_ids:
            errors.append(f"{item_prefix}.widgetRef '{item['widgetRef']}'에 해당하는 widget이 없습니다.")
        
        # 숫자 필드 검증
        numeric_fields = ['col', 'span', 'h']
        for field in numeric_fields:
            if field in item and not isinstance(item[field], int):
                errors.append(f"{item_prefix}.{field}는 정수여야 합니다.")
        
        # 컬럼 범위 검증 (최대 breakpoint의 columns 기준)
        if 'col' in item and 'span' in item and 'breakpoints' in grid:
            max_columns = max(
                bp.get('columns', 1) 
                for bp in grid['breakpoints'].values() 
                if isinstance(bp, dict)
            )
            
            col = item['col']
            span = item['span']
            
            if col < 1:
                errors.append(f"{item_prefix}.col은 1 이상이어야 합니다.")
            elif col + span - 1 > max_columns:
                errors.append(f"{item_prefix}의 col({col}) + span({span})이 최대 컬럼 수({max_columns})를 초과합니다.")
        
        return errors
    
    @staticmethod
    def get_default_layout() -> Dict[str, Any]:
        """
        기본 레이아웃 설정 반환
        
        Returns:
            기본 DashboardLayout 스키마
        """
        return {
            "grid": {
                "mode": "strict-grid",
                "gap": 12,
                "breakpoints": {
                    "sm": {"minWidth": 0, "columns": 1, "rowUnit": 220},
                    "md": {"minWidth": 640, "columns": 2, "rowUnit": 240},
                    "lg": {"minWidth": 1024, "columns": 3, "rowUnit": 260},
                    "xl": {"minWidth": 1440, "columns": 4, "rowUnit": 280}
                }
            },
            "rows": [],
            "widgets": []
        }
    
    @staticmethod
    def get_chart_type_default_render_config(chart_type: str) -> Dict[str, Any]:
        """
        차트 타입별 기본 렌더링 설정 반환
        
        Args:
            chart_type: 차트 타입
            
        Returns:
            기본 렌더링 설정
        """
        configs = {
            'radar': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 220},
            'pie': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 200},
            'gauge': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 180},
            'solidgauge': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 180},
            'bar': {'autoHeight': True, 'aspectRatio': 0.6, 'minHeight': 200},
            'column': {'autoHeight': True, 'aspectRatio': 0.6, 'minHeight': 200},
            'line': {'autoHeight': True, 'aspectRatio': 0.5, 'minHeight': 180},
            'area': {'autoHeight': True, 'aspectRatio': 0.5, 'minHeight': 180},
            'heatmap': {'autoHeight': True, 'aspectRatio': 0.8, 'minHeight': 220},
            'treemap': {'autoHeight': True, 'aspectRatio': 0.8, 'minHeight': 200},
            'scatter': {'autoHeight': True, 'aspectRatio': 0.7, 'minHeight': 200},
            'bubble': {'autoHeight': True, 'aspectRatio': 0.7, 'minHeight': 200},
            'boxplot': {'autoHeight': True, 'aspectRatio': 0.6, 'minHeight': 220},
            'radialbar': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 200},
        }
        
        return configs.get(chart_type, {
            'autoHeight': True, 
            'aspectRatio': 0.7, 
            'minHeight': 200
        })


class LayoutMigrator:
    """
    기존 PageComponent에서 새로운 layout_config로 마이그레이션하는 유틸리티
    """
    
    @staticmethod
    def migrate_page_components_to_layout_config(page_components: List[Any]) -> Dict[str, Any]:
        """
        PageComponent 리스트를 새로운 layout_config 형식으로 변환
        
        Args:
            page_components: PageComponent 인스턴스 리스트
            
        Returns:
            DashboardLayout 스키마
        """
        layout_config = LayoutValidator.get_default_layout()
        
        if not page_components:
            return layout_config
        
        # 위젯 생성
        widgets = []
        row_items = []
        
        for idx, component in enumerate(page_components):
            widget_id = f"widget-{component.id}"
            
            # Widget 생성
            widget = {
                "id": widget_id,
                "type": component.component_template.chart_type,
                "title": component.component_template.name,
                "dataSource": {"componentId": component.component_template.id},
                "render": LayoutValidator.get_chart_type_default_render_config(
                    component.component_template.chart_type
                )
            }
            widgets.append(widget)
            
            # Row item 생성 (기존 grid_position 활용)
            grid_pos = component.grid_position or {}
            col = idx % 3 + 1  # 기본값: 3열 배치
            span = grid_pos.get('width', 1)
            
            row_items.append({
                "widgetRef": widget_id,
                "col": col,
                "span": span,
                "h": 1
            })
        
        # 단일 행 생성
        if row_items:
            layout_config["rows"] = [{
                "id": "migrated-row",
                "rowHeight": "auto",
                "items": row_items
            }]
        
        layout_config["widgets"] = widgets
        
        return layout_config