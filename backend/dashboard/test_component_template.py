"""
Unit tests for ComponentTemplate render configuration functionality.
"""

from django.test import TestCase
from dashboard.models import ComponentTemplate


class ComponentTemplateTest(TestCase):
    """Test cases for ComponentTemplate render configuration."""
    
    def test_get_default_render_config_radar_chart(self):
        """Test default render config for radar chart."""
        template = ComponentTemplate(
            name="Test Radar Chart",
            chart_type="radar",
            api_endpoint="/api/test",
            bigquery_sql="SELECT * FROM test"
        )
        
        config = template.get_default_render_config()
        
        self.assertEqual(config['aspectRatio'], 1.0)
        self.assertEqual(config['minHeight'], 220)
        self.assertEqual(config['maxHeight'], 400)
        self.assertTrue(config['autoHeight'])
        self.assertEqual(config['overflow'], 'hidden')
        self.assertEqual(config['padding'], 16)
    
    def test_get_default_render_config_bar_chart(self):
        """Test default render config for bar chart."""
        template = ComponentTemplate(
            name="Test Bar Chart",
            chart_type="bar",
            api_endpoint="/api/test",
            bigquery_sql="SELECT * FROM test"
        )
        
        config = template.get_default_render_config()
        
        self.assertEqual(config['aspectRatio'], 0.6)
        self.assertEqual(config['minHeight'], 180)
        self.assertEqual(config['maxHeight'], 500)
        self.assertTrue(config['autoHeight'])
        self.assertEqual(config['overflow'], 'hidden')
        self.assertEqual(config['padding'], 12)
    
    def test_get_default_render_config_line_chart(self):
        """Test default render config for line chart."""
        template = ComponentTemplate(
            name="Test Line Chart",
            chart_type="line",
            api_endpoint="/api/test",
            bigquery_sql="SELECT * FROM test"
        )
        
        config = template.get_default_render_config()
        
        self.assertEqual(config['aspectRatio'], 0.5)
        self.assertEqual(config['minHeight'], 160)
        self.assertEqual(config['maxHeight'], 400)
        self.assertTrue(config['autoHeight'])
        self.assertEqual(config['overflow'], 'hidden')
        self.assertEqual(config['padding'], 12)
    
    def test_get_default_render_config_unknown_chart_type(self):
        """Test default render config for unknown chart type."""
        template = ComponentTemplate(
            name="Test Unknown Chart",
            chart_type="unknown",
            api_endpoint="/api/test",
            bigquery_sql="SELECT * FROM test"
        )
        
        config = template.get_default_render_config()
        
        # Should return default configuration
        self.assertEqual(config['aspectRatio'], 0.7)
        self.assertEqual(config['minHeight'], 200)
        self.assertEqual(config['maxHeight'], 400)
        self.assertTrue(config['autoHeight'])
        self.assertEqual(config['overflow'], 'hidden')
        self.assertEqual(config['padding'], 12)
    
    def test_get_default_render_config_with_custom_override(self):
        """Test default render config with custom override."""
        custom_config = {
            'aspectRatio': 1.5,
            'minHeight': 300,
            'customField': 'custom_value'
        }
        
        template = ComponentTemplate(
            name="Test Custom Chart",
            chart_type="bar",
            api_endpoint="/api/test",
            bigquery_sql="SELECT * FROM test",
            default_render_config=custom_config
        )
        
        config = template.get_default_render_config()
        
        # Should merge custom config with defaults
        self.assertEqual(config['aspectRatio'], 1.5)  # Custom override
        self.assertEqual(config['minHeight'], 300)    # Custom override
        self.assertEqual(config['maxHeight'], 500)    # From default
        self.assertTrue(config['autoHeight'])         # From default
        self.assertEqual(config['customField'], 'custom_value')  # Custom field
    
    def test_save_updates_recommended_render_config(self):
        """Test that saving updates recommended_render_config."""
        template = ComponentTemplate(
            name="Test Save Chart",
            chart_type="pie",
            api_endpoint="/api/test",
            bigquery_sql="SELECT * FROM test"
        )
        
        # Save the template
        template.save()
        
        # Check that recommended_render_config was set
        self.assertIsNotNone(template.recommended_render_config)
        self.assertEqual(template.recommended_render_config['aspectRatio'], 1.0)
        self.assertEqual(template.recommended_render_config['minHeight'], 200)
        self.assertTrue(template.recommended_render_config['autoHeight'])
    
    def test_save_updates_recommended_render_config_with_custom(self):
        """Test that saving updates recommended_render_config with custom settings."""
        custom_config = {
            'aspectRatio': 2.0,
            'minHeight': 250
        }
        
        template = ComponentTemplate(
            name="Test Custom Save Chart",
            chart_type="gauge",
            api_endpoint="/api/test",
            bigquery_sql="SELECT * FROM test",
            default_render_config=custom_config
        )
        
        # Save the template
        template.save()
        
        # Check that recommended_render_config includes custom settings
        self.assertEqual(template.recommended_render_config['aspectRatio'], 2.0)  # Custom
        self.assertEqual(template.recommended_render_config['minHeight'], 250)    # Custom
        self.assertEqual(template.recommended_render_config['maxHeight'], 300)    # From default
        self.assertTrue(template.recommended_render_config['autoHeight'])         # From default
    
    def test_all_chart_types_have_defaults(self):
        """Test that all defined chart types have default configurations."""
        from dashboard.models import CHART_TYPES
        
        for chart_type_code, chart_type_name in CHART_TYPES:
            template = ComponentTemplate(
                name=f"Test {chart_type_name}",
                chart_type=chart_type_code,
                api_endpoint="/api/test",
                bigquery_sql="SELECT * FROM test"
            )
            
            config = template.get_default_render_config()
            
            # All configs should have these required fields
            self.assertIn('autoHeight', config, f"Missing autoHeight for {chart_type_code}")
            self.assertIn('aspectRatio', config, f"Missing aspectRatio for {chart_type_code}")
            self.assertIn('minHeight', config, f"Missing minHeight for {chart_type_code}")
            self.assertIn('maxHeight', config, f"Missing maxHeight for {chart_type_code}")
            self.assertIn('overflow', config, f"Missing overflow for {chart_type_code}")
            self.assertIn('padding', config, f"Missing padding for {chart_type_code}")
            
            # Validate value types
            self.assertIsInstance(config['autoHeight'], bool)
            self.assertIsInstance(config['aspectRatio'], (int, float))
            self.assertIsInstance(config['minHeight'], (int, float))
            self.assertIsInstance(config['maxHeight'], (int, float))
            self.assertIsInstance(config['overflow'], str)
            self.assertIsInstance(config['padding'], (int, float))
            
            # Validate value ranges
            self.assertGreater(config['aspectRatio'], 0)
            self.assertGreater(config['minHeight'], 0)
            self.assertGreater(config['maxHeight'], config['minHeight'])
            self.assertIn(config['overflow'], ['hidden', 'scroll', 'visible'])
            self.assertGreaterEqual(config['padding'], 0)
    
    def test_square_charts_have_aspect_ratio_1(self):
        """Test that square charts (radar, pie, gauge) have aspect ratio 1.0."""
        square_chart_types = ['radar', 'pie', 'gauge', 'solidgauge', 'radialbar']
        
        for chart_type in square_chart_types:
            template = ComponentTemplate(
                name=f"Test {chart_type}",
                chart_type=chart_type,
                api_endpoint="/api/test",
                bigquery_sql="SELECT * FROM test"
            )
            
            config = template.get_default_render_config()
            self.assertEqual(
                config['aspectRatio'], 
                1.0, 
                f"{chart_type} should have aspect ratio 1.0 (square)"
            )
    
    def test_wide_charts_have_low_aspect_ratio(self):
        """Test that wide charts (line, area) have low aspect ratio."""
        wide_chart_types = ['line', 'area']
        
        for chart_type in wide_chart_types:
            template = ComponentTemplate(
                name=f"Test {chart_type}",
                chart_type=chart_type,
                api_endpoint="/api/test",
                bigquery_sql="SELECT * FROM test"
            )
            
            config = template.get_default_render_config()
            self.assertEqual(
                config['aspectRatio'], 
                0.5, 
                f"{chart_type} should have aspect ratio 0.5 (wide)"
            )
    
    def test_template_creation_and_update(self):
        """Test template creation and update workflow."""
        # Create template
        template = ComponentTemplate.objects.create(
            name="Test Workflow Chart",
            chart_type="heatmap",
            api_endpoint="/api/test",
            bigquery_sql="SELECT * FROM test"
        )
        
        # Check initial recommended config
        self.assertEqual(template.recommended_render_config['aspectRatio'], 0.8)
        self.assertEqual(template.recommended_render_config['minHeight'], 200)
        
        # Update with custom config
        template.default_render_config = {'aspectRatio': 1.2, 'minHeight': 350}
        template.save()
        
        # Check updated recommended config
        self.assertEqual(template.recommended_render_config['aspectRatio'], 1.2)
        self.assertEqual(template.recommended_render_config['minHeight'], 350)
        self.assertEqual(template.recommended_render_config['maxHeight'], 450)  # From default