"""
Dashboard app tests
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from unittest.mock import patch
from accounts.models import Organization
from .models import Page, ComponentTemplate, PageComponent, PageTemplate, PageTemplateComponent

User = get_user_model()


class DashboardModelTests(TestCase):
    """Dashboard 모델 테스트"""
    
    def setUp(self):
        self.organization = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            role="regular",
            organization=self.organization
        )
        self.admin_user = User.objects.create_user(
            username="adminuser",
            email="<EMAIL>",
            password="adminpass123",
            role="super_admin",
            organization=self.organization
        )
    
    def test_page_creation(self):
        """페이지 생성 테스트"""
        page = Page.objects.create(
            name="Test Page",
            permission_level="regular",
            layout_config={"columns": 100, "gap": "16px"},
            created_by=self.user
        )
        self.assertEqual(page.name, "Test Page")
        self.assertEqual(page.permission_level, "regular")
        self.assertTrue(page.is_active)
        self.assertEqual(str(page), "Test Page")
    
    def test_component_template_creation(self):
        """컴포넌트 템플릿 생성 테스트"""
        template = ComponentTemplate.objects.create(
            name="Test Chart",
            chart_type="bar",
            api_endpoint="/api/test-data/",
            api_params={"param1": "value1"},
            bigquery_sql="SELECT * FROM test_table"
        )
        self.assertEqual(template.name, "Test Chart")
        self.assertEqual(template.chart_type, "bar")
        self.assertTrue(template.is_active)
        self.assertIn("Test Chart", str(template))
    
    def test_page_component_creation(self):
        """페이지 컴포넌트 생성 테스트"""
        page = Page.objects.create(
            name="Test Page",
            permission_level="regular",
            layout_config={"columns": 100},
            created_by=self.user
        )
        template = ComponentTemplate.objects.create(
            name="Test Chart",
            chart_type="bar",
            api_endpoint="/api/test-data/",
            bigquery_sql="SELECT * FROM test_table"
        )
        component = PageComponent.objects.create(
            page=page,
            component_template=template,
            grid_position={"x": 0, "y": 0, "width": 50, "height": 300},
            order=1
        )
        self.assertEqual(component.page, page)
        self.assertEqual(component.component_template, template)
        self.assertEqual(component.order, 1)
        self.assertTrue(component.is_active)


class PageAPITests(APITestCase):
    """페이지 API 테스트"""
    
    def setUp(self):
        self.organization = Organization.objects.create(name="Test Org")
        
        # 일반 사용자
        self.regular_user = User.objects.create_user(
            username="regular",
            email="<EMAIL>",
            password="testpass123",
            role="regular",
            organization=self.organization
        )
        
        # 조직 관리자
        self.org_admin = User.objects.create_user(
            username="orgadmin",
            email="<EMAIL>",
            password="testpass123",
            role="org_admin",
            organization=self.organization
        )
        
        # 슈퍼 관리자
        self.super_admin = User.objects.create_user(
            username="superadmin",
            email="<EMAIL>",
            password="testpass123",
            role="super_admin",
            organization=self.organization
        )
        
        # 테스트 페이지 생성
        self.regular_page = Page.objects.create(
            name="Regular Page",
            permission_level="regular",
            layout_config={"columns": 100},
            created_by=self.super_admin
        )
        
        self.admin_page = Page.objects.create(
            name="Admin Page",
            permission_level="org_admin",
            layout_config={"columns": 100},
            created_by=self.super_admin
        )
    
    def test_regular_user_page_access(self):
        """일반 사용자 페이지 접근 테스트"""
        self.client.force_authenticate(user=self.regular_user)
        
        # 페이지 목록 조회
        url = reverse('dashboard:page-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 일반 사용자는 regular 페이지만 볼 수 있음
        page_names = [page['name'] for page in response.data['results']]
        self.assertIn("Regular Page", page_names)
        self.assertNotIn("Admin Page", page_names)
    
    def test_org_admin_page_access(self):
        """조직 관리자 페이지 접근 테스트"""
        self.client.force_authenticate(user=self.org_admin)
        
        # 페이지 목록 조회
        url = reverse('dashboard:page-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 조직 관리자는 regular + org_admin 페이지를 볼 수 있음
        page_names = [page['name'] for page in response.data['results']]
        self.assertIn("Regular Page", page_names)
        self.assertIn("Admin Page", page_names)
    
    def test_super_admin_page_access(self):
        """슈퍼 관리자 페이지 접근 테스트"""
        self.client.force_authenticate(user=self.super_admin)
        
        # 페이지 목록 조회
        url = reverse('dashboard:page-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 슈퍼 관리자는 모든 페이지를 볼 수 있음
        page_names = [page['name'] for page in response.data['results']]
        self.assertIn("Regular Page", page_names)
        self.assertIn("Admin Page", page_names)
    
    def test_page_creation_permission(self):
        """페이지 생성 권한 테스트"""
        # 일반 사용자는 페이지 생성 불가
        self.client.force_authenticate(user=self.regular_user)
        url = reverse('dashboard:page-list')
        data = {
            'name': 'New Page',
            'permission_level': 'regular',
            'layout_config': {'columns': 100}
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # 관리자는 페이지 생성 가능
        self.client.force_authenticate(user=self.org_admin)
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New Page')
    
    def test_page_detail_access(self):
        """페이지 상세 조회 테스트"""
        self.client.force_authenticate(user=self.regular_user)
        
        # 일반 사용자는 regular 페이지 상세 조회 가능
        url = reverse('dashboard:page-detail', kwargs={'pk': self.regular_page.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Regular Page')
        
        # 일반 사용자는 admin 페이지 상세 조회 불가
        url = reverse('dashboard:page-detail', kwargs={'pk': self.admin_page.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_user_accessible_pages_endpoint(self):
        """사용자 접근 가능 페이지 엔드포인트 테스트"""
        self.client.force_authenticate(user=self.regular_user)
        
        url = reverse('dashboard:user-accessible-pages')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_role'], 'regular')
        
        page_names = [page['name'] for page in response.data['pages']]
        self.assertIn("Regular Page", page_names)
        self.assertNotIn("Admin Page", page_names)


class ComponentTemplateAPITests(APITestCase):
    """컴포넌트 템플릿 API 테스트"""
    
    def setUp(self):
        self.organization = Organization.objects.create(name="Test Org")
        
        self.regular_user = User.objects.create_user(
            username="regular",
            email="<EMAIL>",
            password="testpass123",
            role="regular",
            organization=self.organization
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            role="super_admin",
            organization=self.organization
        )
        
        self.template = ComponentTemplate.objects.create(
            name="Test Template",
            chart_type="bar",
            api_endpoint="/api/test-data/",
            api_params={"param1": "value1"},
            bigquery_sql="SELECT * FROM test_table"
        )
    
    def test_template_list_permission(self):
        """템플릿 목록 조회 권한 테스트"""
        url = reverse('dashboard:component-template-list')

        # 일반 사용자는 접근 불가
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # 관리자는 접근 가능
        self.client.force_authenticate(user=self.admin_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_template_creation_permission(self):
        """템플릿 생성 권한 테스트"""
        # 일반 사용자는 템플릿 생성 불가
        self.client.force_authenticate(user=self.regular_user)
        url = reverse('dashboard:component-template-list')
        data = {
            'name': 'New Template',
            'chart_type': 'line',
            'api_endpoint': '/api/new-data/',
            'bigquery_sql': 'SELECT * FROM new_table'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # 관리자는 템플릿 생성 가능
        self.client.force_authenticate(user=self.admin_user)
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New Template')
    
    def test_template_detail_operations(self):
        """템플릿 상세 조회/수정/삭제 권한 테스트"""
        url = reverse('dashboard:component-template-detail', kwargs={'pk': self.template.pk})

        # 일반 사용자는 접근 불가
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # 관리자는 조회 및 수정 가능
        self.client.force_authenticate(user=self.admin_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = {'name': 'Updated Template'}
        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated Template')


class ComponentDataAPITests(APITestCase):
    """컴포넌트 데이터 API 테스트"""
    
    def setUp(self):
        self.organization = Organization.objects.create(name="Test Org")
        
        self.regular_user = User.objects.create_user(
            username="regular",
            email="<EMAIL>",
            password="testpass123",
            role="regular",
            organization=self.organization
        )
        
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            role="super_admin",
            organization=self.organization
        )
        
        # 테스트 페이지와 컴포넌트 생성
        self.regular_page = Page.objects.create(
            name="Regular Page",
            permission_level="regular",
            layout_config={"columns": 100},
            created_by=self.admin_user
        )
        
        self.admin_page = Page.objects.create(
            name="Admin Page",
            permission_level="org_admin",
            layout_config={"columns": 100},
            created_by=self.admin_user
        )
        
        self.template = ComponentTemplate.objects.create(
            name="Test Template",
            chart_type="bar",
            api_endpoint="/api/test-data/",
            bigquery_sql="SELECT * FROM test_table"
        )
        
        self.regular_component = PageComponent.objects.create(
            page=self.regular_page,
            component_template=self.template,
            grid_position={"x": 0, "y": 0, "width": 50, "height": 300},
            order=1
        )
        
        self.admin_component = PageComponent.objects.create(
            page=self.admin_page,
            component_template=self.template,
            grid_position={"x": 0, "y": 0, "width": 50, "height": 300},
            order=1
        )
    
    def test_component_data_access_permission(self):
        """컴포넌트 데이터 접근 권한 테스트"""
        with patch('dashboard.services.BigQueryService.__init__', return_value=None), \
             patch('dashboard.services.BigQueryService.run_query', return_value=[{'period': 'Jan', 'value': 10}]):
            # 일반 사용자는 regular 페이지의 컴포넌트 데이터 접근 가능
            self.client.force_authenticate(user=self.regular_user)
            url = reverse('dashboard:component-data', kwargs={'component_id': self.regular_component.pk})
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('data', response.data)

            # 일반 사용자는 admin 페이지의 컴포넌트 데이터 접근 불가
            url = reverse('dashboard:component-data', kwargs={'component_id': self.admin_component.pk})
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

            # 관리자는 모든 컴포넌트 데이터 접근 가능
            self.client.force_authenticate(user=self.admin_user)
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_component_data_not_found(self):
        """존재하지 않는 컴포넌트 데이터 조회 테스트"""
        self.client.force_authenticate(user=self.regular_user)
        url = reverse('dashboard:component-data', kwargs={'component_id': 99999})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)