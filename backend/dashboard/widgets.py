"""
Django Admin용 커스텀 위젯들
"""
import json
from django import forms
from django.utils.safestring import mark_safe
from django.utils.html import escape


class PrettyJSONWidget(forms.Textarea):
    """
    JSON 필드를 보기 좋게 표시하는 위젯
    - 읽기 전용 모드에서는 예쁘게 포맷팅된 JSON 표시
    - 편집 모드에서는 텍스트 에리어로 편집 가능
    """
    
    def __init__(self, attrs=None):
        default_attrs = {
            'rows': 20,
            'cols': 80,
            'style': 'font-family: monospace; font-size: 12px; white-space: pre;'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)

    def format_value(self, value):
        """값을 포맷팅하여 반환"""
        if value is None or value == '':
            return ''
        
        try:
            if isinstance(value, str):
                # 문자열인 경우 JSON으로 파싱 후 다시 포맷팅
                parsed = json.loads(value)
                return json.dumps(parsed, indent=2, ensure_ascii=False, sort_keys=True)
            else:
                # 딕셔너리나 리스트인 경우 직접 포맷팅
                return json.dumps(value, indent=2, ensure_ascii=False, sort_keys=True)
        except (json.JSONDecodeError, TypeError):
            # JSON이 아닌 경우 원본 값 반환
            return str(value)

    class Media:
        css = {
            'all': ['admin/css/pretty_json.css']
        }
        js = ['admin/js/pretty_json.js']


class ReadOnlyJSONWidget(forms.Widget):
    """
    읽기 전용 JSON 위젯 (편집 불가, 보기만 가능)
    """
    
    def render(self, name, value, attrs=None, renderer=None):
        if value is None or value == '':
            return '<div class="readonly-json">None</div>'
        
        try:
            if isinstance(value, str):
                parsed = json.loads(value)
            else:
                parsed = value
            
            formatted_json = json.dumps(parsed, indent=2, ensure_ascii=False, sort_keys=True)
            escaped_json = escape(formatted_json)
            
            return mark_safe(f'''
                <div class="readonly-json-container">
                    <div class="readonly-json-toolbar">
                        <button type="button" class="json-copy-btn" onclick="copyJsonToClipboard(this)">📋 복사</button>
                        <button type="button" class="json-toggle-btn" onclick="toggleJsonView(this)">🔍 접기/펼치기</button>
                    </div>
                    <pre class="readonly-json" data-json="{escape(json.dumps(parsed))}">{escaped_json}</pre>
                </div>
            ''')
        except (json.JSONDecodeError, TypeError):
            return f'<div class="readonly-json-error">Invalid JSON: {escape(str(value))}</div>'


class CollapsibleJSONWidget(PrettyJSONWidget):
    """
    접을 수 있는 JSON 위젯
    """
    
    def render(self, name, value, attrs=None, renderer=None):
        # 기본 textarea 렌더링
        textarea_html = super().render(name, value, attrs, renderer)
        
        if value is None or value == '':
            preview_html = '<div class="json-preview">Empty</div>'
        else:
            try:
                if isinstance(value, str):
                    parsed = json.loads(value)
                else:
                    parsed = value
                
                # 미리보기용 축약된 JSON
                preview = self._create_preview(parsed)
                preview_html = f'<div class="json-preview">{escape(preview)}</div>'
            except (json.JSONDecodeError, TypeError):
                preview_html = f'<div class="json-preview json-error">Invalid JSON</div>'
        
        return mark_safe(f'''
            <div class="collapsible-json-widget">
                {preview_html}
                <div class="json-controls">
                    <button type="button" class="json-expand-btn" onclick="toggleJsonEditor(this)">✏️ 편집</button>
                    <button type="button" class="json-format-btn" onclick="formatJson(this)">🎨 포맷팅</button>
                </div>
                <div class="json-editor" style="display: none;">
                    {textarea_html}
                </div>
            </div>
        ''')
    
    def _create_preview(self, data, max_length=100):
        """JSON 데이터의 축약된 미리보기 생성"""
        if isinstance(data, dict):
            keys = list(data.keys())[:3]
            if len(data) > 3:
                keys_str = ', '.join(f'"{k}"' for k in keys) + f', ... (+{len(data)-3} more)'
            else:
                keys_str = ', '.join(f'"{k}"' for k in keys)
            return f"{{ {keys_str} }}"
        elif isinstance(data, list):
            if len(data) > 3:
                return f"[{len(data)} items]"
            else:
                return f"[{', '.join(str(item)[:20] for item in data[:3])}]"
        else:
            preview = str(data)
            return preview[:max_length] + '...' if len(preview) > max_length else preview