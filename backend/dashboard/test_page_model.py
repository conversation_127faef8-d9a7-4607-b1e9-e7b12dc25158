"""
Unit tests for Page model layout functionality.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from accounts.models import Organization
from dashboard.models import Page

User = get_user_model()


class PageModelTest(TestCase):
    """Test cases for Page model layout functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create test organization and user
        self.organization = Organization.objects.create(
            name="Test Organization"
        )
        
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            organization=self.organization
        )
        
        # Legacy layout configuration
        self.legacy_layout = {
            'components': [
                {
                    'id': 1,
                    'component_template_id': 101,
                    'chart_type': 'bar',
                    'title': 'Sales Chart',
                    'grid_position': {'x': 0, 'y': 0, 'width': 2, 'height': 1}
                },
                {
                    'id': 2,
                    'component_template_id': 102,
                    'chart_type': 'pie',
                    'title': 'Distribution Chart',
                    'grid_position': {'x': 2, 'y': 0, 'width': 1, 'height': 1}
                }
            ]
        }
        
        # New schema layout configuration
        self.new_schema_layout = {
            'grid': {
                'mode': 'strict-grid',
                'gap': 12,
                'breakpoints': {
                    'sm': {'minWidth': 0, 'columns': 1, 'rowUnit': 220},
                    'md': {'minWidth': 640, 'columns': 2, 'rowUnit': 240},
                    'lg': {'minWidth': 1024, 'columns': 3, 'rowUnit': 260},
                    'xl': {'minWidth': 1440, 'columns': 4, 'rowUnit': 280}
                }
            },
            'rows': [
                {
                    'id': 'row-1',
                    'rowHeight': 'auto',
                    'items': [
                        {'widgetRef': 'chart-1', 'col': 1, 'span': 1, 'h': 1},
                        {'widgetRef': 'chart-2', 'col': 2, 'span': 1, 'h': 1}
                    ]
                }
            ],
            'widgets': [
                {
                    'id': 'chart-1',
                    'type': 'bar',
                    'title': 'Sales Chart',
                    'dataSource': {'componentId': 101},
                    'render': {'autoHeight': True, 'aspectRatio': 0.6, 'minHeight': 180}
                },
                {
                    'id': 'chart-2',
                    'type': 'pie',
                    'title': 'Distribution Chart',
                    'dataSource': {'componentId': 102},
                    'render': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 200}
                }
            ]
        }
    
    def test_page_creation_with_default_version(self):
        """Test that new pages are created with version 2.0 by default."""
        page = Page.objects.create(
            name="Test Page",
            permission_level="regular",
            layout_config=self.new_schema_layout,
            created_by=self.user
        )
        
        self.assertEqual(page.layout_version, '2.0')
    
    def test_page_creation_with_legacy_version(self):
        """Test creating a page with legacy version."""
        page = Page.objects.create(
            name="Legacy Page",
            permission_level="regular",
            layout_config=self.legacy_layout,
            layout_version='1.0',
            created_by=self.user
        )
        
        self.assertEqual(page.layout_version, '1.0')
        self.assertEqual(page.layout_config, self.legacy_layout)
    
    def test_get_normalized_layout_new_schema(self):
        """Test get_normalized_layout with new schema layout."""
        page = Page.objects.create(
            name="New Schema Page",
            permission_level="regular",
            layout_config=self.new_schema_layout,
            layout_version='2.0',
            created_by=self.user
        )
        
        normalized = page.get_normalized_layout()
        
        # Should return the same layout (already normalized)
        self.assertIn('grid', normalized)
        self.assertIn('rows', normalized)
        self.assertIn('widgets', normalized)
        self.assertEqual(len(normalized['widgets']), 2)
        self.assertEqual(normalized['widgets'][0]['id'], 'chart-1')
    
    def test_get_normalized_layout_legacy_schema(self):
        """Test get_normalized_layout with legacy schema layout."""
        page = Page.objects.create(
            name="Legacy Page",
            permission_level="regular",
            layout_config=self.legacy_layout,
            layout_version='1.0',
            created_by=self.user
        )
        
        normalized = page.get_normalized_layout()
        
        # Should return migrated layout
        self.assertIn('grid', normalized)
        self.assertIn('rows', normalized)
        self.assertIn('widgets', normalized)
        
        # Check that widgets were created from components
        self.assertEqual(len(normalized['widgets']), 2)
        self.assertEqual(normalized['widgets'][0]['type'], 'bar')
        self.assertEqual(normalized['widgets'][1]['type'], 'pie')
        
        # Check that rows were created
        self.assertEqual(len(normalized['rows']), 1)
        self.assertEqual(len(normalized['rows'][0]['items']), 2)
        
        # Version should be updated to 2.0 (but not saved)
        self.assertEqual(page.layout_version, '2.0')
    
    def test_get_normalized_layout_invalid_config(self):
        """Test get_normalized_layout with invalid configuration."""
        page = Page.objects.create(
            name="Invalid Page",
            permission_level="regular",
            layout_config="invalid_json",  # Invalid configuration
            layout_version='2.0',
            created_by=self.user
        )
        
        normalized = page.get_normalized_layout()
        
        # Should return original config when validation fails
        self.assertEqual(normalized, "invalid_json")
    
    def test_migrate_to_new_schema_success(self):
        """Test successful migration from legacy to new schema."""
        page = Page.objects.create(
            name="Legacy Page",
            permission_level="regular",
            layout_config=self.legacy_layout,
            layout_version='1.0',
            created_by=self.user
        )
        
        # Migrate without saving
        result = page.migrate_to_new_schema(save=False)
        
        self.assertTrue(result)
        self.assertEqual(page.layout_version, '2.0')
        self.assertIn('grid', page.layout_config)
        self.assertIn('rows', page.layout_config)
        self.assertIn('widgets', page.layout_config)
        
        # Check that database wasn't updated yet
        page_from_db = Page.objects.get(id=page.id)
        self.assertEqual(page_from_db.layout_version, '1.0')
    
    def test_migrate_to_new_schema_with_save(self):
        """Test migration with database save."""
        page = Page.objects.create(
            name="Legacy Page",
            permission_level="regular",
            layout_config=self.legacy_layout,
            layout_version='1.0',
            created_by=self.user
        )
        
        # Migrate with saving
        result = page.migrate_to_new_schema(save=True)
        
        self.assertTrue(result)
        self.assertEqual(page.layout_version, '2.0')
        
        # Check that database was updated
        page_from_db = Page.objects.get(id=page.id)
        self.assertEqual(page_from_db.layout_version, '2.0')
        self.assertIn('grid', page_from_db.layout_config)
    
    def test_migrate_to_new_schema_already_new(self):
        """Test migration when page is already using new schema."""
        page = Page.objects.create(
            name="New Schema Page",
            permission_level="regular",
            layout_config=self.new_schema_layout,
            layout_version='2.0',
            created_by=self.user
        )
        
        original_config = page.layout_config.copy()
        
        # Attempt migration
        result = page.migrate_to_new_schema(save=False)
        
        self.assertTrue(result)
        self.assertEqual(page.layout_version, '2.0')
        self.assertEqual(page.layout_config, original_config)  # Should be unchanged
    
    def test_migrate_to_new_schema_invalid_config(self):
        """Test migration with invalid configuration."""
        page = Page.objects.create(
            name="Invalid Page",
            permission_level="regular",
            layout_config="invalid_config",
            layout_version='1.0',
            created_by=self.user
        )
        
        # Attempt migration
        result = page.migrate_to_new_schema(save=False)
        
        # The migration should succeed because migrate_legacy_layout handles invalid configs
        # by creating a default structure
        self.assertTrue(result)
        self.assertEqual(page.layout_version, '2.0')
        self.assertIsInstance(page.layout_config, dict)
        self.assertIn('grid', page.layout_config)
        self.assertIn('rows', page.layout_config)
        self.assertIn('widgets', page.layout_config)
    
    def test_layout_version_choices(self):
        """Test that layout_version field has correct choices."""
        page = Page.objects.create(
            name="Test Page",
            permission_level="regular",
            layout_config={},
            created_by=self.user
        )
        
        # Get field choices
        field = Page._meta.get_field('layout_version')
        choices = field.choices
        
        self.assertEqual(len(choices), 2)
        self.assertIn(('1.0', 'Legacy'), choices)
        self.assertIn(('2.0', 'New Schema'), choices)
    
    def test_layout_version_default(self):
        """Test that layout_version defaults to '2.0'."""
        page = Page.objects.create(
            name="Test Page",
            permission_level="regular",
            layout_config={},
            created_by=self.user
        )
        
        self.assertEqual(page.layout_version, '2.0')
    
    def test_empty_legacy_layout_migration(self):
        """Test migration of empty legacy layout."""
        page = Page.objects.create(
            name="Empty Legacy Page",
            permission_level="regular",
            layout_config={'components': []},
            layout_version='1.0',
            created_by=self.user
        )
        
        normalized = page.get_normalized_layout()
        
        # Should create valid structure even with empty components
        self.assertIn('grid', normalized)
        self.assertIn('rows', normalized)
        self.assertIn('widgets', normalized)
        self.assertEqual(len(normalized['widgets']), 0)
        self.assertEqual(len(normalized['rows']), 0)