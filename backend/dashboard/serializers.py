from rest_framework import serializers
from .models import Page, ComponentTemplate, PageComponent, PageTemplate, PageTemplateComponent
from accounts.serializers import UserSerializer


class ComponentTemplateSerializer(serializers.ModelSerializer):
    """컴포넌트 템플릿 시리얼라이저"""
    default_render_config_computed = serializers.SerializerMethodField()
    
    class Meta:
        model = ComponentTemplate
        fields = [
            'id', 'name', 'chart_type', 'api_endpoint', 'api_params',
            'default_render_config', 'recommended_render_config', 
            'default_render_config_computed', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'recommended_render_config', 'default_render_config_computed']
    
    def get_default_render_config_computed(self, obj):
        """계산된 기본 렌더링 설정 반환"""
        return obj.get_default_render_config()

    def validate_api_params(self, value):
        """API 파라미터 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('API 파라미터는 JSON 객체여야 합니다.')
        return value


class PageComponentSerializer(serializers.ModelSerializer):
    """페이지 컴포넌트 시리얼라이저"""
    component_template = ComponentTemplateSerializer(read_only=True)
    component_template_id = serializers.IntegerField(write_only=True)
    custom_params = serializers.JSONField(required=False)

    class Meta:
        model = PageComponent
        fields = [
            'id', 'component_template', 'component_template_id',
            'grid_position', 'custom_params', 'order', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_grid_position(self, value):
        """그리드 위치 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('그리드 위치는 JSON 객체여야 합니다.')
        
        required_fields = ['x', 'y', 'width', 'height']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f'그리드 위치에 {field} 필드가 필요합니다.')
            if not isinstance(value[field], (int, float)):
                raise serializers.ValidationError(f'{field} 필드는 숫자여야 합니다.')
        
        return value

    def validate_custom_params(self, value):
        """커스텀 파라미터 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('커스텀 파라미터는 JSON 객체여야 합니다.')
        return value

    def validate_component_template_id(self, value):
        """컴포넌트 템플릿 ID 검증"""
        try:
            ComponentTemplate.objects.get(id=value, is_active=True)
        except ComponentTemplate.DoesNotExist:
            raise serializers.ValidationError('존재하지 않거나 비활성화된 컴포넌트 템플릿입니다.')
        return value


class PageSerializer(serializers.ModelSerializer):
    """페이지 시리얼라이저"""
    components = PageComponentSerializer(many=True, read_only=True)
    created_by = UserSerializer(read_only=True)
    normalized_layout = serializers.SerializerMethodField()
    
    class Meta:
        model = Page
        fields = [
            'id', 'name', 'permission_level', 'layout_config', 'layout_version',
            'normalized_layout', 'components', 'created_by', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_at', 'updated_at', 'normalized_layout']
    
    def get_normalized_layout(self, obj):
        """표준화된 레이아웃 설정 반환"""
        return obj.get_normalized_layout()

    def validate_layout_config(self, value):
        """레이아웃 설정 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('레이아웃 설정은 JSON 객체여야 합니다.')
        return value

    def create(self, validated_data):
        """페이지 생성"""
        request = self.context.get('request')
        if request and request.user:
            validated_data['created_by'] = request.user
        return super().create(validated_data)


class PageListSerializer(serializers.ModelSerializer):
    """페이지 목록용 간단한 시리얼라이저"""
    components_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Page
        fields = [
            'id', 'name', 'permission_level', 'components_count',
            'is_active', 'created_at'
        ]

    def get_components_count(self, obj):
        """페이지의 활성 컴포넌트 수"""
        return obj.components.filter(is_active=True).count()


class PageCreateSerializer(serializers.ModelSerializer):
    """페이지 생성용 시리얼라이저"""
    components = PageComponentSerializer(many=True, required=False)
    
    class Meta:
        model = Page
        fields = [
            'name', 'permission_level', 'layout_config', 'components'
        ]

    def validate_layout_config(self, value):
        """레이아웃 설정 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('레이아웃 설정은 JSON 객체여야 합니다.')
        return value

    def create(self, validated_data):
        """페이지와 컴포넌트를 함께 생성"""
        components_data = validated_data.pop('components', [])
        request = self.context.get('request')
        
        if request and request.user:
            validated_data['created_by'] = request.user
        
        page = Page.objects.create(**validated_data)
        
        # 컴포넌트 생성
        for component_data in components_data:
            component_template_id = component_data.pop('component_template_id')
            component_template = ComponentTemplate.objects.get(id=component_template_id)
            PageComponent.objects.create(
                page=page,
                component_template=component_template,
                **component_data
            )
        
        return page


class PageTemplateComponentSerializer(serializers.ModelSerializer):
    """페이지 템플릿 컴포넌트 시리얼라이저"""
    component_template = ComponentTemplateSerializer(read_only=True)
    component_template_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = PageTemplateComponent
        fields = [
            'id', 'component_template', 'component_template_id',
            'grid_position', 'default_params', 'order', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_grid_position(self, value):
        """그리드 위치 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('그리드 위치는 JSON 객체여야 합니다.')
        
        required_fields = ['x', 'y', 'width', 'height']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f'그리드 위치에 {field} 필드가 필요합니다.')
            if not isinstance(value[field], (int, float)):
                raise serializers.ValidationError(f'{field} 필드는 숫자여야 합니다.')
        
        return value

    def validate_default_params(self, value):
        """기본 파라미터 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('기본 파라미터는 JSON 객체여야 합니다.')
        return value

    def validate_component_template_id(self, value):
        """컴포넌트 템플릿 ID 검증"""
        try:
            ComponentTemplate.objects.get(id=value, is_active=True)
        except ComponentTemplate.DoesNotExist:
            raise serializers.ValidationError('존재하지 않거나 비활성화된 컴포넌트 템플릿입니다.')
        return value


class PageTemplateSerializer(serializers.ModelSerializer):
    """페이지 템플릿 시리얼라이저"""
    template_components = PageTemplateComponentSerializer(many=True, read_only=True)
    components_count = serializers.SerializerMethodField()
    
    class Meta:
        model = PageTemplate
        fields = [
            'id', 'name', 'description', 'permission_level', 'layout_config',
            'template_components', 'components_count', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_components_count(self, obj):
        """템플릿의 활성 컴포넌트 수"""
        return obj.template_components.filter(is_active=True).count()

    def validate_layout_config(self, value):
        """레이아웃 설정 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('레이아웃 설정은 JSON 객체여야 합니다.')
        return value


class PageTemplateCreateSerializer(serializers.ModelSerializer):
    """페이지 템플릿 생성용 시리얼라이저"""
    template_components = PageTemplateComponentSerializer(many=True, required=False)
    
    class Meta:
        model = PageTemplate
        fields = [
            'name', 'description', 'permission_level', 'layout_config', 'template_components'
        ]

    def validate_layout_config(self, value):
        """레이아웃 설정 검증"""
        if not isinstance(value, dict):
            raise serializers.ValidationError('레이아웃 설정은 JSON 객체여야 합니다.')
        return value

    def create(self, validated_data):
        """페이지 템플릿과 컴포넌트를 함께 생성"""
        template_components_data = validated_data.pop('template_components', [])
        
        page_template = PageTemplate.objects.create(**validated_data)
        
        # 템플릿 컴포넌트 생성
        for component_data in template_components_data:
            component_template_id = component_data.pop('component_template_id')
            component_template = ComponentTemplate.objects.get(id=component_template_id)
            PageTemplateComponent.objects.create(
                page_template=page_template,
                component_template=component_template,
                **component_data
            )
        
        return page_template


class PageFromTemplateSerializer(serializers.Serializer):
    """템플릿으로부터 페이지 생성용 시리얼라이저"""
    name = serializers.CharField(max_length=100)
    page_template_id = serializers.IntegerField()
    custom_layout_config = serializers.JSONField(required=False)
    
    def validate_page_template_id(self, value):
        """페이지 템플릿 ID 검증"""
        try:
            PageTemplate.objects.get(id=value, is_active=True)
        except PageTemplate.DoesNotExist:
            raise serializers.ValidationError('존재하지 않거나 비활성화된 페이지 템플릿입니다.')
        return value
    
    def validate_custom_layout_config(self, value):
        """커스텀 레이아웃 설정 검증"""
        if value is not None and not isinstance(value, dict):
            raise serializers.ValidationError('레이아웃 설정은 JSON 객체여야 합니다.')
        return value
    
    def create(self, validated_data):
        """템플릿을 기반으로 새 페이지 생성"""
        page_template = PageTemplate.objects.get(id=validated_data['page_template_id'])
        request = self.context.get('request')
        
        # 페이지 생성
        page_data = {
            'name': validated_data['name'],
            'permission_level': page_template.permission_level,
            'layout_config': validated_data.get('custom_layout_config', page_template.layout_config),
            'created_by': request.user if request and request.user else None
        }
        
        page = Page.objects.create(**page_data)
        
        # 템플릿의 컴포넌트들을 페이지에 복사
        template_components = page_template.template_components.filter(is_active=True)
        for template_component in template_components:
            PageComponent.objects.create(
                page=page,
                component_template=template_component.component_template,
                grid_position=template_component.grid_position,
                custom_params=template_component.default_params,
                order=template_component.order
            )
        
        return page