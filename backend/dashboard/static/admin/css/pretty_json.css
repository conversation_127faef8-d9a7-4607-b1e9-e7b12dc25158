/* <PERSON><PERSON>go Admin JSON Widget 스타일 */

.readonly-json-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    margin: 5px 0;
}

.readonly-json-toolbar {
    background: #e1e1e1;
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    display: flex;
    gap: 8px;
}

.json-copy-btn, .json-toggle-btn, .json-expand-btn, .json-format-btn {
    background: #007cba;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
}

.json-copy-btn:hover, .json-toggle-btn:hover, 
.json-expand-btn:hover, .json-format-btn:hover {
    background: #005a87;
}

.readonly-json {
    background: white;
    padding: 12px;
    margin: 0;
    font-family: 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
}

.readonly-json-error {
    color: #d63384;
    background: #f8d7da;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: monospace;
}

/* Collapsible JSON Widget */
.collapsible-json-widget {
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 5px 0;
}

.json-preview {
    background: #f8f9fa;
    padding: 12px;
    font-family: monospace;
    font-size: 12px;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
}

.json-preview.json-error {
    background: #f8d7da;
    color: #721c24;
}

.json-controls {
    background: #e9ecef;
    padding: 8px 12px;
    display: flex;
    gap: 8px;
}

.json-editor {
    padding: 0;
}

.json-editor textarea {
    border: none;
    border-radius: 0 0 4px 4px;
    margin: 0;
    background: white;
}

/* JSON 구문 하이라이팅 */
.json-highlight {
    color: #333;
}

.json-highlight .json-key {
    color: #0066cc;
    font-weight: bold;
}

.json-highlight .json-string {
    color: #009900;
}

.json-highlight .json-number {
    color: #ff6600;
}

.json-highlight .json-boolean {
    color: #cc0066;
    font-weight: bold;
}

.json-highlight .json-null {
    color: #999;
    font-style: italic;
}

/* 반응형 조정 */
@media (max-width: 767px) {
    .readonly-json {
        font-size: 11px;
        padding: 8px;
    }
    
    .readonly-json-toolbar {
        padding: 6px 8px;
    }
    
    .json-copy-btn, .json-toggle-btn, 
    .json-expand-btn, .json-format-btn {
        font-size: 10px;
        padding: 3px 6px;
    }
}

/* Django admin 테마와 조화 */
.readonly-json-container,
.collapsible-json-widget {
    font-size: 13px;
}

/* 접기/펼치기 애니메이션 */
.json-editor {
    transition: all 0.3s ease;
}

.json-editor.collapsed {
    max-height: 0;
    overflow: hidden;
    padding: 0;
}

/* 복사 성공 메시지 */
.copy-success {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    z-index: 9999;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-10px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}