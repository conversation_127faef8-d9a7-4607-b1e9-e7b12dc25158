/* Django Admin JSON Widget JavaScript */

// JSON 클립보드 복사 기능
function copyJsonToClipboard(button) {
    const container = button.closest('.readonly-json-container');
    const jsonElement = container.querySelector('.readonly-json');
    const jsonText = jsonElement.textContent;
    
    navigator.clipboard.writeText(jsonText).then(function() {
        showCopySuccess();
    }).catch(function(err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = jsonText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopySuccess();
    });
}

// 복사 성공 메시지 표시
function showCopySuccess() {
    const message = document.createElement('div');
    message.className = 'copy-success';
    message.textContent = '📋 JSON이 클립보드에 복사되었습니다!';
    document.body.appendChild(message);
    
    setTimeout(() => {
        document.body.removeChild(message);
    }, 2000);
}

// JSON 뷰 토글 (접기/펼치기)
function toggleJsonView(button) {
    const container = button.closest('.readonly-json-container');
    const jsonElement = container.querySelector('.readonly-json');
    
    if (jsonElement.style.display === 'none') {
        jsonElement.style.display = 'block';
        button.textContent = '🔍 접기';
    } else {
        jsonElement.style.display = 'none';
        button.textContent = '🔍 펼치기';
    }
}

// Collapsible JSON Editor 토글
function toggleJsonEditor(button) {
    const widget = button.closest('.collapsible-json-widget');
    const editor = widget.querySelector('.json-editor');
    
    if (editor.style.display === 'none') {
        editor.style.display = 'block';
        button.textContent = '👁️ 미리보기';
    } else {
        editor.style.display = 'none';
        button.textContent = '✏️ 편집';
    }
}

// JSON 포맷팅 기능
function formatJson(button) {
    const widget = button.closest('.collapsible-json-widget');
    const textarea = widget.querySelector('textarea');
    
    try {
        const parsed = JSON.parse(textarea.value);
        const formatted = JSON.stringify(parsed, null, 2);
        textarea.value = formatted;
        
        // 미리보기 업데이트
        updateJsonPreview(widget, parsed);
        
        showFormatSuccess();
    } catch (e) {
        showFormatError('잘못된 JSON 형식입니다: ' + e.message);
    }
}

// JSON 미리보기 업데이트
function updateJsonPreview(widget, data) {
    const preview = widget.querySelector('.json-preview');
    const previewText = createJsonPreview(data);
    preview.textContent = previewText;
    preview.className = 'json-preview'; // 에러 클래스 제거
}

// JSON 미리보기 텍스트 생성
function createJsonPreview(data) {
    if (typeof data === 'object' && data !== null) {
        if (Array.isArray(data)) {
            return data.length > 3 
                ? `[${data.length} items]` 
                : `[${data.slice(0, 3).map(item => JSON.stringify(item).slice(0, 20)).join(', ')}]`;
        } else {
            const keys = Object.keys(data);
            const keyStr = keys.length > 3 
                ? `${keys.slice(0, 3).map(k => `"${k}"`).join(', ')}, ... (+${keys.length - 3} more)`
                : keys.map(k => `"${k}"`).join(', ');
            return `{ ${keyStr} }`;
        }
    } else {
        const str = JSON.stringify(data);
        return str.length > 100 ? str.slice(0, 100) + '...' : str;
    }
}

// 포맷팅 성공 메시지
function showFormatSuccess() {
    const message = document.createElement('div');
    message.className = 'copy-success';
    message.textContent = '🎨 JSON이 포맷팅되었습니다!';
    document.body.appendChild(message);
    
    setTimeout(() => {
        document.body.removeChild(message);
    }, 2000);
}

// 포맷팅 에러 메시지
function showFormatError(errorMsg) {
    const message = document.createElement('div');
    message.className = 'copy-success';
    message.style.background = '#dc3545';
    message.textContent = '❌ ' + errorMsg;
    document.body.appendChild(message);
    
    setTimeout(() => {
        document.body.removeChild(message);
    }, 3000);
}

// JSON 구문 하이라이팅 (선택사항)
function highlightJson(jsonString) {
    return jsonString
        .replace(/"([^"]+)":/g, '<span class="json-key">"$1":</span>')
        .replace(/"([^"]+)"/g, '<span class="json-string">"$1"</span>')
        .replace(/\b(\d+\.?\d*)\b/g, '<span class="json-number">$1</span>')
        .replace(/\b(true|false)\b/g, '<span class="json-boolean">$1</span>')
        .replace(/\bnull\b/g, '<span class="json-null">null</span>');
}

// DOM이 로드된 후 초기화
document.addEventListener('DOMContentLoaded', function() {
    // 모든 JSON textarea에 실시간 validation 추가
    const jsonTextareas = document.querySelectorAll('.collapsible-json-widget textarea');
    jsonTextareas.forEach(textarea => {
        textarea.addEventListener('blur', function() {
            const widget = this.closest('.collapsible-json-widget');
            try {
                const parsed = JSON.parse(this.value);
                updateJsonPreview(widget, parsed);
            } catch (e) {
                const preview = widget.querySelector('.json-preview');
                preview.textContent = '❌ Invalid JSON';
                preview.className = 'json-preview json-error';
            }
        });
    });
});