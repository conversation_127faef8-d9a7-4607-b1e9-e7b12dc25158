from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from dashboard.models import Compo<PERSON>T<PERSON>plate, PageTemplate, PageTemplateComponent, Page, PageComponent, LayoutPreset, WidgetPreset, CHART_TYPES
from dashboard.utils import LayoutValidator

User = get_user_model()


class Command(BaseCommand):
    help = 'Create predefined page and component templates with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--components-only',
            action='store_true',
            help='Create only component templates',
        )
        parser.add_argument(
            '--pages-only',
            action='store_true',
            help='Create only page templates',
        )
        parser.add_argument(
            '--showcase',
            action='store_true',
            help='Create showcase page with all chart types',
        )
        parser.add_argument(
            '--user-email',
            type=str,
            default='<EMAIL>',
            help='Email of user to assign as creator',
        )

    def handle(self, *args, **options):
        user_email = options['user_email']
        try:
            creator = User.objects.get(email=user_email)
        except User.DoesNotExist:
            self.stderr.write(f"User with email {user_email} not found. Please create a user first.")
            return

        if options['components_only']:
            self.create_component_templates()
        elif options['pages_only']:
            self.create_page_templates(creator)
        elif options['showcase']:
            self.create_component_templates()
            self.create_layout_presets()
            self.create_showcase_page_new_schema(creator)
        else:
            # Create everything
            self.create_component_templates()
            self.create_layout_presets()
            self.create_page_templates(creator)
            self.create_showcase_page_new_schema(creator)

        self.stdout.write(self.style.SUCCESS('Predefined templates created successfully!'))

    def create_component_templates(self):
        """Create component templates for each chart type with realistic sample data"""
        self.stdout.write("Creating component templates...")
        
        # Component template data with realistic BigQuery SQL and sample data
        templates_data = [
            {
                'name': '월별 매출 현황 (Bar Chart)',
                'chart_type': 'bar',
                'api_endpoint': '/api/components/monthly-sales/data/',
                'api_params': {'period': 'month', 'limit': 12},
                'bigquery_sql': '''
                    SELECT 
                        FORMAT_DATE('%Y-%m', order_date) as month,
                        SUM(total_amount) as revenue
                    FROM `project.dataset.orders`
                    WHERE order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
                    GROUP BY month
                    ORDER BY month
                '''
            },
            {
                'name': '분기별 매출 추이 (Column Chart)',
                'chart_type': 'column',
                'api_endpoint': '/api/components/quarterly-sales/data/',
                'api_params': {'period': 'quarter', 'years': 3},
                'bigquery_sql': '''
                    SELECT 
                        CONCAT(EXTRACT(YEAR FROM order_date), '-Q', EXTRACT(QUARTER FROM order_date)) as quarter,
                        SUM(total_amount) as revenue
                    FROM `project.dataset.orders`
                    WHERE order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 YEAR)
                    GROUP BY quarter
                    ORDER BY quarter
                '''
            },
            {
                'name': '일별 방문자 추이 (Line Chart)',
                'chart_type': 'line',
                'api_endpoint': '/api/components/daily-visitors/data/',
                'api_params': {'days': 30},
                'bigquery_sql': '''
                    SELECT 
                        DATE(visit_timestamp) as date,
                        COUNT(DISTINCT user_id) as unique_visitors,
                        COUNT(*) as total_visits
                    FROM `project.dataset.user_visits`
                    WHERE visit_timestamp >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
                    GROUP BY date
                    ORDER BY date
                '''
            },
            {
                'name': '누적 사용자 증가 (Area Chart)',
                'chart_type': 'area',
                'api_endpoint': '/api/components/cumulative-users/data/',
                'api_params': {'period': 'month'},
                'bigquery_sql': '''
                    SELECT 
                        DATE_TRUNC(created_at, MONTH) as month,
                        COUNT(*) as new_users,
                        SUM(COUNT(*)) OVER (ORDER BY DATE_TRUNC(created_at, MONTH)) as cumulative_users
                    FROM `project.dataset.users`
                    GROUP BY month
                    ORDER BY month
                '''
            },
            {
                'name': '제품 카테고리별 매출 비율 (Pie Chart)',
                'chart_type': 'pie',
                'api_endpoint': '/api/components/category-revenue/data/',
                'api_params': {'period': 'last_quarter'},
                'bigquery_sql': '''
                    SELECT 
                        p.category,
                        SUM(oi.quantity * oi.unit_price) as revenue
                    FROM `project.dataset.order_items` oi
                    JOIN `project.dataset.products` p ON oi.product_id = p.id
                    JOIN `project.dataset.orders` o ON oi.order_id = o.id
                    WHERE o.order_date >= DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL 1 QUARTER), QUARTER)
                    GROUP BY p.category
                    ORDER BY revenue DESC
                '''
            },
            {
                'name': '매출-수익률 상관관계 (Bubble Chart)',
                'chart_type': 'bubble',
                'api_endpoint': '/api/components/revenue-margin-bubble/data/',
                'api_params': {'period': 'last_year'},
                'bigquery_sql': '''
                    SELECT 
                        p.category,
                        SUM(oi.quantity * oi.unit_price) as revenue,
                        AVG((oi.unit_price - p.cost_price) / oi.unit_price * 100) as margin_percent,
                        COUNT(DISTINCT o.id) as order_count
                    FROM `project.dataset.order_items` oi
                    JOIN `project.dataset.products` p ON oi.product_id = p.id
                    JOIN `project.dataset.orders` o ON oi.order_id = o.id
                    WHERE o.order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 YEAR)
                    GROUP BY p.category
                '''
            },
            {
                'name': '고객 연령-구매액 분포 (Scatter Chart)',
                'chart_type': 'scatter',
                'api_endpoint': '/api/components/age-purchase-scatter/data/',
                'api_params': {'min_orders': 5},
                'bigquery_sql': '''
                    SELECT 
                        c.age,
                        SUM(o.total_amount) as total_spent,
                        COUNT(o.id) as order_count
                    FROM `project.dataset.customers` c
                    JOIN `project.dataset.orders` o ON c.id = o.customer_id
                    WHERE c.age IS NOT NULL
                    GROUP BY c.id, c.age
                    HAVING COUNT(o.id) >= 5
                '''
            },
            {
                'name': '월별 판매량 히트맵 (Heatmap)',
                'chart_type': 'heatmap',
                'api_endpoint': '/api/components/monthly-sales-heatmap/data/',
                'api_params': {'years': 2},
                'bigquery_sql': '''
                    SELECT 
                        EXTRACT(YEAR FROM order_date) as year,
                        EXTRACT(MONTH FROM order_date) as month,
                        SUM(oi.quantity) as total_quantity
                    FROM `project.dataset.orders` o
                    JOIN `project.dataset.order_items` oi ON o.id = oi.order_id
                    WHERE order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 2 YEAR)
                    GROUP BY year, month
                    ORDER BY year, month
                '''
            },
            {
                'name': '제품 계층별 매출 (Treemap)',
                'chart_type': 'treemap',
                'api_endpoint': '/api/components/product-hierarchy-revenue/data/',
                'api_params': {'min_revenue': 1000},
                'bigquery_sql': '''
                    SELECT 
                        p.main_category,
                        p.sub_category,
                        p.name as product_name,
                        SUM(oi.quantity * oi.unit_price) as revenue
                    FROM `project.dataset.order_items` oi
                    JOIN `project.dataset.products` p ON oi.product_id = p.id
                    JOIN `project.dataset.orders` o ON oi.order_id = o.id
                    WHERE o.order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 YEAR)
                    GROUP BY p.main_category, p.sub_category, p.name
                    HAVING revenue >= 1000
                    ORDER BY revenue DESC
                '''
            },
            {
                'name': '고객 만족도 레이더 (Radar Chart)',
                'chart_type': 'radar',
                'api_endpoint': '/api/components/satisfaction-radar/data/',
                'api_params': {'period': 'last_quarter'},
                'bigquery_sql': '''
                    SELECT 
                        metric_name,
                        AVG(score) as avg_score,
                        COUNT(*) as response_count
                    FROM `project.dataset.customer_feedback`
                    WHERE created_at >= DATE_TRUNC(DATE_SUB(CURRENT_DATE(), INTERVAL 1 QUARTER), QUARTER)
                    GROUP BY metric_name
                    ORDER BY metric_name
                '''
            },
            {
                'name': '배송시간 분포 (Box Plot)',
                'chart_type': 'boxplot',
                'api_endpoint': '/api/components/delivery-time-boxplot/data/',
                'api_params': {'months': 6},
                'bigquery_sql': '''
                    SELECT 
                        shipping_method,
                        ARRAY_AGG(delivery_days ORDER BY delivery_days) as delivery_times
                    FROM (
                        SELECT 
                            shipping_method,
                            DATE_DIFF(delivered_date, shipped_date, DAY) as delivery_days
                        FROM `project.dataset.orders`
                        WHERE shipped_date IS NOT NULL 
                        AND delivered_date IS NOT NULL
                        AND order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 6 MONTH)
                    )
                    GROUP BY shipping_method
                '''
            },
            {
                'name': '월별 목표 달성률 (Radial Bar)',
                'chart_type': 'radialbar',
                'api_endpoint': '/api/components/monthly-target-achievement/data/',
                'api_params': {'months': 12},
                'bigquery_sql': '''
                    SELECT 
                        FORMAT_DATE('%Y-%m', order_date) as month,
                        SUM(total_amount) as actual_revenue,
                        MAX(monthly_target) as target_revenue,
                        ROUND(SUM(total_amount) / MAX(monthly_target) * 100, 1) as achievement_percent
                    FROM `project.dataset.orders` o
                    JOIN `project.dataset.monthly_targets` t ON DATE_TRUNC(o.order_date, MONTH) = t.target_month
                    WHERE order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
                    GROUP BY month
                    ORDER BY month
                '''
            },
            {
                'name': '실시간 매출 게이지 (Gauge)',
                'chart_type': 'gauge',
                'api_endpoint': '/api/components/realtime-sales-gauge/data/',
                'api_params': {'period': 'today'},
                'bigquery_sql': '''
                    SELECT 
                        SUM(total_amount) as today_revenue,
                        MAX(daily_target) as daily_target,
                        ROUND(SUM(total_amount) / MAX(daily_target) * 100, 1) as achievement_percent
                    FROM `project.dataset.orders` o
                    JOIN `project.dataset.daily_targets` t ON DATE(o.order_date) = t.target_date
                    WHERE DATE(order_date) = CURRENT_DATE()
                '''
            },
            {
                'name': '종합 성과 대시보드 (Solid Gauge)',
                'chart_type': 'solidgauge',
                'api_endpoint': '/api/components/performance-solidgauge/data/',
                'api_params': {'period': 'this_month'},
                'bigquery_sql': '''
                    SELECT 
                        'Revenue' as metric,
                        SUM(o.total_amount) as actual_value,
                        MAX(t.target_amount) as target_value,
                        ROUND(SUM(o.total_amount) / MAX(t.target_amount) * 100, 1) as percentage
                    FROM `project.dataset.orders` o
                    JOIN `project.dataset.monthly_targets` t ON DATE_TRUNC(o.order_date, MONTH) = t.target_month
                    WHERE DATE_TRUNC(order_date, MONTH) = DATE_TRUNC(CURRENT_DATE(), MONTH)
                    
                    UNION ALL
                    
                    SELECT 
                        'Customer Satisfaction' as metric,
                        AVG(rating) as actual_value,
                        5.0 as target_value,
                        ROUND(AVG(rating) / 5.0 * 100, 1) as percentage
                    FROM `project.dataset.customer_reviews`
                    WHERE DATE_TRUNC(created_at, MONTH) = DATE_TRUNC(CURRENT_DATE(), MONTH)
                '''
            }
        ]

        created_count = 0
        for template_data in templates_data:
            # 차트 타입별 기본 렌더링 설정 추가
            template_data['default_render_config'] = LayoutValidator.get_chart_type_default_render_config(
                template_data['chart_type']
            )
            
            template, created = ComponentTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            if created:
                created_count += 1
                self.stdout.write(f"  ✓ Created component template: {template.name}")
            else:
                self.stdout.write(f"  - Already exists: {template.name}")

        self.stdout.write(f"Created {created_count} new component templates out of {len(templates_data)} total")

    def create_page_templates(self, creator):
        """Create page templates with predefined layouts"""
        self.stdout.write("Creating page templates...")
        
        page_templates_data = [
            {
                'name': '매출 분석 대시보드',
                'description': '매출 관련 주요 지표들을 한눈에 볼 수 있는 대시보드',
                'layout_config': {'columns': 100, 'gap': '16px', 'rowHeight': 'auto'},
                'permission_level': 'regular',
                'components': [
                    {'template_name': '월별 매출 현황 (Bar Chart)', 'position': {'x': 0, 'y': 0, 'w': 50, 'h': 6}, 'order': 1},
                    {'template_name': '분기별 매출 추이 (Column Chart)', 'position': {'x': 50, 'y': 0, 'w': 50, 'h': 6}, 'order': 2},
                    {'template_name': '제품 카테고리별 매출 비율 (Pie Chart)', 'position': {'x': 0, 'y': 6, 'w': 33, 'h': 6}, 'order': 3},
                    {'template_name': '월별 목표 달성률 (Radial Bar)', 'position': {'x': 33, 'y': 6, 'w': 33, 'h': 6}, 'order': 4},
                    {'template_name': '실시간 매출 게이지 (Gauge)', 'position': {'x': 66, 'y': 6, 'w': 34, 'h': 6}, 'order': 5},
                ]
            },
            {
                'name': '고객 분석 대시보드',
                'description': '고객 행동 및 만족도 분석을 위한 대시보드',
                'layout_config': {'columns': 100, 'gap': '16px', 'rowHeight': 'auto'},
                'permission_level': 'regular',
                'components': [
                    {'template_name': '일별 방문자 추이 (Line Chart)', 'position': {'x': 0, 'y': 0, 'w': 60, 'h': 8}, 'order': 1},
                    {'template_name': '누적 사용자 증가 (Area Chart)', 'position': {'x': 60, 'y': 0, 'w': 40, 'h': 8}, 'order': 2},
                    {'template_name': '고객 연령-구매액 분포 (Scatter Chart)', 'position': {'x': 0, 'y': 8, 'w': 50, 'h': 8}, 'order': 3},
                    {'template_name': '고객 만족도 레이더 (Radar Chart)', 'position': {'x': 50, 'y': 8, 'w': 50, 'h': 8}, 'order': 4},
                ]
            },
            {
                'name': '운영 모니터링 대시보드',
                'description': '일일 운영 현황 및 성과 지표 모니터링',
                'layout_config': {'columns': 100, 'gap': '20px', 'rowHeight': 'auto'},
                'permission_level': 'org_admin',
                'components': [
                    {'template_name': '종합 성과 대시보드 (Solid Gauge)', 'position': {'x': 0, 'y': 0, 'w': 25, 'h': 6}, 'order': 1},
                    {'template_name': '실시간 매출 게이지 (Gauge)', 'position': {'x': 25, 'y': 0, 'w': 25, 'h': 6}, 'order': 2},
                    {'template_name': '월별 목표 달성률 (Radial Bar)', 'position': {'x': 50, 'y': 0, 'w': 25, 'h': 6}, 'order': 3},
                    {'template_name': '배송시간 분포 (Box Plot)', 'position': {'x': 75, 'y': 0, 'w': 25, 'h': 6}, 'order': 4},
                    {'template_name': '월별 판매량 히트맵 (Heatmap)', 'position': {'x': 0, 'y': 6, 'w': 50, 'h': 8}, 'order': 5},
                    {'template_name': '제품 계층별 매출 (Treemap)', 'position': {'x': 50, 'y': 6, 'w': 50, 'h': 8}, 'order': 6},
                ]
            }
        ]

        created_count = 0
        for template_data in page_templates_data:
            # Create page template
            page_template, created = PageTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    'name': template_data['name'],
                    'description': template_data['description'],
                    'layout_config': template_data['layout_config'],
                    'permission_level': template_data['permission_level'],
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f"  ✓ Created page template: {page_template.name}")
                
                # Add components to the page template
                for comp_data in template_data['components']:
                    try:
                        component_template = ComponentTemplate.objects.get(name=comp_data['template_name'])
                        PageTemplateComponent.objects.get_or_create(
                            page_template=page_template,
                            component_template=component_template,
                            defaults={
                                'grid_position': comp_data['position'],
                                'order': comp_data['order'],
                                'default_params': {}
                            }
                        )
                    except ComponentTemplate.DoesNotExist:
                        self.stdout.write(f"    Warning: Component template '{comp_data['template_name']}' not found")
            else:
                self.stdout.write(f"  - Already exists: {page_template.name}")

        self.stdout.write(f"Created {created_count} new page templates out of {len(page_templates_data)} total")

    def create_layout_presets(self):
        """Create sample layout presets"""
        self.stdout.write("Creating layout presets...")
        
        presets_data = [
            {
                'name': '단일 열 모바일 레이아웃',
                'description': '모바일 친화적 세로 배치',
                'layout_template': {
                    "grid": {
                        "mode": "strict-grid",
                        "gap": 8,
                        "breakpoints": {
                            "sm": {"minWidth": 0, "columns": 1, "rowUnit": 200},
                            "md": {"minWidth": 640, "columns": 1, "rowUnit": 220},
                            "lg": {"minWidth": 1024, "columns": 2, "rowUnit": 240},
                            "xl": {"minWidth": 1440, "columns": 2, "rowUnit": 260}
                        }
                    }
                }
            },
            {
                'name': '대시보드 표준 레이아웃',
                'description': 'KPI + 메인차트 + 서브차트',
                'layout_template': {
                    "grid": {
                        "mode": "strict-grid",
                        "gap": 16,
                        "breakpoints": {
                            "sm": {"minWidth": 0, "columns": 1, "rowUnit": 200},
                            "md": {"minWidth": 768, "columns": 2, "rowUnit": 240},
                            "lg": {"minWidth": 1024, "columns": 3, "rowUnit": 260},
                            "xl": {"minWidth": 1440, "columns": 4, "rowUnit": 280}
                        }
                    }
                }
            },
            {
                'name': '4열 쇼케이스 레이아웃',
                'description': '다양한 차트를 4열로 정렬하여 표시',
                'layout_template': {
                    "grid": {
                        "mode": "strict-grid",
                        "gap": 12,
                        "breakpoints": {
                            "sm": {"minWidth": 0, "columns": 1, "rowUnit": 220},
                            "md": {"minWidth": 640, "columns": 2, "rowUnit": 240},
                            "lg": {"minWidth": 1024, "columns": 3, "rowUnit": 260},
                            "xl": {"minWidth": 1440, "columns": 4, "rowUnit": 280}
                        }
                    }
                }
            }
        ]
        
        created_count = 0
        for preset_data in presets_data:
            preset, created = LayoutPreset.objects.get_or_create(
                name=preset_data['name'],
                defaults=preset_data
            )
            if created:
                created_count += 1
                self.stdout.write(f"  ✓ Created layout preset: {preset.name}")
            else:
                self.stdout.write(f"  - Already exists: {preset.name}")
        
        self.stdout.write(f"Created {created_count} new layout presets")
    
    def create_showcase_page_new_schema(self, creator):
        """Create a showcase page using new layout schema"""
        self.stdout.write("Creating showcase page with new layout schema...")
        
        # Get all component templates
        component_templates = ComponentTemplate.objects.filter(is_active=True)[:14]  # 최대 14개
        
        # Create widgets array
        widgets = []
        for idx, template in enumerate(component_templates):
            widgets.append({
                "id": f"showcase-widget-{idx+1}",
                "type": template.chart_type,
                "title": template.name,
                "dataSource": {"componentId": template.id},
                "render": template.default_render_config or LayoutValidator.get_chart_type_default_render_config(template.chart_type)
            })
        
        # Create rows with 4-column layout
        rows = []
        for i in range(0, len(widgets), 4):
            row_widgets = widgets[i:i+4]
            items = []
            
            for j, widget in enumerate(row_widgets):
                items.append({
                    "widgetRef": widget["id"],
                    "col": j + 1,
                    "span": 1,
                    "h": 1
                })
            
            rows.append({
                "id": f"showcase-row-{i//4 + 1}",
                "rowHeight": "auto",
                "items": items
            })
        
        # Complete layout config
        layout_config = {
            "grid": {
                "mode": "strict-grid",
                "gap": 12,
                "breakpoints": {
                    "sm": {"minWidth": 0, "columns": 1, "rowUnit": 220},
                    "md": {"minWidth": 640, "columns": 2, "rowUnit": 240},
                    "lg": {"minWidth": 1024, "columns": 3, "rowUnit": 260},
                    "xl": {"minWidth": 1440, "columns": 4, "rowUnit": 280}
                }
            },
            "rows": rows,
            "widgets": widgets
        }
        
        # Create showcase page
        showcase_page, created = Page.objects.get_or_create(
            name='📊 새로운 스키마 쇼케이스',
            defaults={
                'name': '📊 새로운 스키마 쇼케이스',
                'permission_level': 'regular',
                'layout_config': layout_config,
                'created_by': creator,
            }
        )
        
        if created:
            self.stdout.write(f"  ✓ Created new schema showcase page: {showcase_page.name}")
            self.stdout.write(f"    Added {len(widgets)} widgets in {len(rows)} rows")
        else:
            self.stdout.write(f"  - New schema showcase page already exists: {showcase_page.name}")
    
    def create_showcase_page(self, creator):
        """Create a showcase page with all chart types (legacy method)"""
        self.stdout.write("Creating legacy showcase page with all chart types...")
        
        # Create showcase page
        showcase_page, created = Page.objects.get_or_create(
            name='📊 레거시 차트 타입 쇼케이스',
            defaults={
                'name': '📊 레거시 차트 타입 쇼케이스',
                'permission_level': 'regular',
                'layout_config': {'columns': 100, 'gap': '12px', 'rowHeight': 'auto'},
                'created_by': creator,
            }
        )
        
        if created:
            self.stdout.write(f"  ✓ Created legacy showcase page: {showcase_page.name}")
            
            # Add all component types to the showcase page
            component_templates = ComponentTemplate.objects.filter(is_active=True)
            
            # Define grid layout for showcase (4 columns layout)
            positions = [
                {'x': 0, 'y': 0, 'w': 25, 'h': 6},    # Row 1, Col 1
                {'x': 25, 'y': 0, 'w': 25, 'h': 6},   # Row 1, Col 2  
                {'x': 50, 'y': 0, 'w': 25, 'h': 6},   # Row 1, Col 3
                {'x': 75, 'y': 0, 'w': 25, 'h': 6},   # Row 1, Col 4
                {'x': 0, 'y': 6, 'w': 25, 'h': 6},    # Row 2, Col 1
                {'x': 25, 'y': 6, 'w': 25, 'h': 6},   # Row 2, Col 2
                {'x': 50, 'y': 6, 'w': 25, 'h': 6},   # Row 2, Col 3
                {'x': 75, 'y': 6, 'w': 25, 'h': 6},   # Row 2, Col 4
                {'x': 0, 'y': 12, 'w': 25, 'h': 6},   # Row 3, Col 1
                {'x': 25, 'y': 12, 'w': 25, 'h': 6},  # Row 3, Col 2
                {'x': 50, 'y': 12, 'w': 25, 'h': 6},  # Row 3, Col 3
                {'x': 75, 'y': 12, 'w': 25, 'h': 6},  # Row 3, Col 4
                {'x': 0, 'y': 18, 'w': 50, 'h': 6},   # Row 4, Span 2 cols
                {'x': 50, 'y': 18, 'w': 50, 'h': 6},  # Row 4, Span 2 cols
            ]
            
            for order, template in enumerate(component_templates, 1):
                position = positions[(order - 1) % len(positions)]
                if order > len(positions):
                    # If we have more templates than positions, stack them vertically
                    position = {
                        'x': ((order - 1 - len(positions)) % 4) * 25,
                        'y': 24 + ((order - 1 - len(positions)) // 4) * 6,
                        'w': 25,
                        'h': 6
                    }
                
                PageComponent.objects.get_or_create(
                    page=showcase_page,
                    component_template=template,
                    defaults={
                        'grid_position': position,
                        'order': order,
                        'custom_params': {}
                    }
                )
            
            self.stdout.write(f"    Added {component_templates.count()} components to showcase page")
        else:
            self.stdout.write(f"  - Legacy showcase page already exists: {showcase_page.name}")