"""
Management command to update existing ComponentTemplate instances with recommended render configurations.
"""

from django.core.management.base import BaseCommand
from dashboard.models import ComponentTemplate


class Command(BaseCommand):
    help = 'Update existing ComponentTemplate instances with recommended render configurations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Get all ComponentTemplate instances
        templates = ComponentTemplate.objects.all()
        
        if not templates.exists():
            self.stdout.write(
                self.style.WARNING('No ComponentTemplate instances found')
            )
            return
        
        updated_count = 0
        
        for template in templates:
            # Get the computed default render config
            computed_config = template.get_default_render_config()
            
            # Check if recommended_render_config needs updating
            if template.recommended_render_config != computed_config:
                if dry_run:
                    self.stdout.write(
                        f'Would update {template.name} ({template.chart_type}): '
                        f'{template.recommended_render_config} -> {computed_config}'
                    )
                else:
                    template.recommended_render_config = computed_config
                    template.save(update_fields=['recommended_render_config'])
                    self.stdout.write(
                        f'Updated {template.name} ({template.chart_type})'
                    )
                
                updated_count += 1
            else:
                if dry_run:
                    self.stdout.write(
                        f'{template.name} ({template.chart_type}) is already up to date'
                    )
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'DRY RUN: Would update {updated_count} out of {templates.count()} templates'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated {updated_count} out of {templates.count()} templates'
                )
            )