from django.core.management.base import BaseCommand
from django.conf import settings
from google.cloud import bigquery
from google.oauth2 import service_account
import pandas as pd
from datetime import datetime, timedelta
import random
from faker import Faker

class Command(BaseCommand):
    help = 'Create sample data in BigQuery for predefined templates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--project-id',
            type=str,
            default=settings.GOOGLE_CLOUD_PROJECT,
            help='BigQuery project ID',
        )
        parser.add_argument(
            '--dataset-id',
            type=str,
            default='sample_data',
            help='BigQuery dataset ID',
        )
        parser.add_argument(
            '--num-customers',
            type=int,
            default=1000,
            help='Number of customers to generate',
        )
        parser.add_argument(
            '--num-products',
            type=int,
            default=200,
            help='Number of products to generate',
        )
        parser.add_argument(
            '--num-orders',
            type=int,
            default=5000,
            help='Number of orders to generate',
        )
        parser.add_argument(
            '--months-back',
            type=int,
            default=24,
            help='Number of months of historical data',
        )

    def handle(self, **options):
        self.fake = Faker('ko_KR')
        self.project_id = options['project_id']
        self.dataset_id = options['dataset_id']
        
        if not self.project_id:
            self.stderr.write("BigQuery project ID is not configured. Set GOOGLE_CLOUD_PROJECT.")
            return

        try:
            # Initialize BigQuery client
            if settings.GOOGLE_APPLICATION_CREDENTIALS:
                credentials = service_account.Credentials.from_service_account_file(
                    settings.GOOGLE_APPLICATION_CREDENTIALS
                )
                self.client = bigquery.Client(credentials=credentials, project=self.project_id)
            else:
                self.client = bigquery.Client(project=self.project_id)

            # Create dataset if not exists
            self.create_dataset()
            
            # Generate and upload sample data
            self.stdout.write("🔄 Generating sample data...")
            
            customers_df = self.generate_customers(options['num_customers'])
            self.upload_to_bigquery(customers_df, 'customers')
            
            products_df = self.generate_products(options['num_products'])
            self.upload_to_bigquery(products_df, 'products')
            
            orders_df = self.generate_orders(
                options['num_orders'], 
                options['months_back'],
                customers_df,
                products_df
            )
            self.upload_to_bigquery(orders_df, 'orders')
            
            order_items_df = self.generate_order_items(orders_df, products_df)
            self.upload_to_bigquery(order_items_df, 'order_items')
            
            # Generate additional tables
            self.generate_additional_tables(options['months_back'])
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Successfully created sample data in {self.project_id}.{self.dataset_id}'
                )
            )
            
        except Exception as e:
            self.stderr.write(f"❌ Error creating sample data: {str(e)}")

    def create_dataset(self):
        """Create BigQuery dataset if it doesn't exist"""
        dataset_ref = self.client.dataset(self.dataset_id)
        
        try:
            self.client.get_dataset(dataset_ref)
            self.stdout.write(f"📂 Dataset {self.dataset_id} already exists")
        except:
            dataset = bigquery.Dataset(dataset_ref)
            dataset.location = "US"
            dataset.description = "Sample data for BI Dashboard templates"
            
            self.client.create_dataset(dataset)
            self.stdout.write(f"📂 Created dataset {self.dataset_id}")

    def generate_customers(self, num_customers):
        """Generate customer data"""
        self.stdout.write(f"👥 Generating {num_customers} customers...")
        
        customers = []
        for i in range(1, num_customers + 1):
            customers.append({
                'id': i,
                'age': random.randint(18, 80),
                'name': self.fake.name(),
                'email': self.fake.email(),
                'created_at': self.fake.date_time_between(
                    start_date='-2y', 
                    end_date='now'
                ).isoformat()
            })
        
        return pd.DataFrame(customers)

    def generate_products(self, num_products):
        """Generate product data"""
        self.stdout.write(f"📦 Generating {num_products} products...")
        
        categories = {
            '전자제품': ['스마트폰', '노트북', '태블릿', '이어폰', '충전기'],
            '의류': ['상의', '하의', '외투', '속옷', '양말'],
            '식품': ['과자', '음료', '육류', '채소', '과일'],
            '생활용품': ['청소용품', '화장품', '문구용품', '주방용품', '욕실용품'],
            '도서': ['소설', '자기계발', '전문서적', '만화', '잡지']
        }
        
        products = []
        for i in range(1, num_products + 1):
            main_category = random.choice(list(categories.keys()))
            sub_category = random.choice(categories[main_category])
            
            cost_price = random.uniform(5000, 500000)
            
            products.append({
                'id': i,
                'name': f"{sub_category} {i:03d}",
                'category': sub_category,
                'main_category': main_category,
                'sub_category': sub_category,
                'cost_price': round(cost_price, 0),
                'created_at': self.fake.date_time_between(
                    start_date='-1y', 
                    end_date='now'
                ).isoformat()
            })
        
        return pd.DataFrame(products)

    def generate_orders(self, num_orders, months_back, customers_df, products_df):
        """Generate order data"""
        self.stdout.write(f"🛒 Generating {num_orders} orders...")
        
        shipping_methods = ['일반배송', '택배', '당일배송', '픽업']
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months_back * 30)
        
        orders = []
        for i in range(1, num_orders + 1):
            customer_id = random.choice(customers_df['id'].tolist())
            order_date = self.fake.date_time_between(start_date=start_date, end_date=end_date)
            
            # Calculate total amount (will be updated based on order items)
            num_items = random.randint(1, 5)
            total_amount = sum(
                random.choice(products_df['cost_price'].tolist()) * 
                random.uniform(1.2, 3.0) * 
                random.randint(1, 3) 
                for _ in range(num_items)
            )
            
            shipping_method = random.choice(shipping_methods)
            shipped_date = order_date + timedelta(days=random.randint(1, 3))
            delivered_date = shipped_date + timedelta(days=random.randint(1, 7))
            
            orders.append({
                'id': i,
                'customer_id': customer_id,
                'order_date': order_date.date().isoformat(),
                'total_amount': round(total_amount, 0),
                'shipping_method': shipping_method,
                'shipped_date': shipped_date.date().isoformat(),
                'delivered_date': delivered_date.date().isoformat(),
                'created_at': order_date.isoformat()
            })
        
        return pd.DataFrame(orders)

    def generate_order_items(self, orders_df, products_df):
        """Generate order items data"""
        self.stdout.write("📋 Generating order items...")
        
        order_items = []
        item_id = 1
        
        for _, order in orders_df.iterrows():
            num_items = random.randint(1, 5)
            selected_products = random.sample(products_df['id'].tolist(), num_items)
            
            for product_id in selected_products:
                product = products_df[products_df['id'] == product_id].iloc[0]
                quantity = random.randint(1, 3)
                unit_price = product['cost_price'] * random.uniform(1.2, 3.0)
                
                order_items.append({
                    'id': item_id,
                    'order_id': order['id'],
                    'product_id': product_id,
                    'quantity': quantity,
                    'unit_price': round(unit_price, 0)
                })
                item_id += 1
        
        return pd.DataFrame(order_items)

    def generate_additional_tables(self, months_back):
        """Generate additional support tables"""
        self.stdout.write("📊 Generating additional tables...")
        
        # User visits table
        visits_data = []
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months_back * 30)
        
        for _ in range(10000):  # 10,000 visits
            visits_data.append({
                'user_id': random.randint(1, 1000),
                'visit_timestamp': self.fake.date_time_between(
                    start_date=start_date, 
                    end_date=end_date
                ).isoformat(),
                'page_views': random.randint(1, 10),
                'session_duration': random.randint(30, 1800)  # seconds
            })
        
        visits_df = pd.DataFrame(visits_data)
        self.upload_to_bigquery(visits_df, 'user_visits')
        
        # Customer feedback table
        feedback_data = []
        metrics = ['제품품질', '배송속도', '고객서비스', '가격만족도', '전반적만족도']
        
        for _ in range(2000):  # 2,000 feedback entries
            feedback_data.append({
                'id': len(feedback_data) + 1,
                'customer_id': random.randint(1, 1000),
                'metric_name': random.choice(metrics),
                'score': random.randint(1, 5),
                'created_at': self.fake.date_time_between(
                    start_date=start_date,
                    end_date=end_date
                ).isoformat()
            })
        
        feedback_df = pd.DataFrame(feedback_data)
        self.upload_to_bigquery(feedback_df, 'customer_feedback')
        
        # Monthly targets table
        targets_data = []
        current_date = datetime.now().replace(day=1)
        for i in range(months_back):
            target_month = current_date - timedelta(days=i * 30)
            targets_data.append({
                'target_month': target_month.date().isoformat(),
                'monthly_target': random.randint(50000000, 100000000)  # 5천만 ~ 1억
            })
        
        targets_df = pd.DataFrame(targets_data)
        self.upload_to_bigquery(targets_df, 'monthly_targets')
        
        # Daily targets table  
        daily_targets_data = []
        for i in range(30):  # Last 30 days
            target_date = datetime.now().date() - timedelta(days=i)
            daily_targets_data.append({
                'target_date': target_date.isoformat(),
                'daily_target': random.randint(1000000, 5000000)  # 100만 ~ 500만
            })
        
        daily_targets_df = pd.DataFrame(daily_targets_data)
        self.upload_to_bigquery(daily_targets_df, 'daily_targets')

    def upload_to_bigquery(self, df, table_name):
        """Upload DataFrame to BigQuery table"""
        table_id = f"{self.project_id}.{self.dataset_id}.{table_name}"
        
        job_config = bigquery.LoadJobConfig()
        job_config.write_disposition = bigquery.WriteDisposition.WRITE_TRUNCATE
        job_config.autodetect = True
        
        try:
            job = self.client.load_table_from_dataframe(df, table_id, job_config=job_config)
            job.result()  # Wait for the job to complete
            
            self.stdout.write(f"  ✅ Uploaded {len(df)} rows to {table_name}")
            
        except Exception as e:
            self.stderr.write(f"  ❌ Failed to upload {table_name}: {str(e)}")