from django.test import TestCase
from unittest.mock import patch, MagicMock
from dashboard.services import BigQueryService, convert_to_chart_data


class BigQueryServiceTests(TestCase):
    """Tests for BigQueryService"""

    @patch('dashboard.services.bigquery.Client')
    def test_run_query_returns_rows(self, mock_client):
        """run_query should return list of dictionaries"""
        mock_job = MagicMock()
        mock_job.result.return_value = [
            {'period': 'Jan', 'value': 10},
            {'period': 'Feb', 'value': 20},
        ]
        mock_client.return_value.query.return_value = mock_job

        service = BigQueryService(project_id='test')
        result = service.run_query('SELECT period, value FROM table')

        self.assertEqual(result, [
            {'period': 'Jan', 'value': 10},
            {'period': 'Feb', 'value': 20},
        ])
        mock_client.return_value.query.assert_called_once()

    def test_convert_to_chart_data(self):
        """convert_to_chart_data should map rows to chart structure"""
        rows = [
            {'period': 'Jan', 'sales': 100, 'profit': 10},
            {'period': 'Feb', 'sales': 200, 'profit': 20},
        ]
        data = convert_to_chart_data(rows)
        self.assertEqual(data['categories'], ['Jan', 'Feb'])
        self.assertEqual(len(data['series']), 2)
        self.assertEqual(data['series'][0]['name'], 'sales')
        self.assertEqual(data['series'][0]['data'], [100, 200])
        self.assertEqual(data['series'][1]['name'], 'profit')
        self.assertEqual(data['series'][1]['data'], [10, 20])
