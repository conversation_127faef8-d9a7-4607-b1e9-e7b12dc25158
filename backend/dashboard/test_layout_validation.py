"""
Unit tests for LayoutConfigValidator service.
"""

import unittest
from django.test import TestCase
from dashboard.services import LayoutConfigValidator, LayoutValidationError


class LayoutConfigValidatorTest(TestCase):
    """Test cases for LayoutConfigValidator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.validator = LayoutConfigValidator()
        
        # Valid new schema layout
        self.valid_new_layout = {
            'grid': {
                'mode': 'strict-grid',
                'gap': 12,
                'breakpoints': {
                    'sm': {'minWidth': 0, 'columns': 1, 'rowUnit': 220},
                    'md': {'minWidth': 640, 'columns': 2, 'rowUnit': 240},
                    'lg': {'minWidth': 1024, 'columns': 3, 'rowUnit': 260},
                    'xl': {'minWidth': 1440, 'columns': 4, 'rowUnit': 280}
                }
            },
            'rows': [
                {
                    'id': 'row-1',
                    'rowHeight': 'auto',
                    'items': [
                        {'widgetRef': 'chart-1', 'col': 1, 'span': 1, 'h': 1},
                        {'widgetRef': 'chart-2', 'col': 2, 'span': 1, 'h': 1}
                    ]
                }
            ],
            'widgets': [
                {
                    'id': 'chart-1',
                    'type': 'bar',
                    'title': 'Sales Chart',
                    'dataSource': {'componentId': 101},
                    'render': {'autoHeight': True, 'aspectRatio': 0.6, 'minHeight': 180}
                },
                {
                    'id': 'chart-2',
                    'type': 'pie',
                    'title': 'Distribution Chart',
                    'dataSource': {'componentId': 102},
                    'render': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 200}
                }
            ]
        }
        
        # Valid legacy layout
        self.valid_legacy_layout = {
            'components': [
                {
                    'id': 1,
                    'component_template_id': 101,
                    'chart_type': 'bar',
                    'title': 'Sales Chart',
                    'grid_position': {'x': 0, 'y': 0, 'width': 2, 'height': 1}
                },
                {
                    'id': 2,
                    'component_template_id': 102,
                    'chart_type': 'pie',
                    'title': 'Distribution Chart',
                    'grid_position': {'x': 2, 'y': 0, 'width': 1, 'height': 1}
                }
            ]
        }
    
    def test_validate_valid_new_layout(self):
        """Test validation of valid new schema layout."""
        result = self.validator.validate_dashboard_layout(self.valid_new_layout)
        
        self.assertIsInstance(result, dict)
        self.assertIn('grid', result)
        self.assertIn('rows', result)
        self.assertIn('widgets', result)
        
        # Check grid configuration
        self.assertEqual(result['grid']['mode'], 'strict-grid')
        self.assertEqual(result['grid']['gap'], 12)
        self.assertIn('breakpoints', result['grid'])
        
        # Check rows configuration
        self.assertEqual(len(result['rows']), 1)
        self.assertEqual(result['rows'][0]['id'], 'row-1')
        self.assertEqual(len(result['rows'][0]['items']), 2)
        
        # Check widgets configuration
        self.assertEqual(len(result['widgets']), 2)
        self.assertEqual(result['widgets'][0]['id'], 'chart-1')
        self.assertEqual(result['widgets'][1]['id'], 'chart-2')
    
    def test_validate_legacy_layout_migration(self):
        """Test migration of legacy layout to new schema."""
        result = self.validator.validate_dashboard_layout(self.valid_legacy_layout)
        
        self.assertIsInstance(result, dict)
        self.assertIn('grid', result)
        self.assertIn('rows', result)
        self.assertIn('widgets', result)
        
        # Check that widgets were created from components
        self.assertEqual(len(result['widgets']), 2)
        self.assertEqual(result['widgets'][0]['type'], 'bar')
        self.assertEqual(result['widgets'][1]['type'], 'pie')
        
        # Check that rows were created
        self.assertEqual(len(result['rows']), 1)
        self.assertEqual(len(result['rows'][0]['items']), 2)
    
    def test_migrate_legacy_layout_direct(self):
        """Test direct legacy layout migration."""
        result = self.validator.migrate_legacy_layout(self.valid_legacy_layout)
        
        # Check grid defaults
        self.assertEqual(result['grid']['mode'], 'strict-grid')
        self.assertEqual(result['grid']['gap'], 12)
        self.assertIn('breakpoints', result['grid'])
        
        # Check widgets creation
        self.assertEqual(len(result['widgets']), 2)
        widget_1 = result['widgets'][0]
        self.assertEqual(widget_1['id'], 'widget-1')
        self.assertEqual(widget_1['type'], 'bar')
        self.assertEqual(widget_1['title'], 'Sales Chart')
        self.assertEqual(widget_1['dataSource']['componentId'], 101)
        
        # Check render config defaults
        self.assertIn('render', widget_1)
        self.assertEqual(widget_1['render']['aspectRatio'], 0.6)  # bar chart default
        self.assertEqual(widget_1['render']['minHeight'], 180)
        
        # Check rows creation
        self.assertEqual(len(result['rows']), 1)
        row = result['rows'][0]
        self.assertEqual(row['id'], 'row-1')
        self.assertEqual(len(row['items']), 2)
        
        # Check item positioning (converted from 0-based to 1-based)
        item_1 = row['items'][0]
        self.assertEqual(item_1['widgetRef'], 'widget-1')
        self.assertEqual(item_1['col'], 1)  # x=0 -> col=1
        self.assertEqual(item_1['span'], 2)  # width=2
    
    def test_invalid_layout_config_type(self):
        """Test validation error for non-dict layout config."""
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout("invalid")
        
        self.assertEqual(context.exception.field, 'layout_config')
        self.assertIn('must be a dictionary', context.exception.message)
    
    def test_invalid_grid_config(self):
        """Test validation error for invalid grid configuration."""
        invalid_layout = {
            'grid': 'invalid',
            'rows': [],
            'widgets': []
        }
        
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout(invalid_layout)
        
        self.assertEqual(context.exception.field, 'grid')
        self.assertIn('must be a dictionary', context.exception.message)
    
    def test_invalid_grid_mode(self):
        """Test validation error for invalid grid mode."""
        invalid_layout = {
            'grid': {
                'mode': 'invalid-mode',
                'gap': 12,
                'breakpoints': {}
            },
            'rows': [],
            'widgets': []
        }
        
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout(invalid_layout)
        
        self.assertEqual(context.exception.field, 'grid.mode')
        self.assertIn('Invalid grid mode', context.exception.message)
    
    def test_invalid_breakpoint_config(self):
        """Test validation error for invalid breakpoint configuration."""
        invalid_layout = {
            'grid': {
                'mode': 'strict-grid',
                'gap': 12,
                'breakpoints': {
                    'sm': {'minWidth': 0, 'columns': 1}  # missing rowUnit
                }
            },
            'rows': [],
            'widgets': []
        }
        
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout(invalid_layout)
        
        self.assertEqual(context.exception.field, 'grid.breakpoints.sm.rowUnit')
        self.assertIn('Missing required field', context.exception.message)
    
    def test_duplicate_row_ids(self):
        """Test validation error for duplicate row IDs."""
        invalid_layout = {
            'grid': {'mode': 'strict-grid', 'gap': 12, 'breakpoints': {}},
            'rows': [
                {'id': 'row-1', 'rowHeight': 'auto', 'items': []},
                {'id': 'row-1', 'rowHeight': 'auto', 'items': []}  # duplicate
            ],
            'widgets': []
        }
        
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout(invalid_layout)
        
        self.assertEqual(context.exception.field, 'rows[1].id')
        self.assertIn('Duplicate row ID', context.exception.message)
    
    def test_duplicate_widget_ids(self):
        """Test validation error for duplicate widget IDs."""
        invalid_layout = {
            'grid': {'mode': 'strict-grid', 'gap': 12, 'breakpoints': {}},
            'rows': [],
            'widgets': [
                {
                    'id': 'chart-1',
                    'type': 'bar',
                    'title': 'Chart 1',
                    'dataSource': {'componentId': 101},
                    'render': {}
                },
                {
                    'id': 'chart-1',  # duplicate
                    'type': 'pie',
                    'title': 'Chart 2',
                    'dataSource': {'componentId': 102},
                    'render': {}
                }
            ]
        }
        
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout(invalid_layout)
        
        self.assertEqual(context.exception.field, 'widgets[1].id')
        self.assertIn('Duplicate widget ID', context.exception.message)
    
    def test_invalid_widget_reference(self):
        """Test validation error for invalid widget reference."""
        invalid_layout = {
            'grid': {'mode': 'strict-grid', 'gap': 12, 'breakpoints': {}},
            'rows': [
                {
                    'id': 'row-1',
                    'rowHeight': 'auto',
                    'items': [
                        {'widgetRef': 'nonexistent-widget', 'col': 1, 'span': 1, 'h': 1}
                    ]
                }
            ],
            'widgets': [
                {
                    'id': 'chart-1',
                    'type': 'bar',
                    'title': 'Chart 1',
                    'dataSource': {'componentId': 101},
                    'render': {}
                }
            ]
        }
        
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout(invalid_layout)
        
        self.assertEqual(context.exception.field, 'rows[0].items[0].widgetRef')
        self.assertIn('Widget reference "nonexistent-widget" not found', context.exception.message)
    
    def test_invalid_render_config_auto_height(self):
        """Test validation error for invalid autoHeight value."""
        invalid_layout = {
            'grid': {'mode': 'strict-grid', 'gap': 12, 'breakpoints': {}},
            'rows': [],
            'widgets': [
                {
                    'id': 'chart-1',
                    'type': 'bar',
                    'title': 'Chart 1',
                    'dataSource': {'componentId': 101},
                    'render': {'autoHeight': 'invalid'}  # should be boolean
                }
            ]
        }
        
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout(invalid_layout)
        
        self.assertEqual(context.exception.field, 'widgets[0].render.autoHeight')
        self.assertIn('must be a boolean', context.exception.message)
    
    def test_invalid_render_config_aspect_ratio(self):
        """Test validation error for invalid aspectRatio value."""
        invalid_layout = {
            'grid': {'mode': 'strict-grid', 'gap': 12, 'breakpoints': {}},
            'rows': [],
            'widgets': [
                {
                    'id': 'chart-1',
                    'type': 'bar',
                    'title': 'Chart 1',
                    'dataSource': {'componentId': 101},
                    'render': {'aspectRatio': -1}  # should be positive
                }
            ]
        }
        
        with self.assertRaises(LayoutValidationError) as context:
            self.validator.validate_dashboard_layout(invalid_layout)
        
        self.assertEqual(context.exception.field, 'widgets[0].render.aspectRatio')
        self.assertIn('must be a positive number', context.exception.message)
    
    def test_get_default_render_config(self):
        """Test default render configuration for different chart types."""
        # Test known chart type
        bar_config = self.validator._get_default_render_config('bar')
        self.assertEqual(bar_config['aspectRatio'], 0.6)
        self.assertEqual(bar_config['minHeight'], 180)
        self.assertTrue(bar_config['autoHeight'])
        
        # Test radar chart (square aspect ratio)
        radar_config = self.validator._get_default_render_config('radar')
        self.assertEqual(radar_config['aspectRatio'], 1.0)
        self.assertEqual(radar_config['minHeight'], 220)
        
        # Test unknown chart type (should return default)
        unknown_config = self.validator._get_default_render_config('unknown')
        self.assertEqual(unknown_config['aspectRatio'], 0.7)
        self.assertEqual(unknown_config['minHeight'], 200)
        self.assertTrue(unknown_config['autoHeight'])
    
    def test_is_legacy_layout(self):
        """Test legacy layout detection."""
        # New schema layout
        self.assertFalse(self.validator._is_legacy_layout(self.valid_new_layout))
        
        # Legacy layout with components
        self.assertTrue(self.validator._is_legacy_layout(self.valid_legacy_layout))
        
        # Empty layout (considered legacy)
        self.assertTrue(self.validator._is_legacy_layout({}))
        
        # Partial new schema (missing widgets)
        partial_layout = {'grid': {}, 'rows': []}
        self.assertTrue(self.validator._is_legacy_layout(partial_layout))
    
    def test_layout_validation_error_with_suggestions(self):
        """Test LayoutValidationError with suggestions."""
        error = LayoutValidationError(
            'test.field',
            'Test error message',
            ['Suggestion 1', 'Suggestion 2']
        )
        
        self.assertEqual(error.field, 'test.field')
        self.assertEqual(error.message, 'Test error message')
        self.assertEqual(error.suggestions, ['Suggestion 1', 'Suggestion 2'])
        self.assertEqual(str(error), 'test.field: Test error message')
    
    def test_empty_legacy_components_migration(self):
        """Test migration of legacy layout with empty components."""
        empty_legacy = {'components': []}
        result = self.validator.migrate_legacy_layout(empty_legacy)
        
        # Should still create valid structure
        self.assertIn('grid', result)
        self.assertIn('rows', result)
        self.assertIn('widgets', result)
        self.assertEqual(len(result['widgets']), 0)
        self.assertEqual(len(result['rows']), 0)
    
    def test_legacy_component_without_grid_position(self):
        """Test migration of legacy component without grid_position."""
        legacy_layout = {
            'components': [
                {
                    'id': 1,
                    'component_template_id': 101,
                    'chart_type': 'bar',
                    'title': 'Chart without position'
                    # missing grid_position
                }
            ]
        }
        
        result = self.validator.migrate_legacy_layout(legacy_layout)
        
        # Should still create the widget but skip positioning
        self.assertEqual(len(result['widgets']), 1)  # Widget created
        self.assertEqual(len(result['rows']), 0)  # No positioning, so no rows
        
        # Check widget was created with defaults
        widget = result['widgets'][0]
        self.assertEqual(widget['id'], 'widget-1')
        self.assertEqual(widget['type'], 'bar')
        self.assertEqual(widget['title'], 'Chart without position')


if __name__ == '__main__':
    unittest.main()