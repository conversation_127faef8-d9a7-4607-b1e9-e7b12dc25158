from django.contrib import admin
from django import forms
from .models import Page, ComponentTemplate, PageComponent, PageOrganizationAccess, LayoutPreset
from .widgets import CollapsibleJSONWidget, PrettyJSONWidget


class ComponentTemplateForm(forms.ModelForm):
    class Meta:
        model = ComponentTemplate
        fields = '__all__'
        widgets = {
            'api_params': CollapsibleJSONWidget(attrs={'rows': 10}),
        }


@admin.register(ComponentTemplate)
class ComponentTemplateAdmin(admin.ModelAdmin):
    form = ComponentTemplateForm
    list_display = ['id', 'name', 'chart_type', 'api_endpoint', 'is_active', 'created_at']
    list_filter = ['chart_type', 'is_active']
    search_fields = ['name', 'api_endpoint']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = [
        ('기본 정보', {
            'fields': ('name', 'chart_type', 'is_active')
        }),
        ('API 설정', {
            'fields': ('api_endpoint', 'api_params')
        }),
        ('BigQuery 설정', {
            'fields': ('bigquery_sql',)
        }),
        ('시간 정보', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    ]


class PageComponentInline(admin.TabularInline):
    model = PageComponent
    extra = 0
    readonly_fields = ['created_at', 'updated_at']


class PageOrganizationAccessInline(admin.TabularInline):
    model = PageOrganizationAccess
    extra = 0
    readonly_fields = ['granted_at']
    fields = ['organization', 'granted_by', 'granted_at']


class PageForm(forms.ModelForm):
    class Meta:
        model = Page
        fields = '__all__'
        widgets = {
            'layout_config': CollapsibleJSONWidget(attrs={'rows': 15}),
        }


@admin.register(Page)
class PageAdmin(admin.ModelAdmin):
    form = PageForm
    list_display = ['name', 'permission_level', 'created_by', 'is_active', 'created_at']
    list_filter = ['permission_level', 'is_active']
    search_fields = ['name']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [PageComponentInline, PageOrganizationAccessInline]
    
    fieldsets = [
        ('기본 정보', {
            'fields': ('name', 'permission_level', 'is_active')
        }),
        ('레이아웃 설정', {
            'fields': ('layout_config',)
        }),
        ('생성 정보', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    ]


class PageComponentForm(forms.ModelForm):
    class Meta:
        model = PageComponent
        fields = '__all__'
        widgets = {
            'grid_position': PrettyJSONWidget(attrs={'rows': 8}),
            'custom_params': CollapsibleJSONWidget(attrs={'rows': 10}),
        }


@admin.register(PageComponent)
class PageComponentAdmin(admin.ModelAdmin):
    form = PageComponentForm
    list_display = ['id', 'page', 'component_template', 'order', 'is_active', 'created_at']
    list_filter = ['is_active', 'page']
    search_fields = ['page__name', 'component_template__name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = [
        ('기본 정보', {
            'fields': ('page', 'component_template', 'order', 'is_active')
        }),
        ('위치 및 설정', {
            'fields': ('grid_position', 'custom_params')
        }),
        ('시간 정보', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    ]


@admin.register(PageOrganizationAccess)
class PageOrganizationAccessAdmin(admin.ModelAdmin):
    list_display = ['organization', 'page', 'granted_by', 'granted_at']
    list_filter = ['granted_at', 'page__permission_level', 'organization']
    search_fields = ['organization__name', 'page__name', 'granted_by__email']
    readonly_fields = ['granted_at']
    
    fieldsets = [
        ('권한 정보', {
            'fields': ('organization', 'page', 'granted_by')
        }),
        ('시간 정보', {
            'fields': ('granted_at',),
            'classes': ('collapse',)
        }),
    ]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('organization', 'page', 'granted_by')


class LayoutPresetForm(forms.ModelForm):
    class Meta:
        model = LayoutPreset
        fields = '__all__'
        widgets = {
            'layout_template': CollapsibleJSONWidget(attrs={'rows': 15}),
        }


@admin.register(LayoutPreset)
class LayoutPresetAdmin(admin.ModelAdmin):
    form = LayoutPresetForm
    list_display = ['name', 'description', 'is_active', 'created_at']
    list_filter = ['is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = [
        ('기본 정보', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('레이아웃 설정', {
            'fields': ('layout_template',)
        }),
        ('시간 정보', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    ]


