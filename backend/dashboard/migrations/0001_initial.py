# Generated by Django 5.2.5 on 2025-08-08 09:01

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ComponentTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='템플릿명')),
                ('chart_type', models.CharField(choices=[('bar', 'Bar Chart'), ('column', 'Column Chart'), ('line', 'Line Chart'), ('area', 'Area Chart'), ('pie', 'Pie Chart'), ('bubble', 'Bubble Chart'), ('scatter', 'Scatter Chart'), ('heatmap', 'Heatmap'), ('treemap', 'Treemap'), ('radar', 'Radar Chart'), ('boxplot', 'Box Plot'), ('radialbar', 'Radial Bar'), ('gauge', 'Gauge'), ('solidgauge', 'Solid Gauge')], max_length=20, verbose_name='차트 타입')),
                ('api_endpoint', models.CharField(help_text='데이터를 가져올 API 경로', max_length=200, verbose_name='API 엔드포인트')),
                ('api_params', models.JSONField(default=dict, help_text='API 호출 시 사용할 기본 파라미터', verbose_name='API 파라미터')),
                ('bigquery_sql', models.TextField(help_text='데이터 조회를 위한 SQL 쿼리', verbose_name='BigQuery SQL')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='생성일시')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='수정일시')),
                ('is_active', models.BooleanField(default=True, verbose_name='활성 상태')),
            ],
            options={
                'verbose_name': '컴포넌트 템플릿',
                'verbose_name_plural': '컴포넌트 템플릿',
                'db_table': 'component_templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Page',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='페이지명')),
                ('permission_level', models.CharField(choices=[('regular', 'Regular User'), ('org_admin', 'Organization Admin')], max_length=20, verbose_name='권한 레벨')),
                ('layout_config', models.JSONField(default=dict, help_text='그리드 레이아웃 구성 정보', verbose_name='레이아웃 설정')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='생성일시')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='수정일시')),
                ('is_active', models.BooleanField(default=True, verbose_name='활성 상태')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='생성자')),
            ],
            options={
                'verbose_name': '페이지',
                'verbose_name_plural': '페이지',
                'db_table': 'pages',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PageComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grid_position', models.JSONField(help_text='컴포넌트의 그리드 내 위치 및 크기 정보 {x, y, width, height}', verbose_name='그리드 위치')),
                ('custom_params', models.JSONField(default=dict, help_text='이 컴포넌트에만 적용되는 추가 파라미터', verbose_name='커스텀 파라미터')),
                ('order', models.IntegerField(default=0, help_text='페이지 내에서의 컴포넌트 표시 순서', verbose_name='정렬 순서')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='생성일시')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='수정일시')),
                ('is_active', models.BooleanField(default=True, verbose_name='활성 상태')),
                ('component_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.componenttemplate', verbose_name='컴포넌트 템플릿')),
                ('page', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='dashboard.page', verbose_name='페이지')),
            ],
            options={
                'verbose_name': '페이지 컴포넌트',
                'verbose_name_plural': '페이지 컴포넌트',
                'db_table': 'page_components',
                'ordering': ['page', 'order'],
                'unique_together': {('page', 'order')},
            },
        ),
    ]
