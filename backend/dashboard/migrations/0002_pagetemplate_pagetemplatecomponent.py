# Generated by Django 5.2.5 on 2025-08-08 12:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PageTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='템플릿명')),
                ('description', models.TextField(blank=True, help_text='페이지 템플릿에 대한 설명', verbose_name='설명')),
                ('layout_config', models.JSONField(default=dict, help_text='그리드 레이아웃 구성 정보', verbose_name='레이아웃 설정')),
                ('permission_level', models.CharField(choices=[('regular', 'Regular User'), ('org_admin', 'Organization Admin')], max_length=20, verbose_name='권한 레벨')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='생성일시')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='수정일시')),
                ('is_active', models.BooleanField(default=True, verbose_name='활성 상태')),
            ],
            options={
                'verbose_name': '페이지 템플릿',
                'verbose_name_plural': '페이지 템플릿',
                'db_table': 'page_templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PageTemplateComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grid_position', models.JSONField(help_text='컴포넌트의 그리드 내 위치 및 크기 정보 {x, y, width, height}', verbose_name='그리드 위치')),
                ('default_params', models.JSONField(default=dict, help_text='템플릿에서 사용할 기본 파라미터', verbose_name='기본 파라미터')),
                ('order', models.IntegerField(default=0, help_text='템플릿 내에서의 컴포넌트 표시 순서', verbose_name='정렬 순서')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='생성일시')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='수정일시')),
                ('is_active', models.BooleanField(default=True, verbose_name='활성 상태')),
                ('component_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.componenttemplate', verbose_name='컴포넌트 템플릿')),
                ('page_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='template_components', to='dashboard.pagetemplate', verbose_name='페이지 템플릿')),
            ],
            options={
                'verbose_name': '페이지 템플릿 컴포넌트',
                'verbose_name_plural': '페이지 템플릿 컴포넌트',
                'db_table': 'page_template_components',
                'ordering': ['page_template', 'order'],
                'unique_together': {('page_template', 'order')},
            },
        ),
    ]
