# Generated by Django 5.2.5 on 2025-08-15 01:11

from django.db import migrations


def migrate_page_components_to_layout_config(apps, schema_editor):
    """기존 PageComponent 데이터를 Page.layout_config로 마이그레이션"""
    Page = apps.get_model('dashboard', 'Page')
    PageComponent = apps.get_model('dashboard', 'PageComponent')
    ComponentTemplate = apps.get_model('dashboard', 'ComponentTemplate')
    
    # 차트 타입별 기본 렌더링 설정
    chart_render_configs = {
        'radar': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 220},
        'pie': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 200},
        'gauge': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 180},
        'solidgauge': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 180},
        'bar': {'autoHeight': True, 'aspectRatio': 0.6, 'minHeight': 200},
        'column': {'autoHeight': True, 'aspectRatio': 0.6, 'minHeight': 200},
        'line': {'autoHeight': True, 'aspectRatio': 0.5, 'minHeight': 180},
        'area': {'autoHeight': True, 'aspectRatio': 0.5, 'minHeight': 180},
        'heatmap': {'autoHeight': True, 'aspectRatio': 0.8, 'minHeight': 220},
        'treemap': {'autoHeight': True, 'aspectRatio': 0.8, 'minHeight': 200},
        'scatter': {'autoHeight': True, 'aspectRatio': 0.7, 'minHeight': 200},
        'bubble': {'autoHeight': True, 'aspectRatio': 0.7, 'minHeight': 200},
        'boxplot': {'autoHeight': True, 'aspectRatio': 0.6, 'minHeight': 220},
        'radialbar': {'autoHeight': True, 'aspectRatio': 1.0, 'minHeight': 200},
    }
    
    for page in Page.objects.all():
        # 이미 layout_config가 설정된 페이지는 건너뛰기
        if page.layout_config and page.layout_config.get('widgets'):
            continue
            
        # 페이지의 컴포넌트들 가져오기
        components = PageComponent.objects.filter(page=page, is_active=True).order_by('order')
        
        if not components.exists():
            continue
        
        # 기본 레이아웃 설정
        layout_config = {
            "grid": {
                "mode": "strict-grid",
                "gap": 12,
                "breakpoints": {
                    "sm": {"minWidth": 0, "columns": 1, "rowUnit": 220},
                    "md": {"minWidth": 640, "columns": 2, "rowUnit": 240},
                    "lg": {"minWidth": 1024, "columns": 3, "rowUnit": 260},
                    "xl": {"minWidth": 1440, "columns": 4, "rowUnit": 280}
                }
            },
            "rows": [],
            "widgets": []
        }
        
        # 위젯 및 행 아이템 생성
        widgets = []
        row_items = []
        
        for idx, component in enumerate(components):
            widget_id = f"widget-{component.id}"
            
            # Widget 생성
            chart_type = component.component_template.chart_type
            render_config = chart_render_configs.get(chart_type, {
                'autoHeight': True, 
                'aspectRatio': 0.7, 
                'minHeight': 200
            })
            
            widget = {
                "id": widget_id,
                "type": chart_type,
                "title": component.component_template.name,
                "dataSource": {"componentId": component.component_template.id},
                "render": render_config
            }
            widgets.append(widget)
            
            # Row item 생성 (3열 기준으로 배치)
            grid_pos = component.grid_position or {}
            col = (idx % 3) + 1
            span = min(grid_pos.get('width', 1), 3)  # 최대 3까지
            
            row_items.append({
                "widgetRef": widget_id,
                "col": col,
                "span": span,
                "h": 1
            })
        
        # 3개씩 묶어서 행 생성
        rows = []
        for i in range(0, len(row_items), 3):
            row_chunk = row_items[i:i+3]
            # 각 행 아이템의 col 값을 다시 계산
            for j, item in enumerate(row_chunk):
                item['col'] = j + 1
            
            rows.append({
                "id": f"row-{i//3 + 1}",
                "rowHeight": "auto",
                "items": row_chunk
            })
        
        layout_config["rows"] = rows
        layout_config["widgets"] = widgets
        
        # 페이지 업데이트
        page.layout_config = layout_config
        page.save()


def reverse_migration(apps, schema_editor):
    """역방향 마이그레이션 - layout_config 초기화"""
    Page = apps.get_model('dashboard', 'Page')
    
    for page in Page.objects.all():
        page.layout_config = {}
        page.save()


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0004_layoutpreset_componenttemplate_default_render_config_and_more'),
    ]

    operations = [
        migrations.RunPython(migrate_page_components_to_layout_config, reverse_migration),
    ]
