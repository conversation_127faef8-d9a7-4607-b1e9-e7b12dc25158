# Generated by Django 5.2.5 on 2025-08-15 01:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0003_pageorganizationaccess_page_accessible_organizations'),
    ]

    operations = [
        migrations.CreateModel(
            name='LayoutPreset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='프리셋명')),
                ('description', models.TextField(blank=True, help_text='레이아웃 프리셋에 대한 설명', verbose_name='설명')),
                ('layout_template', models.JSONField(help_text='DashboardLayout 스키마 (widgets 제외)', verbose_name='레이아웃 템플릿')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='생성일시')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='수정일시')),
                ('is_active', models.BooleanField(default=True, verbose_name='활성 상태')),
            ],
            options={
                'verbose_name': '레이아웃 프리셋',
                'verbose_name_plural': '레이아웃 프리셋',
                'db_table': 'layout_presets',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='componenttemplate',
            name='default_render_config',
            field=models.JSONField(default=dict, help_text='WidgetRenderConfig 스키마: {autoHeight, aspectRatio, minHeight, maxHeight, fixedHeight}', verbose_name='기본 렌더링 설정'),
        ),
        migrations.AlterField(
            model_name='page',
            name='layout_config',
            field=models.JSONField(default=dict, help_text='DashboardLayout 스키마 준수: {grid, rows, widgets}', verbose_name='레이아웃 설정'),
        ),
        migrations.CreateModel(
            name='WidgetPreset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='위젯 프리셋명')),
                ('chart_type', models.CharField(choices=[('bar', 'Bar Chart'), ('column', 'Column Chart'), ('line', 'Line Chart'), ('area', 'Area Chart'), ('pie', 'Pie Chart'), ('bubble', 'Bubble Chart'), ('scatter', 'Scatter Chart'), ('heatmap', 'Heatmap'), ('treemap', 'Treemap'), ('radar', 'Radar Chart'), ('boxplot', 'Box Plot'), ('radialbar', 'Radial Bar'), ('gauge', 'Gauge'), ('solidgauge', 'Solid Gauge')], max_length=20, verbose_name='차트 타입')),
                ('default_render_config', models.JSONField(default=dict, help_text='WidgetRenderConfig 스키마', verbose_name='기본 렌더링 설정')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='생성일시')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='수정일시')),
                ('is_active', models.BooleanField(default=True, verbose_name='활성 상태')),
                ('component_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.componenttemplate', verbose_name='컴포넌트 템플릿')),
            ],
            options={
                'verbose_name': '위젯 프리셋',
                'verbose_name_plural': '위젯 프리셋',
                'db_table': 'widget_presets',
                'ordering': ['name'],
            },
        ),
    ]
