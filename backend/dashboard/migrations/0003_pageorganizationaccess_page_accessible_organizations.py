# Generated by Django 5.2.5 on 2025-08-11 12:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('dashboard', '0002_pagetemplate_pagetemplatecomponent'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PageOrganizationAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('granted_at', models.DateTimeField(auto_now_add=True, verbose_name='권한 부여일시')),
                ('granted_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_organization_accesses', to=settings.AUTH_USER_MODEL, verbose_name='권한 부여자')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.organization', verbose_name='조직')),
                ('page', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.page', verbose_name='페이지')),
            ],
            options={
                'verbose_name': '페이지 조직 접근 권한',
                'verbose_name_plural': '페이지 조직 접근 권한',
                'db_table': 'page_organization_accesses',
                'ordering': ['-granted_at'],
                'unique_together': {('organization', 'page')},
            },
        ),
        migrations.AddField(
            model_name='page',
            name='accessible_organizations',
            field=models.ManyToManyField(blank=True, related_name='accessible_pages', through='dashboard.PageOrganizationAccess', to='accounts.organization', verbose_name='접근 가능 조직'),
        ),
    ]
