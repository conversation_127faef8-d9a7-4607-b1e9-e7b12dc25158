# Generated by Django 5.2.5 on 2025-08-17 00:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0005_auto_20250815_1011'),
    ]

    operations = [
        migrations.AddField(
            model_name='page',
            name='layout_version',
            field=models.CharField(choices=[('1.0', 'Legacy'), ('2.0', 'New Schema')], default='2.0', help_text='레이아웃 설정의 스키마 버전 (1.0: 레거시, 2.0: 새 스키마)', max_length=10, verbose_name='레이아웃 스키마 버전'),
        ),
        migrations.AlterField(
            model_name='pagecomponent',
            name='custom_params',
            field=models.JSONField(blank=True, default=dict, help_text='이 컴포넌트에만 적용되는 추가 파라미터', verbose_name='커스텀 파라미터'),
        ),
    ]
