from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    # 페이지 관련 URL
    path('pages/', views.PageListView.as_view(), name='page-list'),
    path('pages/<int:pk>/', views.PageDetailView.as_view(), name='page-detail'),
    path('pages/accessible/', views.user_accessible_pages, name='user-accessible-pages'),
    path('pages/from-template/', views.create_page_from_template, name='create-page-from-template'),
    
    # 페이지 템플릿 관련 URL
    path('page-templates/', views.PageTemplateListView.as_view(), name='page-template-list'),
    path('page-templates/<int:pk>/', views.PageTemplateDetailView.as_view(), name='page-template-detail'),
    
    # 페이지 템플릿 컴포넌트 관련 URL
    path('page-templates/<int:template_id>/components/', views.PageTemplateComponentListView.as_view(), name='page-template-component-list'),
    path('page-templates/<int:template_id>/components/<int:pk>/', views.PageTemplateComponentDetailView.as_view(), name='page-template-component-detail'),
    
    # 컴포넌트 템플릿 관련 URL
    path('component-templates/', views.ComponentTemplateListView.as_view(), name='component-template-list'),
    path('component-templates/<int:pk>/', views.ComponentTemplateDetailView.as_view(), name='component-template-detail'),
    
    # 페이지 컴포넌트 관련 URL
    path('pages/<int:page_id>/components/', views.PageComponentListView.as_view(), name='page-component-list'),
    path('pages/<int:page_id>/components/<int:pk>/', views.PageComponentDetailView.as_view(), name='page-component-detail'),
    
    # 컴포넌트 데이터 URL
    path('components/<int:component_id>/data/', views.component_data_view, name='component-data'),
    
    # 페이지 접근 권한 관리 URL (관리자 전용)
    path('admin/pages/<int:page_id>/organizations/<int:org_id>/access/', views.grant_page_access, name='grant-page-access'),
    path('admin/pages/<int:page_id>/organizations/<int:org_id>/access/revoke/', views.revoke_page_access, name='revoke-page-access'),
    path('admin/pages/<int:page_id>/organizations/', views.page_accessible_organizations, name='page-accessible-organizations'),
    path('admin/organizations/<int:org_id>/pages/', views.organization_accessible_pages_admin, name='organization-accessible-pages-admin'),
]