from __future__ import annotations

import time
from typing import Any, Dict, List

from decouple import config
from google.api_core.exceptions import GoogleAP<PERSON>allError, RetryError
from google.cloud import bigquery


class BigQueryService:
    """Service class for interacting with Google BigQuery."""

    def __init__(self, project_id: str | None = None) -> None:
        self.project_id = project_id or config('BIGQUERY_PROJECT_ID', default=None)
        # Credentials are automatically picked from GOOGLE_APPLICATION_CREDENTIALS
        self.client = bigquery.Client(project=self.project_id)

    def _build_query_params(self, params: Dict[str, Any]) -> List[bigquery.ScalarQueryParameter]:
        query_params: List[bigquery.ScalarQueryParameter] = []
        for key, value in params.items():
            if isinstance(value, bool):
                param_type = 'BOOL'
            elif isinstance(value, int):
                param_type = 'INT64'
            elif isinstance(value, float):
                param_type = 'FLOAT64'
            else:
                param_type = 'STRING'
            query_params.append(bigquery.ScalarQueryParameter(key, param_type, value))
        return query_params

    def run_query(self, sql: str, params: Dict[str, Any] | None = None, retries: int = 3) -> List[Dict[str, Any]]:
        """Execute a SQL query against BigQuery and return the results."""
        job_config = bigquery.QueryJobConfig()
        if params:
            job_config.query_parameters = self._build_query_params(params)

        last_exception: Exception | None = None
        for _ in range(retries):
            try:
                query_job = self.client.query(sql, job_config=job_config)
                rows = query_job.result()
                return [dict(row) for row in rows]
            except (GoogleAPICallError, RetryError, Exception) as exc:  # pragma: no cover
                last_exception = exc
                time.sleep(1)
        raise RuntimeError(f'BigQuery query failed: {last_exception}')

    def build_time_aggregation_query(
        self,
        base_sql: str,
        time_column: str = 'timestamp',
        granularity: str = 'day',
        method: str = 'sum',
        value_column: str = 'value',
    ) -> str:
        """Wrap the base SQL with time based aggregation logic."""
        granularity_map = {
            'minute': 'MINUTE',
            'hour': 'HOUR',
            'day': 'DAY',
            'month': 'MONTH',
        }
        gran = granularity_map.get(granularity, 'DAY')
        time_trunc = f"TIMESTAMP_TRUNC({time_column}, {gran})"

        if method == 'window':
            return (
                f"SELECT {time_trunc} AS period, "
                f"SUM({value_column}) OVER (ORDER BY {time_trunc}) AS {value_column} "
                f"FROM ({base_sql})"
            )
        return (
            f"SELECT {time_trunc} AS period, SUM({value_column}) AS {value_column} "
            f"FROM ({base_sql}) GROUP BY period ORDER BY period"
        )


def convert_to_chart_data(rows: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Convert BigQuery rows to a generic chart data structure."""
    if not rows:
        return {'categories': [], 'series': []}

    field_names = list(rows[0].keys())
    category_field = field_names[0]
    value_fields = field_names[1:]
    categories: List[Any] = []
    series_map: Dict[str, List[Any]] = {field: [] for field in value_fields}

    for row in rows:
        categories.append(str(row[category_field]))
        for field in value_fields:
            series_map[field].append(row[field])

    series = [{'name': name, 'data': data} for name, data in series_map.items()]
    return {'categories': categories, 'series': series}


class LayoutValidationError(Exception):
    """Exception raised for layout configuration validation errors."""
    
    def __init__(self, field: str, message: str, suggestions: List[str] = None):
        self.field = field
        self.message = message
        self.suggestions = suggestions or []
        super().__init__(f"{field}: {message}")


class LayoutConfigValidator:
    """Service class for validating and migrating dashboard layout configurations."""
    
    # Chart type specific default render configurations
    CHART_TYPE_DEFAULTS = {
        'radar': {'aspectRatio': 1.0, 'minHeight': 220, 'autoHeight': True},
        'pie': {'aspectRatio': 1.0, 'minHeight': 200, 'autoHeight': True},
        'gauge': {'aspectRatio': 1.0, 'minHeight': 180, 'autoHeight': True},
        'solidgauge': {'aspectRatio': 1.0, 'minHeight': 180, 'autoHeight': True},
        'bar': {'aspectRatio': 0.6, 'minHeight': 180, 'autoHeight': True},
        'column': {'aspectRatio': 0.6, 'minHeight': 180, 'autoHeight': True},
        'line': {'aspectRatio': 0.5, 'minHeight': 160, 'autoHeight': True},
        'area': {'aspectRatio': 0.5, 'minHeight': 160, 'autoHeight': True},
        'heatmap': {'aspectRatio': 0.8, 'minHeight': 200, 'autoHeight': True},
        'treemap': {'aspectRatio': 0.8, 'minHeight': 200, 'autoHeight': True},
        'bubble': {'aspectRatio': 0.7, 'minHeight': 200, 'autoHeight': True},
        'scatter': {'aspectRatio': 0.7, 'minHeight': 200, 'autoHeight': True},
        'boxplot': {'aspectRatio': 0.7, 'minHeight': 200, 'autoHeight': True},
        'radialbar': {'aspectRatio': 1.0, 'minHeight': 200, 'autoHeight': True},
    }
    
    def validate_dashboard_layout(self, layout_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a DashboardLayout configuration and return normalized version.
        
        Args:
            layout_config: The layout configuration to validate
            
        Returns:
            Dict containing the validated and normalized layout configuration
            
        Raises:
            LayoutValidationError: If the layout configuration is invalid
        """
        if not isinstance(layout_config, dict):
            raise LayoutValidationError(
                'layout_config', 
                'Layout configuration must be a dictionary',
                ['Ensure the layout_config field contains a valid JSON object']
            )
        
        # Check if this is a legacy layout that needs migration
        if self._is_legacy_layout(layout_config):
            return self.migrate_legacy_layout(layout_config)
        
        # Validate new DashboardLayout schema
        validated_config = {}
        
        # Validate grid configuration
        validated_config['grid'] = self._validate_grid_config(
            layout_config.get('grid', {})
        )
        
        # Validate rows configuration
        validated_config['rows'] = self._validate_rows_config(
            layout_config.get('rows', [])
        )
        
        # Validate widgets configuration
        validated_config['widgets'] = self._validate_widgets_config(
            layout_config.get('widgets', [])
        )
        
        # Cross-validate widget references
        self._validate_widget_references(validated_config['rows'], validated_config['widgets'])
        
        return validated_config
    
    def migrate_legacy_layout(self, legacy_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert legacy grid_position format to new DashboardLayout schema.
        
        Args:
            legacy_config: Legacy layout configuration with grid_position format
            
        Returns:
            Dict containing the migrated layout in new DashboardLayout schema
        """
        # Default grid configuration for migrated layouts
        migrated_config = {
            'grid': {
                'mode': 'strict-grid',
                'gap': 12,
                'breakpoints': {
                    'sm': {'minWidth': 0, 'columns': 1, 'rowUnit': 220},
                    'md': {'minWidth': 640, 'columns': 2, 'rowUnit': 240},
                    'lg': {'minWidth': 1024, 'columns': 3, 'rowUnit': 260},
                    'xl': {'minWidth': 1440, 'columns': 4, 'rowUnit': 280}
                }
            },
            'rows': [],
            'widgets': []
        }
        
        # If legacy_config has components with grid_position, convert them
        if 'components' in legacy_config:
            components = legacy_config['components']
            if isinstance(components, list):
                # Group components by row (y position)
                rows_map = {}
                widgets = []
                
                for i, component in enumerate(components):
                    if not isinstance(component, dict):
                        continue
                    
                    # Create widget regardless of grid_position
                    widget_id = f"widget-{i+1}"
                    widget = {
                        'id': widget_id,
                        'type': component.get('chart_type', 'bar'),
                        'title': component.get('title', f'Chart {i+1}'),
                        'dataSource': {
                            'componentId': component.get('component_template_id', component.get('id', i+1))
                        },
                        'render': self._get_default_render_config(component.get('chart_type', 'bar'))
                    }
                    widgets.append(widget)
                    
                    # Only add to rows if grid_position is valid
                    grid_pos = component.get('grid_position', {})
                    if isinstance(grid_pos, dict) and all(key in grid_pos for key in ['x', 'y', 'width', 'height']):
                        # Extract position information
                        x = grid_pos.get('x', 0)
                        y = grid_pos.get('y', 0)
                        width = grid_pos.get('width', 1)
                        height = grid_pos.get('height', 1)
                        
                        # Group by row
                        if y not in rows_map:
                            rows_map[y] = []
                        
                        rows_map[y].append({
                            'widgetRef': widget_id,
                            'col': x + 1,  # Convert 0-based to 1-based
                            'span': width,
                            'h': height
                        })
                
                # Create rows from grouped components
                for y in sorted(rows_map.keys()):
                    row = {
                        'id': f'row-{y+1}',
                        'rowHeight': 'auto',
                        'items': sorted(rows_map[y], key=lambda item: item['col'])
                    }
                    migrated_config['rows'].append(row)
                
                migrated_config['widgets'] = widgets
        
        return migrated_config
    
    def _is_legacy_layout(self, layout_config: Dict[str, Any]) -> bool:
        """Check if the layout configuration uses legacy format."""
        # Legacy layouts typically have 'components' with 'grid_position'
        # or missing the new schema fields (grid, rows, widgets)
        has_new_schema = all(key in layout_config for key in ['grid', 'rows', 'widgets'])
        has_legacy_components = 'components' in layout_config
        
        return not has_new_schema or has_legacy_components
    
    def _validate_grid_config(self, grid_config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate grid configuration."""
        if not isinstance(grid_config, dict):
            raise LayoutValidationError(
                'grid',
                'Grid configuration must be a dictionary',
                ['Provide a valid grid configuration object']
            )
        
        validated_grid = {
            'mode': grid_config.get('mode', 'strict-grid'),
            'gap': grid_config.get('gap', 12),
            'breakpoints': {}
        }
        
        # Validate mode
        valid_modes = ['strict-grid', 'flexible-grid']
        if validated_grid['mode'] not in valid_modes:
            raise LayoutValidationError(
                'grid.mode',
                f'Invalid grid mode. Must be one of: {valid_modes}',
                [f'Use one of the supported modes: {", ".join(valid_modes)}']
            )
        
        # Validate gap
        if not isinstance(validated_grid['gap'], (int, float)) or validated_grid['gap'] < 0:
            raise LayoutValidationError(
                'grid.gap',
                'Gap must be a non-negative number',
                ['Provide a valid gap value (e.g., 12, 16, 20)']
            )
        
        # Validate breakpoints
        breakpoints = grid_config.get('breakpoints', {})
        if not isinstance(breakpoints, dict):
            raise LayoutValidationError(
                'grid.breakpoints',
                'Breakpoints must be a dictionary',
                ['Provide breakpoint configurations for different screen sizes']
            )
        
        # Default breakpoints if none provided
        if not breakpoints:
            breakpoints = {
                'sm': {'minWidth': 0, 'columns': 1, 'rowUnit': 220},
                'md': {'minWidth': 640, 'columns': 2, 'rowUnit': 240},
                'lg': {'minWidth': 1024, 'columns': 3, 'rowUnit': 260},
                'xl': {'minWidth': 1440, 'columns': 4, 'rowUnit': 280}
            }
        
        for bp_name, bp_config in breakpoints.items():
            validated_grid['breakpoints'][bp_name] = self._validate_breakpoint_config(
                bp_name, bp_config
            )
        
        return validated_grid
    
    def _validate_breakpoint_config(self, name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate individual breakpoint configuration."""
        if not isinstance(config, dict):
            raise LayoutValidationError(
                f'grid.breakpoints.{name}',
                'Breakpoint configuration must be a dictionary',
                ['Provide minWidth, columns, and rowUnit for each breakpoint']
            )
        
        required_fields = ['minWidth', 'columns', 'rowUnit']
        for field in required_fields:
            if field not in config:
                raise LayoutValidationError(
                    f'grid.breakpoints.{name}.{field}',
                    f'Missing required field: {field}',
                    [f'Add {field} to the breakpoint configuration']
                )
            
            if not isinstance(config[field], (int, float)) or config[field] < 0:
                raise LayoutValidationError(
                    f'grid.breakpoints.{name}.{field}',
                    f'{field} must be a non-negative number',
                    [f'Provide a valid {field} value']
                )
        
        return {
            'minWidth': config['minWidth'],
            'columns': config['columns'],
            'rowUnit': config['rowUnit']
        }
    
    def _validate_rows_config(self, rows_config: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate rows configuration."""
        if not isinstance(rows_config, list):
            raise LayoutValidationError(
                'rows',
                'Rows configuration must be a list',
                ['Provide an array of row configurations']
            )
        
        validated_rows = []
        row_ids = set()
        
        for i, row in enumerate(rows_config):
            if not isinstance(row, dict):
                raise LayoutValidationError(
                    f'rows[{i}]',
                    'Row configuration must be a dictionary',
                    ['Each row should have id, rowHeight, and items fields']
                )
            
            # Validate row ID
            row_id = row.get('id')
            if not row_id or not isinstance(row_id, str):
                raise LayoutValidationError(
                    f'rows[{i}].id',
                    'Row ID must be a non-empty string',
                    ['Provide a unique ID for each row (e.g., "row-1", "header-row")']
                )
            
            if row_id in row_ids:
                raise LayoutValidationError(
                    f'rows[{i}].id',
                    f'Duplicate row ID: {row_id}',
                    ['Ensure each row has a unique ID']
                )
            row_ids.add(row_id)
            
            # Validate rowHeight
            row_height = row.get('rowHeight', 'auto')
            if not (row_height == 'auto' or isinstance(row_height, (int, float))):
                raise LayoutValidationError(
                    f'rows[{i}].rowHeight',
                    'Row height must be "auto" or a number',
                    ['Use "auto" for flexible height or a pixel value (e.g., 300)']
                )
            
            # Validate items
            items = row.get('items', [])
            if not isinstance(items, list):
                raise LayoutValidationError(
                    f'rows[{i}].items',
                    'Row items must be a list',
                    ['Provide an array of item configurations for the row']
                )
            
            validated_items = []
            for j, item in enumerate(items):
                validated_items.append(self._validate_row_item(i, j, item))
            
            validated_rows.append({
                'id': row_id,
                'rowHeight': row_height,
                'items': validated_items
            })
        
        return validated_rows
    
    def _validate_row_item(self, row_index: int, item_index: int, item: Dict[str, Any]) -> Dict[str, Any]:
        """Validate individual row item configuration."""
        if not isinstance(item, dict):
            raise LayoutValidationError(
                f'rows[{row_index}].items[{item_index}]',
                'Row item must be a dictionary',
                ['Each item should have widgetRef, col, span, and h fields']
            )
        
        required_fields = ['widgetRef', 'col', 'span', 'h']
        for field in required_fields:
            if field not in item:
                raise LayoutValidationError(
                    f'rows[{row_index}].items[{item_index}].{field}',
                    f'Missing required field: {field}',
                    [f'Add {field} to the item configuration']
                )
        
        # Validate widgetRef
        widget_ref = item['widgetRef']
        if not isinstance(widget_ref, str) or not widget_ref:
            raise LayoutValidationError(
                f'rows[{row_index}].items[{item_index}].widgetRef',
                'Widget reference must be a non-empty string',
                ['Provide a valid widget ID reference']
            )
        
        # Validate numeric fields
        for field in ['col', 'span', 'h']:
            value = item[field]
            if not isinstance(value, int) or value < 1:
                raise LayoutValidationError(
                    f'rows[{row_index}].items[{item_index}].{field}',
                    f'{field} must be a positive integer',
                    [f'Provide a valid {field} value (minimum 1)']
                )
        
        return {
            'widgetRef': widget_ref,
            'col': item['col'],
            'span': item['span'],
            'h': item['h']
        }
    
    def _validate_widgets_config(self, widgets_config: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate widgets configuration."""
        if not isinstance(widgets_config, list):
            raise LayoutValidationError(
                'widgets',
                'Widgets configuration must be a list',
                ['Provide an array of widget configurations']
            )
        
        validated_widgets = []
        widget_ids = set()
        
        for i, widget in enumerate(widgets_config):
            if not isinstance(widget, dict):
                raise LayoutValidationError(
                    f'widgets[{i}]',
                    'Widget configuration must be a dictionary',
                    ['Each widget should have id, type, title, dataSource, and render fields']
                )
            
            # Validate widget ID
            widget_id = widget.get('id')
            if not widget_id or not isinstance(widget_id, str):
                raise LayoutValidationError(
                    f'widgets[{i}].id',
                    'Widget ID must be a non-empty string',
                    ['Provide a unique ID for each widget (e.g., "chart-1", "kpi-widget")']
                )
            
            if widget_id in widget_ids:
                raise LayoutValidationError(
                    f'widgets[{i}].id',
                    f'Duplicate widget ID: {widget_id}',
                    ['Ensure each widget has a unique ID']
                )
            widget_ids.add(widget_id)
            
            # Validate widget type
            widget_type = widget.get('type')
            if not widget_type or not isinstance(widget_type, str):
                raise LayoutValidationError(
                    f'widgets[{i}].type',
                    'Widget type must be a non-empty string',
                    ['Specify a valid chart type (e.g., "bar", "line", "pie")']
                )
            
            # Validate title
            title = widget.get('title', '')
            if not isinstance(title, str):
                raise LayoutValidationError(
                    f'widgets[{i}].title',
                    'Widget title must be a string',
                    ['Provide a descriptive title for the widget']
                )
            
            # Validate dataSource
            data_source = widget.get('dataSource', {})
            if not isinstance(data_source, dict):
                raise LayoutValidationError(
                    f'widgets[{i}].dataSource',
                    'Widget dataSource must be a dictionary',
                    ['Provide dataSource configuration with componentId']
                )
            
            if 'componentId' not in data_source:
                raise LayoutValidationError(
                    f'widgets[{i}].dataSource.componentId',
                    'Missing componentId in dataSource',
                    ['Add componentId to specify the data source component']
                )
            
            # Validate render configuration
            render_config = widget.get('render', {})
            validated_render = self._validate_render_config(i, render_config, widget_type)
            
            validated_widgets.append({
                'id': widget_id,
                'type': widget_type,
                'title': title,
                'dataSource': data_source,
                'render': validated_render
            })
        
        return validated_widgets
    
    def _validate_render_config(self, widget_index: int, render_config: Dict[str, Any], chart_type: str) -> Dict[str, Any]:
        """Validate widget render configuration."""
        if not isinstance(render_config, dict):
            raise LayoutValidationError(
                f'widgets[{widget_index}].render',
                'Render configuration must be a dictionary',
                ['Provide render configuration with sizing and display options']
            )
        
        # Get default configuration for chart type
        default_config = self._get_default_render_config(chart_type)
        validated_render = default_config.copy()
        
        # Override with provided configuration
        for key, value in render_config.items():
            if key == 'autoHeight':
                if not isinstance(value, bool):
                    raise LayoutValidationError(
                        f'widgets[{widget_index}].render.autoHeight',
                        'autoHeight must be a boolean',
                        ['Use true for automatic height or false for fixed height']
                    )
                validated_render['autoHeight'] = value
            
            elif key == 'aspectRatio':
                if not isinstance(value, (int, float)) or value <= 0:
                    raise LayoutValidationError(
                        f'widgets[{widget_index}].render.aspectRatio',
                        'aspectRatio must be a positive number',
                        ['Provide aspect ratio as width/height (e.g., 1.0 for square, 0.6 for wide)']
                    )
                validated_render['aspectRatio'] = value
            
            elif key in ['minHeight', 'maxHeight', 'fixedHeight']:
                if not isinstance(value, (int, float)) or value <= 0:
                    raise LayoutValidationError(
                        f'widgets[{widget_index}].render.{key}',
                        f'{key} must be a positive number',
                        [f'Provide {key} in pixels (e.g., 200, 300)']
                    )
                validated_render[key] = value
            
            elif key == 'overflow':
                valid_overflow = ['hidden', 'scroll', 'visible']
                if value not in valid_overflow:
                    raise LayoutValidationError(
                        f'widgets[{widget_index}].render.overflow',
                        f'Invalid overflow value. Must be one of: {valid_overflow}',
                        [f'Use one of: {", ".join(valid_overflow)}']
                    )
                validated_render['overflow'] = value
            
            elif key == 'padding':
                if not isinstance(value, (int, float)) or value < 0:
                    raise LayoutValidationError(
                        f'widgets[{widget_index}].render.padding',
                        'padding must be a non-negative number',
                        ['Provide padding in pixels (e.g., 8, 12, 16)']
                    )
                validated_render['padding'] = value
        
        return validated_render
    
    def _validate_widget_references(self, rows: List[Dict[str, Any]], widgets: List[Dict[str, Any]]) -> None:
        """Validate that all widget references in rows exist in widgets."""
        widget_ids = {widget['id'] for widget in widgets}
        
        for i, row in enumerate(rows):
            for j, item in enumerate(row['items']):
                widget_ref = item['widgetRef']
                if widget_ref not in widget_ids:
                    raise LayoutValidationError(
                        f'rows[{i}].items[{j}].widgetRef',
                        f'Widget reference "{widget_ref}" not found in widgets',
                        [f'Add a widget with id "{widget_ref}" or update the reference']
                    )
    
    def _get_default_render_config(self, chart_type: str) -> Dict[str, Any]:
        """Get default render configuration for a chart type."""
        return self.CHART_TYPE_DEFAULTS.get(chart_type, {
            'aspectRatio': 0.7,
            'minHeight': 200,
            'autoHeight': True,
            'overflow': 'hidden',
            'padding': 12
        })
