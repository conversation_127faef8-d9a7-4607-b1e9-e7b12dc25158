from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.exceptions import PermissionDenied, NotFound, APIException
from django.db.models import Q
from .models import Page, ComponentTemplate, PageComponent, PageTemplate, PageTemplateComponent, PageOrganizationAccess
from .serializers import (
    PageSerializer, PageListSerializer, PageCreateSerializer,
    ComponentTemplateSerializer, PageComponentSerializer,
    PageTemplateSerializer, PageTemplateCreateSerializer, PageFromTemplateSerializer,
    PageTemplateComponentSerializer
)
from accounts.permissions import (
    IsAdminUser, CanAccessPage
)
from accounts.models import User, Organization
from .services import BigQueryService, convert_to_chart_data


def get_mock_chart_data(chart_type):
    """차트 타입별 목 데이터 생성"""
    if chart_type in ['line', 'area', 'bar', 'column']:
        return {
            'categories': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'series': [
                {'name': 'Series A', 'data': [23, 45, 56, 78, 89, 76]},
                {'name': 'Series B', 'data': [12, 34, 45, 67, 78, 65]},
            ]
        }
    elif chart_type == 'pie':
        return {
            'series': [
                {'name': 'Chrome', 'data': 46.2},
                {'name': 'Safari', 'data': 20.1},
                {'name': 'Firefox', 'data': 15.8},
                {'name': 'Edge', 'data': 10.3},
                {'name': 'Others', 'data': 7.6},
            ]
        }
    elif chart_type == 'scatter':
        return {
            'series': [
                {'name': 'Dataset A', 'data': [[10, 20], [15, 25], [20, 30], [25, 35], [30, 40]]},
                {'name': 'Dataset B', 'data': [[12, 18], [18, 28], [22, 32], [28, 38], [32, 42]]},
            ]
        }
    elif chart_type == 'bubble':
        return {
            'series': [
                {'name': 'Bubble A', 'data': [[10, 20, 15], [15, 25, 20], [20, 30, 25]]},
                {'name': 'Bubble B', 'data': [[12, 18, 18], [18, 28, 22], [22, 32, 28]]},
            ]
        }
    elif chart_type == 'heatmap':
        return {
            'categories': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            'series': [
                {'name': 'Week 1', 'data': [1, 2, 3, 4, 5, 3, 2]},
                {'name': 'Week 2', 'data': [2, 3, 4, 5, 6, 4, 3]},
                {'name': 'Week 3', 'data': [3, 4, 5, 6, 7, 5, 4]},
                {'name': 'Week 4', 'data': [4, 5, 6, 7, 8, 6, 5]},
            ]
        }
    elif chart_type == 'radar':
        return {
            'categories': ['Speed', 'Reliability', 'Comfort', 'Safety', 'Efficiency'],
            'series': [
                {'name': 'Model A', 'data': [80, 90, 70, 85, 75]},
                {'name': 'Model B', 'data': [75, 85, 80, 90, 85]},
            ]
        }
    elif chart_type == 'boxplot':
        return {
            'categories': ['Q1', 'Q2', 'Q3', 'Q4'],
            'series': [
                {'name': 'Sales', 'data': [[10, 20, 30, 40, 50], [15, 25, 35, 45, 55], [5, 15, 25, 35, 45], [20, 30, 40, 50, 60]]},
            ]
        }
    elif chart_type == 'radialbar':
        return {
            'categories': ['Progress'],
            'series': [
                {'name': 'Completion', 'data': [75]},
            ]
        }
    elif chart_type in ['gauge', 'solidgauge']:
        return {
            'categories': ['Performance'],
            'series': [
                {'name': 'Score', 'data': [68]},
            ]
        }
    elif chart_type == 'treemap':
        return {
            'series': [
                {
                    'name': 'tree',
                    'data': [
                        {
                            'label': 'Category A',
                            'children': [
                                {'label': 'Sub A1', 'value': 30},
                                {'label': 'Sub A2', 'value': 25},
                            ]
                        },
                        {
                            'label': 'Category B',
                            'children': [
                                {'label': 'Sub B1', 'value': 20},
                                {'label': 'Sub B2', 'value': 25},
                            ]
                        }
                    ]
                }
            ]
        }
    else:
        # Default fallback
        return {
            'categories': ['Sample'],
            'series': [{'name': 'Data', 'data': [42]}]
        }


class PageListView(generics.ListCreateAPIView):
    """페이지 목록 조회 및 생성"""
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return PageCreateSerializer
        return PageListSerializer

    def get_queryset(self):
        """사용자 권한에 따른 페이지 필터링 (권한 레벨 + 개별 권한)"""
        user = self.request.user
        
        if user.is_super_admin():
            # 슈퍼 관리자는 모든 페이지 접근 가능
            return Page.objects.filter(is_active=True)
        
        # 권한 레벨 기반 필터링
        role_based_q = Q()
        if user.is_org_admin():
            role_based_q = Q(permission_level__in=['regular', 'org_admin'])
        else:
            role_based_q = Q(permission_level='regular')
        
        # 조직 기반 접근 권한이 있는 페이지 필터링
        org_access_q = Q()
        if user.organization:
            org_access_q = Q(accessible_organizations=user.organization)
        
        # 권한 레벨 또는 조직 접근 권한이 있는 페이지 반환
        return Page.objects.filter(
            (role_based_q | org_access_q) & Q(is_active=True)
        ).distinct()

    def get_permissions(self):
        """페이지 생성은 관리자만 가능"""
        if self.request.method == 'POST':
            return [permissions.IsAuthenticated(), IsAdminUser()]
        return [permissions.IsAuthenticated()]


class PageDetailView(generics.RetrieveUpdateDestroyAPIView):
    """페이지 상세 조회, 수정, 삭제"""
    serializer_class = PageSerializer

    def get_queryset(self):
        """사용자 권한에 따른 페이지 필터링 (권한 레벨 + 개별 권한)"""
        user = self.request.user

        if user.is_super_admin():
            return Page.objects.filter(is_active=True)
        
        # 권한 레벨 기반 필터링
        role_based_q = Q()
        if user.is_org_admin():
            role_based_q = Q(permission_level__in=['regular', 'org_admin'])
        else:
            role_based_q = Q(permission_level='regular')
        
        # 조직 기반 접근 권한이 있는 페이지 필터링
        org_access_q = Q()
        if user.organization:
            org_access_q = Q(accessible_organizations=user.organization)
        
        # 권한 레벨 또는 조직 접근 권한이 있는 페이지 반환
        return Page.objects.filter(
            (role_based_q | org_access_q) & Q(is_active=True)
        ).distinct()

    def get_permissions(self):
        """조회는 권한 기반, 수정/삭제는 관리자만 가능"""
        perms = [permissions.IsAuthenticated(), CanAccessPage()]
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            perms.append(IsAdminUser())
        return perms


class ComponentTemplateListView(generics.ListCreateAPIView):
    """컴포넌트 템플릿 목록 조회 및 생성 (관리자 전용)"""
    queryset = ComponentTemplate.objects.filter(is_active=True)
    serializer_class = ComponentTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]


class ComponentTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """컴포넌트 템플릿 상세 조회, 수정, 삭제 (관리자 전용)"""
    queryset = ComponentTemplate.objects.filter(is_active=True)
    serializer_class = ComponentTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]


class PageComponentListView(generics.ListCreateAPIView):
    """페이지 컴포넌트 목록 조회 및 생성 (관리자 전용)"""
    serializer_class = PageComponentSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        """특정 페이지의 컴포넌트 목록"""
        page_id = self.kwargs.get('page_id')
        return PageComponent.objects.filter(
            page_id=page_id,
            is_active=True
        ).order_by('order')

    def perform_create(self, serializer):
        """페이지 ID를 자동으로 설정"""
        page_id = self.kwargs.get('page_id')
        try:
            page = Page.objects.get(id=page_id, is_active=True)
            serializer.save(page=page)
        except Page.DoesNotExist:
            raise NotFound('페이지를 찾을 수 없습니다.')


class PageComponentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """페이지 컴포넌트 상세 조회, 수정, 삭제"""
    serializer_class = PageComponentSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        """특정 페이지의 컴포넌트"""
        page_id = self.kwargs.get('page_id')
        return PageComponent.objects.filter(
            page_id=page_id, 
            is_active=True
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_accessible_pages(request):
    """현재 사용자가 접근 가능한 페이지 목록 (권한 레벨 + 개별 권한)"""
    user = request.user
    
    if user.is_super_admin():
        pages = Page.objects.filter(is_active=True)
    else:
        # 권한 레벨 기반 필터링
        role_based_q = Q()
        if user.is_org_admin():
            role_based_q = Q(permission_level__in=['regular', 'org_admin'])
        else:
            role_based_q = Q(permission_level='regular')
        
        # 조직 기반 접근 권한이 있는 페이지 필터링
        org_access_q = Q()
        if user.organization:
            org_access_q = Q(accessible_organizations=user.organization)
        
        # 권한 레벨 또는 조직 접근 권한이 있는 페이지 반환
        pages = Page.objects.filter(
            (role_based_q | org_access_q) & Q(is_active=True)
        ).distinct()
    
    serializer = PageListSerializer(pages, many=True)
    return Response({
        'pages': serializer.data,
        'user_role': user.role
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def component_data_view(request, component_id):
    """컴포넌트 데이터 조회 (BigQuery 연동)"""
    try:
        component = PageComponent.objects.get(id=component_id, is_active=True)
        user = request.user
        page = component.page

        if page.permission_level == 'org_admin' and not user.has_admin_permissions():
            raise PermissionDenied('이 컴포넌트에 접근할 권한이 없습니다.')

        # BigQuery 서비스 초기화 시 에러 처리
        try:
            service = BigQueryService()
        except Exception as bigquery_init_error:
            # BigQuery 설정이 없거나 잘못된 경우 차트 타입별 목 데이터 반환
            mock_data = get_mock_chart_data(component.component_template.chart_type)
            return Response({
                'component_id': component_id,
                'component_name': component.component_template.name,
                'chart_type': component.component_template.chart_type,
                'data': mock_data,
                'note': 'BigQuery not configured, showing sample data'
            })

        sql = component.component_template.bigquery_sql
        if not sql:
            # SQL이 없는 경우 차트 타입별 목 데이터 반환
            mock_data = get_mock_chart_data(component.component_template.chart_type)
            return Response({
                'component_id': component_id,
                'component_name': component.component_template.name,
                'chart_type': component.component_template.chart_type,
                'data': mock_data,
                'note': 'No SQL query configured, showing sample data'
            })

        # 기본 파라미터와 요청 파라미터 병합
        params = {}
        params.update(component.component_template.api_params or {})
        params.update(component.custom_params or {})
        params.update({k: v for k, v in request.query_params.items()})

        granularity = params.pop('granularity', None)
        aggregation = params.pop('aggregation', 'sum')
        if granularity:
            sql = service.build_time_aggregation_query(
                sql,
                granularity=granularity,
                method=aggregation,
            )

        try:
            rows = service.run_query(sql, params=params)
            chart_data = convert_to_chart_data(rows)
        except Exception as query_error:
            # BigQuery 쿼리 실행 실패 시 차트 타입별 목 데이터 반환
            mock_data = get_mock_chart_data(component.component_template.chart_type)
            return Response({
                'component_id': component_id,
                'component_name': component.component_template.name,
                'chart_type': component.component_template.chart_type,
                'data': mock_data,
                'note': f'Query execution failed, showing sample data: {str(query_error)}'
            })

        return Response({
            'component_id': component_id,
            'component_name': component.component_template.name,
            'chart_type': component.component_template.chart_type,
            'data': chart_data,
        })

    except PageComponent.DoesNotExist:
        raise NotFound('컴포넌트를 찾을 수 없습니다.')
    except Exception as exc:  # pragma: no cover
        if isinstance(exc, APIException):
            raise exc
        # 서버 에러 시에도 차트 타입별 목 데이터 반환
        try:
            component = PageComponent.objects.get(id=component_id, is_active=True)
            mock_data = get_mock_chart_data(component.component_template.chart_type)
        except:
            mock_data = {'categories': ['Error'], 'series': [{'name': 'Server Error', 'data': [0]}]}
            
        return Response({
            'error': 'Internal server error',
            'detail': str(exc),
            'component_id': component_id,
            'data': mock_data
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PageTemplateListView(generics.ListCreateAPIView):
    """페이지 템플릿 목록 조회 및 생성 (관리자 전용)"""
    queryset = PageTemplate.objects.filter(is_active=True)
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return PageTemplateCreateSerializer
        return PageTemplateSerializer


class PageTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """페이지 템플릿 상세 조회, 수정, 삭제 (관리자 전용)"""
    queryset = PageTemplate.objects.filter(is_active=True)
    serializer_class = PageTemplateSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]


class PageTemplateComponentListView(generics.ListCreateAPIView):
    """페이지 템플릿 컴포넌트 목록 조회 및 생성 (관리자 전용)"""
    serializer_class = PageTemplateComponentSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        """특정 페이지 템플릿의 컴포넌트 목록"""
        template_id = self.kwargs.get('template_id')
        return PageTemplateComponent.objects.filter(
            page_template_id=template_id,
            is_active=True
        ).order_by('order')

    def perform_create(self, serializer):
        """페이지 템플릿 ID를 자동으로 설정"""
        template_id = self.kwargs.get('template_id')
        try:
            page_template = PageTemplate.objects.get(id=template_id, is_active=True)
            serializer.save(page_template=page_template)
        except PageTemplate.DoesNotExist:
            raise NotFound('페이지 템플릿을 찾을 수 없습니다.')


class PageTemplateComponentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """페이지 템플릿 컴포넌트 상세 조회, 수정, 삭제"""
    serializer_class = PageTemplateComponentSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        """특정 페이지 템플릿의 컴포넌트"""
        template_id = self.kwargs.get('template_id')
        return PageTemplateComponent.objects.filter(
            page_template_id=template_id, 
            is_active=True
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsAdminUser])
def create_page_from_template(request):
    """템플릿을 기반으로 새 페이지 생성"""
    serializer = PageFromTemplateSerializer(data=request.data, context={'request': request})
    
    if serializer.is_valid():
        page = serializer.save()
        page_serializer = PageSerializer(page)
        return Response({
            'message': '템플릿을 기반으로 페이지가 성공적으로 생성되었습니다.',
            'page': page_serializer.data
        }, status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, IsAdminUser])
def grant_page_access(request, page_id, org_id):
    """조직에게 특정 페이지 접근 권한 부여"""
    try:
        page = Page.objects.get(id=page_id, is_active=True)
        organization = Organization.objects.get(id=org_id)
        
        # 이미 권한이 있는지 확인
        if PageOrganizationAccess.objects.filter(organization=organization, page=page).exists():
            return Response(
                {'detail': '이미 해당 페이지에 접근 권한이 있습니다.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 권한 부여
        PageOrganizationAccess.objects.create(
            organization=organization,
            page=page,
            granted_by=request.user
        )
        
        return Response({
            'message': f'{organization.name} 조직에게 "{page.name}" 페이지 접근 권한을 부여했습니다.',
            'organization_name': organization.name,
            'page_name': page.name,
            'granted_by': request.user.email
        }, status=status.HTTP_201_CREATED)
        
    except Page.DoesNotExist:
        raise NotFound('페이지를 찾을 수 없습니다.')
    except Organization.DoesNotExist:
        raise NotFound('조직을 찾을 수 없습니다.')


@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated, IsAdminUser])
def revoke_page_access(request, page_id, org_id):
    """조직의 특정 페이지 접근 권한 해제"""
    try:
        page = Page.objects.get(id=page_id, is_active=True)
        organization = Organization.objects.get(id=org_id)
        
        # 권한이 있는지 확인
        try:
            page_access = PageOrganizationAccess.objects.get(organization=organization, page=page)
            page_access.delete()
            
            return Response({
                'message': f'{organization.name} 조직의 "{page.name}" 페이지 접근 권한을 해제했습니다.',
                'organization_name': organization.name,
                'page_name': page.name,
                'revoked_by': request.user.email
            }, status=status.HTTP_200_OK)
            
        except PageOrganizationAccess.DoesNotExist:
            return Response(
                {'detail': '해당 조직은 이 페이지에 접근 권한이 없습니다.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
    except Page.DoesNotExist:
        raise NotFound('페이지를 찾을 수 없습니다.')
    except Organization.DoesNotExist:
        raise NotFound('조직을 찾을 수 없습니다.')


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsAdminUser])
def page_accessible_organizations(request, page_id):
    """특정 페이지에 접근 권한이 있는 조직 목록"""
    try:
        page = Page.objects.get(id=page_id, is_active=True)
        page_accesses = PageOrganizationAccess.objects.filter(page=page).select_related('organization', 'granted_by')
        
        organizations_data = []
        for access in page_accesses:
            organizations_data.append({
                'organization_id': access.organization.id,
                'organization_name': access.organization.name,
                'granted_at': access.granted_at,
                'granted_by': access.granted_by.email if access.granted_by else None
            })
        
        return Response({
            'page_id': page.id,
            'page_name': page.name,
            'organizations': organizations_data,
            'total_organizations': len(organizations_data)
        })
        
    except Page.DoesNotExist:
        raise NotFound('페이지를 찾을 수 없습니다.')


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated, IsAdminUser])
def organization_accessible_pages_admin(request, org_id):
    """특정 조직이 접근 가능한 페이지 목록 (관리자용)"""
    try:
        organization = Organization.objects.get(id=org_id)
        page_accesses = PageOrganizationAccess.objects.filter(organization=organization).select_related('page', 'granted_by')
        
        pages_data = []
        for access in page_accesses:
            pages_data.append({
                'page_id': access.page.id,
                'page_name': access.page.name,
                'permission_level': access.page.permission_level,
                'granted_at': access.granted_at,
                'granted_by': access.granted_by.email if access.granted_by else None
            })
        
        return Response({
            'organization_id': organization.id,
            'organization_name': organization.name,
            'pages': pages_data,
            'total_pages': len(pages_data)
        })
        
    except Organization.DoesNotExist:
        raise NotFound('조직을 찾을 수 없습니다.')