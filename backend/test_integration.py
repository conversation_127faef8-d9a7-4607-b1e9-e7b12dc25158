"""Integration and performance tests for the system."""
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from accounts.models import Organization
from dashboard.models import Page, ComponentTemplate, PageComponent
from unittest.mock import patch
import time

User = get_user_model()


class EndToEndTests(APITestCase):
    """End-to-end tests covering login and dashboard data flow."""

    def setUp(self):
        self.organization = Organization.objects.create(name="Test Org")
        # create admin to create pages
        self.admin = User.objects.create_user(
            email="<EMAIL>",
            username="admin",
            password="adminpass123",
            role="org_admin",
            organization=self.organization,
        )
        # regular user for E2E flow
        self.user = User.objects.create_user(
            email="<EMAIL>",
            username="user",
            password="userpass123",
            role="regular",
            organization=self.organization,
        )
        # create page and component
        self.page = Page.objects.create(
            name="Regular Page",
            permission_level="regular",
            layout_config={"columns": 100},
            created_by=self.admin,
        )
        template = ComponentTemplate.objects.create(
            name="Test Chart",
            chart_type="bar",
            api_endpoint="/api/test-data/",
            bigquery_sql="SELECT 1 as value",
        )
        self.component = PageComponent.objects.create(
            page=self.page,
            component_template=template,
            grid_position={"x": 0, "y": 0, "width": 10, "height": 10},
            order=1,
        )

    def authenticate(self):
        """Helper to authenticate the regular user using the login API."""
        url = reverse("accounts:login")
        response = self.client.post(
            url, {"email": "<EMAIL>", "password": "userpass123"}, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        token = response.data["access"]
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")

    def test_login_and_fetch_component_data(self):
        """Full flow from login to retrieving component data."""
        self.authenticate()
        # fetch accessible pages
        response = self.client.get(reverse("dashboard:page-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data["results"]), 1)

        # fetch component data with BigQuery mocked
        with patch("dashboard.services.bigquery.Client"), patch(
            "dashboard.services.BigQueryService.run_query",
            return_value=[{"label": "x", "value": 1}],
        ):
            response = self.client.get(
                reverse("dashboard:component-data", args=[self.component.id])
            )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["component_id"], self.component.id)
        self.assertIn("data", response.data)

    def test_permission_restriction(self):
        """Regular user should not access admin-only pages."""
        # create admin-only page
        Page.objects.create(
            name="Admin Page",
            permission_level="org_admin",
            layout_config={"columns": 100},
            created_by=self.admin,
        )
        self.authenticate()
        response = self.client.get(reverse("dashboard:page-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        page_names = [p["name"] for p in response.data["results"]]
        self.assertNotIn("Admin Page", page_names)

    def test_component_data_error_handling(self):
        """Errors from BigQuery should return 500 with detail."""
        self.authenticate()
        with patch("dashboard.services.bigquery.Client"), patch(
            "dashboard.services.BigQueryService.run_query",
            side_effect=Exception("boom"),
        ):
            response = self.client.get(
                reverse("dashboard:component-data", args=[self.component.id])
            )
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn("detail", response.data)


class PerformanceTests(APITestCase):
    """Simple performance checks for critical services."""

    @patch("dashboard.services.bigquery.Client")
    def test_bigquery_query_performance(self, mock_client):
        mock_job = mock_client.return_value.query.return_value
        mock_job.result.return_value = []
        service_sql = "SELECT 1"
        from dashboard.services import BigQueryService

        service = BigQueryService(project_id="test")
        start = time.time()
        service.run_query(service_sql)
        duration = time.time() - start
        # expect mocked query to return quickly
        self.assertLess(duration, 0.5)
        mock_client.return_value.query.assert_called_once()

