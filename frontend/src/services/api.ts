import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { AuthResponse, LoginCredentials, User } from '../types/auth';

export class ApiError extends Error {
  status?: number;
}

export class NetworkError extends ApiError {}
export class PermissionError extends ApiError {}
export class ServerError extends ApiError {}

interface Page {
  id: number | string;
  name: string;
  permission_level: string;
}

export interface Organization {
  id: number;
  name: string;
  description?: string;
  user_count?: number;
  created_at: string;
}

interface PageOrganizationAccess {
  organization_id: number;
  organization_name: string;
  granted_at: string;
  granted_by: string | null;
}

interface PageOrganizationAccessResponse {
  page_id: number;
  page_name: string;
  organizations: PageOrganizationAccess[];
  total_organizations: number;
}

interface OrganizationPageAccess {
  page_id: number;
  page_name: string;
  permission_level: string;
  granted_at: string;
  granted_by: string | null;
}

interface OrganizationPageAccessResponse {
  organization_id: number;
  organization_name: string;
  pages: OrganizationPageAccess[];
  total_pages: number;
}

class ApiClient {
  private client: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: Array<{resolve: Function, reject: Function}> = [];

  constructor() {
    this.client = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for automatic token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // Queue requests while refreshing
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then(token => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.client(originalRequest);
            }).catch(err => {
              return Promise.reject(err);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              const newToken = response.access;
              
              localStorage.setItem('access_token', newToken);
              
              // Process queued requests
              this.failedQueue.forEach(({ resolve }) => resolve(newToken));
              this.failedQueue = [];
              
              // Retry original request with new token
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, clear tokens and redirect
            this.clearTokens();
            this.failedQueue.forEach(({ reject }) => reject(refreshError));
            this.failedQueue = [];
            window.location.hash = '#/login';
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private async refreshToken(refreshToken: string): Promise<{access: string}> {
    // Use direct axios call to avoid interceptor recursion
    const response = await axios.post(
      `${this.client.defaults.baseURL}/auth/token/refresh/`,
      { refresh: refreshToken },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: this.client.defaults.timeout
      }
    );
    return response.data;
  }

  private clearTokens(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.client(config);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        const status = error.response.status;
        if (status === 403) {
          const err = new PermissionError('접근 권한이 없습니다.');
          err.status = status;
          throw err;
        }
        if (status >= 500) {
          const err = new ServerError('서버 오류가 발생했습니다.');
          err.status = status;
          throw err;
        }
        const err = new ApiError(error.response.data?.error || error.response.data?.message || '요청 중 오류가 발생했습니다.');
        err.status = status;
        throw err;
      }
      if (error.code === 'ECONNABORTED' || error.message === 'Network Error') {
        throw new NetworkError('네트워크 오류가 발생했습니다.');
      }
      throw error;
    }
  }

  // Auth endpoints
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return this.request<AuthResponse>({
      method: 'POST',
      url: '/auth/login/',
      data: credentials,
    });
  }

  async loginWithGoogle(code: string): Promise<AuthResponse> {
    return this.request<AuthResponse>({
      method: 'POST',
      url: '/auth/google/login/',
      data: { code },
    });
  }

  async logout(): Promise<void> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (refreshToken) {
      try {
        await this.request({
          method: 'POST',
          url: '/auth/logout/',
          data: { refresh: refreshToken },
        });
      } catch (error) {
        // Ignore logout errors
        console.warn('Logout request failed:', error);
      }
    }
    this.clearTokens();
  }

  async getCurrentUser() {
    return this.request<User>({
      method: 'GET',
      url: '/auth/profile/',
    });
  }

  async updateUserTheme(theme: string) {
    return this.request<{ message: string; user: User }>({
      method: 'PATCH',
      url: '/auth/theme/',
      data: { ui_theme: theme },
    });
  }

  async getGoogleOAuthConfig() {
    return this.request<{ client_id: string; redirect_uri: string; auth_url: string; scope: string }>({
      method: 'GET',
      url: '/auth/google/config/',
    });
  }

  // Page and component endpoints
  async getAccessiblePages() {
    return this.request<{ pages: Page[]; user_role: string }>({
      method: 'GET',
      url: '/pages/accessible/',
    });
  }

  async getPageComponents(pageId: string | number) {
    return this.request<any[]>({
      method: 'GET',
      url: `/pages/${pageId}/components/`,
    });
  }

  async getPageDetail(pageId: string | number) {
    return this.request<any>({
      method: 'GET',
      url: `/pages/${pageId}/`,
    });
  }

  async getComponentData(componentId: string | number, params?: any) {
    return this.request<any>({
      method: 'GET',
      url: `/components/${componentId}/data/`,
      params,
    });
  }

  // Admin endpoints for organization-page permissions
  async getAllPages() {
    return this.request<Page[]>({
      method: 'GET',
      url: '/pages/',
    });
  }

  async getAllOrganizations() {
    return this.request<Organization[]>({
      method: 'GET',
      url: '/auth/organizations/',
    });
  }

  async getPageAccessibleOrganizations(pageId: number) {
    return this.request<PageOrganizationAccessResponse>({
      method: 'GET',
      url: `/admin/pages/${pageId}/organizations/`,
    });
  }

  async getOrganizationAccessiblePages(orgId: number) {
    return this.request<OrganizationPageAccessResponse>({
      method: 'GET',
      url: `/admin/organizations/${orgId}/pages/`,
    });
  }

  async grantPageAccess(pageId: number, orgId: number) {
    return this.request<{ message: string; organization_name: string; page_name: string; granted_by: string }>({
      method: 'POST',
      url: `/admin/pages/${pageId}/organizations/${orgId}/access/`,
    });
  }

  async revokePageAccess(pageId: number, orgId: number) {
    return this.request<{ message: string; organization_name: string; page_name: string; revoked_by: string }>({
      method: 'DELETE',
      url: `/admin/pages/${pageId}/organizations/${orgId}/access/revoke/`,
    });
  }
}

export const apiClient = new ApiClient();

// Export types for use in components
export type { Page, PageOrganizationAccess, PageOrganizationAccessResponse, OrganizationPageAccess, OrganizationPageAccessResponse };