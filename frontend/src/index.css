@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import custom grid layout styles */
@import './styles/gridLayout.css';

/* Set default theme before React loads */
html {
  data-theme: corporate;
}

/* DaisyUI Corporate Theme Variables */
[data-theme="corporate"] {
  --p: 259 94% 51%;
  --pc: 0 0% 100%;
  --s: 314 100% 47%;
  --sc: 0 0% 100%;
  --a: 174 60% 51%;
  --ac: 0 0% 100%;
  --n: 214 20% 14%;
  --nc: 210 20% 98%;
  --b1: 0 0% 100%;
  --b2: 0 0% 95%;
  --b3: 180 2% 90%;
  --bc: 215 28% 17%;
  --in: 198 93% 60%;
  --inc: 198 100% 12%;
  --su: 158 64% 52%;
  --suc: 158 100% 10%;
  --wa: 43 96% 56%;
  --wac: 43 100% 11%;
  --er: 0 91% 71%;
  --erc: 0 100% 14%;
}

/* DaisyUI Dark Theme Variables */
[data-theme="dark"] {
  --p: 262 80% 50%;
  --pc: 0 0% 100%;
  --s: 316 70% 50%;
  --sc: 0 0% 100%;
  --a: 175 70% 41%;
  --ac: 0 0% 100%;
  --n: 213 18% 20%;
  --nc: 220 13% 69%;
  --b1: 212 18% 14%;
  --b2: 213 18% 12%;
  --b3: 213 18% 10%;
  --bc: 220 13% 69%;
  --in: 198 93% 60%;
  --inc: 198 100% 12%;
  --su: 158 64% 52%;
  --suc: 158 100% 10%;
  --wa: 43 96% 56%;
  --wac: 43 100% 11%;
  --er: 0 91% 71%;
  --erc: 0 100% 14%;
}

/* DaisyUI Business Theme Variables */
[data-theme="business"] {
  --p: 233 47% 21%;
  --s: 217 91% 60%;
  --a: 136 72% 40%;
  --n: 218 11% 65%;
  --nc: 222 84% 5%;
  --b1: 0 0% 100%;
  --b2: 210 20% 98%;
  --b3: 216 12% 84%;
  --bc: 222 84% 5%;
  --in: 198 93% 60%;
  --su: 158 64% 52%;
  --wa: 43 96% 56%;
  --er: 0 91% 71%;
}

/* DaisyUI Retro Theme Variables */
[data-theme="retro"] {
  --p: 13 100% 72%;
  --s: 287 58% 48%;
  --a: 86 70% 68%;
  --n: 23 18% 20%;
  --nc: 23 46% 82%;
  --b1: 37 41% 91%;
  --b2: 35 25% 80%;
  --b3: 33 24% 64%;
  --bc: 23 18% 20%;
  --in: 198 93% 60%;
  --su: 158 64% 52%;
  --wa: 43 96% 56%;
  --er: 0 91% 71%;
}

/* DaisyUI Cyberpunk Theme Variables */
[data-theme="cyberpunk"] {
  --p: 321 70% 52%;
  --s: 180 100% 50%;
  --a: 57 100% 50%;
  --n: 230 61% 14%;
  --nc: 230 61% 90%;
  --b1: 230 61% 14%;
  --b2: 231 59% 11%;
  --b3: 230 61% 8%;
  --bc: 230 61% 90%;
  --in: 198 93% 60%;
  --su: 158 64% 52%;
  --wa: 43 96% 56%;
  --er: 0 91% 71%;
}

@layer base {
  body {
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: hsl(var(--b1));
    color: hsl(var(--bc));
  }

  * {
    box-sizing: border-box;
  }

  #root {
    height: 100vh;
  }
}

@layer components {
  /* Tremor chart container styles */
  .chart-wrapper {
    @apply w-full h-full relative block;
    min-height: 300px;
  }

  /* Essential DaisyUI Components */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all;
    background-color: hsl(var(--b2));
    color: hsl(var(--bc));
    border: 1px solid hsl(var(--b3));
  }

  .btn:hover {
    background-color: hsl(var(--b3));
  }

  .btn-primary {
    background-color: hsl(var(--p));
    color: hsl(var(--pc));
    border-color: hsl(var(--p));
  }

  .btn-primary:hover {
    background-color: hsl(var(--p) / 0.8);
  }

  .btn-ghost {
    background-color: transparent;
    border-color: transparent;
  }

  .btn-ghost:hover {
    background-color: hsl(var(--b2));
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .navbar {
    @apply flex items-center justify-between w-full p-4;
    background-color: hsl(var(--b2));
    color: hsl(var(--bc));
  }

  .navbar-start {
    @apply flex items-center gap-4;
  }

  .navbar-end {
    @apply flex items-center gap-2;
  }

  .card {
    @apply rounded-lg shadow-md;
    background-color: hsl(var(--b1));
    color: hsl(var(--bc));
    border: 1px solid hsl(var(--b3));
  }

  .card-body {
    @apply p-6;
  }

  .card-title {
    @apply text-lg font-semibold mb-2;
  }

  .modal {
    @apply fixed inset-0 z-50 hidden;
  }

  .modal.modal-open {
    @apply flex items-center justify-center;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .modal-box {
    @apply relative rounded-lg shadow-xl max-w-lg w-full mx-4;
    background-color: hsl(var(--b1));
    color: hsl(var(--bc));
  }

  .modal-backdrop {
    @apply absolute inset-0;
  }

  .badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
    background-color: hsl(var(--b2));
    color: hsl(var(--bc));
  }

  .badge-primary {
    background-color: hsl(var(--p));
    color: hsl(var(--pc));
  }

  .badge-sm {
    @apply px-1.5 py-0.5 text-xs;
  }

  .alert {
    @apply flex items-center gap-3 p-4 rounded-lg;
    background-color: hsl(var(--b2));
    color: hsl(var(--bc));
  }

  .alert-info {
    background-color: hsl(var(--in) / 0.1);
    color: hsl(var(--inc));
    border: 1px solid hsl(var(--in) / 0.2);
  }

  .bg-base-100 {
    background-color: hsl(var(--b1));
  }

  .bg-base-200 {
    background-color: hsl(var(--b2));
  }

  .bg-base-300 {
    background-color: hsl(var(--b3));
  }

  .text-base-content {
    color: hsl(var(--bc));
  }

  .text-primary {
    color: hsl(var(--p));
  }

  .text-primary-content {
    color: hsl(var(--pc));
  }

  .loading {
    @apply inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent;
  }

  .loading-spinner {
    @apply w-4 h-4;
  }

  .loading-lg {
    @apply w-8 h-8;
  }

  /* Main Application Areas */
  .App {
    background-color: hsl(var(--b1));
    color: hsl(var(--bc));
  }

  /* Ensure all base classes work properly */
  .min-h-screen {
    background-color: hsl(var(--b1));
    color: hsl(var(--bc));
  }

  /* Shadow utilities with theme colors */
  .shadow-md {
    box-shadow: 0 4px 6px -1px hsl(var(--b3) / 0.1), 0 2px 4px -2px hsl(var(--b3) / 0.1);
  }

  /* Grid and layout components */
  .grid {
    background-color: hsl(var(--b1));
  }

  /* Page content areas */
  .page-content {
    background-color: hsl(var(--b1));
    color: hsl(var(--bc));
  }

  /* Navigation elements */
  .nav-tab {
    background-color: hsl(var(--b2));
    color: hsl(var(--bc));
    border-color: hsl(var(--b3));
  }

  .nav-tab:hover {
    background-color: hsl(var(--b3));
  }

  .nav-tab.active {
    background-color: hsl(var(--p));
    color: hsl(var(--pc));
  }
}