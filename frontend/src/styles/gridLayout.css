/**
 * CSS Grid Layout Styles
 * 
 * Custom CSS for dashboard grid layout system with container queries,
 * CSS custom properties, and flexible sizing constraints.
 */

/* Container Query Support */
@container (min-width: 320px) {
  .chart-container {
    font-size: 0.875rem; /* text-sm */
  }
}

@container (min-width: 480px) {
  .chart-container {
    font-size: 1rem; /* text-base */
  }
}

@container (min-width: 640px) {
  .chart-container {
    font-size: 1.125rem; /* text-lg */
  }
}

/* CSS Custom Properties for Dynamic Grid Values */
.grid-item-dynamic {
  grid-column-start: var(--grid-column-start, auto);
  grid-column-end: var(--grid-column-end, auto);
  grid-row-start: var(--grid-row-start, auto);
  grid-row-end: var(--grid-row-end, auto);
}

/* Flexible Grid Container with minmax() */
.flexible-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(var(--min-column-width, 200px), 1fr));
  grid-auto-rows: minmax(var(--min-row-height, 200px), auto);
}

/* Dynamic Aspect Ratio Support */
.aspect-ratio-dynamic {
  aspect-ratio: var(--aspect-ratio, auto);
}

/* Dynamic Height Constraints */
.height-constraints-dynamic {
  min-height: var(--min-height, auto);
  max-height: var(--max-height, none);
  height: var(--fixed-height, auto);
}

/* Widget Render Configuration Support */
.widget-render-config {
  /* Aspect ratio from render config */
  aspect-ratio: var(--widget-aspect-ratio, auto);
  
  /* Height constraints from render config */
  min-height: var(--widget-min-height, auto);
  max-height: var(--widget-max-height, none);
  height: var(--widget-fixed-height, auto);
  
  /* Padding from render config */
  padding: var(--widget-padding, 0);
  
  /* Overflow behavior from render config */
  overflow: var(--widget-overflow, hidden);
}

/* Auto height behavior */
.widget-auto-height {
  height: auto;
  flex: 1;
  min-height: 0;
}

/* Fixed height behavior */
.widget-fixed-height {
  flex: none;
}

/* Fallback styles for missing render configurations */
.widget-render-fallback {
  aspect-ratio: 0.7;
  min-height: 200px;
  overflow: hidden;
}

/* Chart Container Base Styles */
.chart-container {
  container-type: inline-size;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Important for flex children to shrink */
}

.chart-content {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Grid Item Base Styles */
.grid-item {
  position: relative;
  overflow: hidden;
  min-height: 0;
  min-width: 0;
}

/* Responsive Grid Utilities */
.responsive-grid {
  display: grid;
  gap: 1rem;
  width: 100%;
  grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Auto-fit Grid for Dynamic Column Count */
.auto-fit-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-rows: minmax(200px, auto);
}

/* Smooth Transitions for Layout Changes */
.grid-transition {
  transition: all 0.3s ease-in-out;
}

.grid-item-transition {
  transition: grid-column 0.3s ease-in-out, 
              grid-row 0.3s ease-in-out,
              transform 0.3s ease-in-out;
}

/* Overflow Handling Variants */
.overflow-scroll-custom {
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.overflow-scroll-custom::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.overflow-scroll-custom::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-scroll-custom::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.overflow-scroll-custom::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Loading State for Grid Items */
.grid-item-loading {
  background: linear-gradient(90deg, 
    rgba(243, 244, 246, 0.8) 25%, 
    rgba(229, 231, 235, 0.8) 50%, 
    rgba(243, 244, 246, 0.8) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Error State for Grid Items */
.grid-item-error {
  border: 2px dashed #ef4444;
  background-color: rgba(254, 226, 226, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 0.875rem;
  text-align: center;
  padding: 1rem;
}

/* Debug Mode Styles */
.grid-debug .grid-item {
  border: 1px solid #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}

.grid-debug .grid-item::before {
  content: attr(data-grid-position);
  position: absolute;
  top: 0;
  left: 0;
  background: #3b82f6;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  z-index: 10;
}

/* Print Styles */
@media print {
  .responsive-grid,
  .flexible-grid,
  .auto-fit-grid {
    display: block !important;
  }
  
  .grid-item {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
  
  .chart-container {
    container-type: normal;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .grid-item {
    border: 2px solid;
  }
  
  .chart-container {
    border: 1px solid;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .grid-transition,
  .grid-item-transition {
    transition: none;
  }
  
  .grid-item-loading {
    animation: none;
    background: rgba(243, 244, 246, 0.8);
  }
}