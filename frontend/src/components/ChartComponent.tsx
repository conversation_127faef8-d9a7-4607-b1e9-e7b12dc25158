import { useEffect, useState, forwardRef, useImperative<PERSON><PERSON><PERSON>, useCallback, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Card,
  Title,
  Text,
  Metric,
  Flex,
  BadgeDelta,
} from '@tremor/react';
import { apiClient } from '../services/api';
import { WidgetRenderConfig } from '../types/dashboard';

// Chart data types for Tremor
export interface TremorDataPoint {
  [key: string]: string | number;
}

export interface ChartData {
  data: TremorDataPoint[];
  categories?: string[];
  valueFormatter?: (value: number) => string;
  title?: string;
  subtitle?: string;
}

export type ChartType =
  | 'area'
  | 'bar'
  | 'line'
  | 'donut'
  | 'scatter'
  | 'metric'
  | 'kpi';

interface ChartComponentProps {
  type: ChartType;
  apiUrl?: string;
  componentId?: string | number;
  data?: ChartData;
  title?: string;
  subtitle?: string;
  refreshInterval?: number;
  className?: string;
  onLoaded?: () => void;
  disableAutoLoad?: boolean;
  isAdmin?: boolean;
  colors?: string[];
  index?: string;
  categories?: string[];
  valueFormatter?: (value: number) => string;
  renderConfig?: WidgetRenderConfig;
}

export interface ChartComponentHandle {
  refresh: () => Promise<void>;
  loadData: () => Promise<void>;
}

// Transform legacy data format to Tremor format
const transformDataForTremor = (rawData: any, type: ChartType): ChartData => {
  if (!rawData) {
    return {
      data: [{ name: 'No Data', value: 0 }],
      categories: ['name'],
    };
  }

  // If already in Tremor format
  if (rawData.data && Array.isArray(rawData.data)) {
    return rawData;
  }

  // Transform legacy Toast UI format
  if (rawData.series && rawData.categories) {
    const { series, categories } = rawData;
    
    if (type === 'donut' || type === 'metric' || type === 'kpi') {
      // For donut/metric charts, transform to simple key-value format
      const transformedData = series.map((s: any, index: number) => ({
        name: s.name || `Series ${index + 1}`,
        value: Array.isArray(s.data) ? s.data.reduce((a: number, b: number) => a + b, 0) : s.data || 0,
      }));
      
      return {
        data: transformedData,
        categories: ['name'],
      };
    } else {
      // For cartesian charts, create data points for each category
      const transformedData: TremorDataPoint[] = [];
      
      if (Array.isArray(categories)) {
        categories.forEach((category: string, categoryIndex: number) => {
          const dataPoint: TremorDataPoint = { name: category };
          
          series.forEach((s: any) => {
            const seriesName = s.name || `Series ${categoryIndex + 1}`;
            const value = Array.isArray(s.data) ? s.data[categoryIndex] || 0 : s.data || 0;
            dataPoint[seriesName] = value;
          });
          
          transformedData.push(dataPoint);
        });
      }
      
      return {
        data: transformedData,
        categories: series.map((s: any, i: number) => s.name || `Series ${i + 1}`),
      };
    }
  }

  // Fallback for unexpected data format
  return {
    data: [{ name: 'Invalid Data', value: 0 }],
    categories: ['name'],
  };
};

// Default color palette for charts
const DEFAULT_COLORS = [
  'blue', 'emerald', 'violet', 'amber', 'rose', 'cyan', 'pink', 'indigo'
];

// Utility function to calculate container styles based on render config
const getContainerStyles = (renderConfig?: WidgetRenderConfig, containerWidth?: number): React.CSSProperties => {
  if (!renderConfig) return {};

  const styles: React.CSSProperties = {};

  // Handle height constraints
  if (renderConfig.fixedHeight) {
    styles.height = `${renderConfig.fixedHeight}px`;
  } else if (renderConfig.autoHeight) {
    styles.height = 'auto';
    if (renderConfig.minHeight) {
      styles.minHeight = `${renderConfig.minHeight}px`;
    }
    if (renderConfig.maxHeight) {
      styles.maxHeight = `${renderConfig.maxHeight}px`;
    }
  }

  // Handle aspect ratio
  if (renderConfig.aspectRatio && containerWidth) {
    const calculatedHeight = containerWidth / renderConfig.aspectRatio;
    
    // Apply aspect ratio height, but respect min/max constraints
    let finalHeight = calculatedHeight;
    
    if (renderConfig.minHeight && finalHeight < renderConfig.minHeight) {
      finalHeight = renderConfig.minHeight;
    }
    if (renderConfig.maxHeight && finalHeight > renderConfig.maxHeight) {
      finalHeight = renderConfig.maxHeight;
    }
    
    if (!renderConfig.fixedHeight) {
      styles.height = `${finalHeight}px`;
    }
  }

  return styles;
};

// Utility function to get chart container class names based on render config
const getContainerClasses = (renderConfig?: WidgetRenderConfig): string => {
  const classes = ['relative', 'w-full'];

  if (renderConfig?.autoHeight) {
    classes.push('h-auto');
  }

  // Add overflow handling
  classes.push('overflow-hidden');

  return classes.join(' ');
};

const ChartComponent = forwardRef<ChartComponentHandle, ChartComponentProps>(({
  type,
  apiUrl,
  componentId,
  data,
  title,
  subtitle,
  refreshInterval = 60000,
  className = '',
  onLoaded,
  disableAutoLoad = false,
  isAdmin = false,
  colors = DEFAULT_COLORS,
  index = 'name',
  categories,
  valueFormatter,
  renderConfig,
}, ref) => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [note, setNote] = useState<string | null>(null);
  const [showNoteModal, setShowNoteModal] = useState<boolean>(false);
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const fetchData = useCallback(async () => {
    if (!componentId && !apiUrl) return;
    
    setLoading(true);
    try {
      let json;
      if (componentId) {
        json = await apiClient.getComponentData(componentId);
      } else if (apiUrl) {
        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        json = await response.json();
      }
      
      // Extract data and note from API response
      const rawData = json?.data || json;
      const incomingNote: string | null = (json && typeof json === 'object' && 'note' in json) ? (json.note as string) : null;
      setNote(incomingNote || null);
      
      if (!rawData) {
        throw new Error('No chart data received from API');
      }
      
      // Transform data to Tremor format
      const transformedData = transformDataForTremor(rawData, type);
      setChartData(transformedData);
      setLastUpdated(new Date().toLocaleTimeString('ko-KR'));
      setError(null);
      
      if (onLoaded) {
        onLoaded();
      }
    } catch (err) {
      console.error('Error fetching chart data:', err);
      const errorMessage = err instanceof Error ? err.message : '알 수 없는 오류';
      setError(`데이터 로드 실패: ${errorMessage}`);
      setChartData(null);
    } finally {
      setLoading(false);
    }
  }, [componentId, apiUrl, onLoaded, type]);

  useImperativeHandle(ref, () => ({ 
    refresh: fetchData,
    loadData: fetchData
  }));

  // Handle container resize for aspect ratio calculations
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !renderConfig?.aspectRatio) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setContainerWidth(entry.contentRect.width);
      }
    });

    resizeObserver.observe(container);
    // Set initial width
    setContainerWidth(container.offsetWidth);

    return () => {
      resizeObserver.disconnect();
    };
  }, [renderConfig?.aspectRatio]);

  // Handle window resize for responsive behavior
  useEffect(() => {
    if (!renderConfig?.aspectRatio) return;

    const handleResize = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [renderConfig?.aspectRatio]);

  useEffect(() => {
    if (data) {
      // Static data - transform and load immediately
      const transformedData = transformDataForTremor(data, type);
      setChartData(transformedData);
      setLastUpdated(new Date().toLocaleTimeString('ko-KR'));
      const staticNote = (data as any)?.note as string | undefined;
      if (staticNote) setNote(staticNote);
      if (onLoaded) {
        onLoaded();
      }
    } else if ((componentId || apiUrl) && !disableAutoLoad) {
      // API data with auto-load enabled
      fetchData().catch(() => {});
      const interval = setInterval(() => {
        fetchData().catch(() => {});
      }, refreshInterval);
      
      return () => clearInterval(interval);
    }
  }, [componentId, apiUrl, data, refreshInterval, fetchData, onLoaded, disableAutoLoad, type]);

  const renderChart = () => {
    if (!chartData || !chartData.data || chartData.data.length === 0) {
      const containerStyles = getContainerStyles(renderConfig, containerWidth);
      const emptyHeight = containerStyles.height || '16rem'; // fallback to h-64 equivalent
      
      return (
        <div 
          className="flex items-center justify-center text-gray-500"
          style={{ height: emptyHeight }}
        >
          데이터가 없습니다
        </div>
      );
    }

    const effectiveCategories = categories || chartData.categories || ['value'];
    const effectiveValueFormatter = valueFormatter || chartData.valueFormatter || ((value: number) => value.toString());
    
    // Calculate chart height based on render config
    const containerStyles = getContainerStyles(renderConfig, containerWidth);
    const chartHeight = containerStyles.height || '16rem'; // fallback to h-64 equivalent
    const chartClassName = `w-full`;

    switch (type) {
      case 'area':
        return (
          <div style={{ height: chartHeight }}>
            <AreaChart
              data={chartData.data}
              index={index}
              categories={effectiveCategories}
              colors={colors}
              valueFormatter={effectiveValueFormatter}
              className={chartClassName}
            />
          </div>
        );
      
      case 'bar':
        return (
          <div style={{ height: chartHeight }}>
            <BarChart
              data={chartData.data}
              index={index}
              categories={effectiveCategories}
              colors={colors}
              valueFormatter={effectiveValueFormatter}
              className={chartClassName}
            />
          </div>
        );
      
      case 'line':
        return (
          <div style={{ height: chartHeight }}>
            <LineChart
              data={chartData.data}
              index={index}
              categories={effectiveCategories}
              colors={colors}
              valueFormatter={effectiveValueFormatter}
              className={chartClassName}
            />
          </div>
        );
      
      case 'donut':
        return (
          <div style={{ height: chartHeight }}>
            <DonutChart
              data={chartData.data}
              category="value"
              index="name"
              colors={colors}
              valueFormatter={effectiveValueFormatter}
              className={chartClassName}
            />
          </div>
        );
      
      case 'scatter':
        return (
          <div style={{ height: chartHeight }}>
            <ScatterChart
              data={chartData.data}
              x={effectiveCategories[0] || 'x'}
              y={effectiveCategories[1] || 'y'}
              category="name"
              colors={colors}
              valueFormatter={{
                x: effectiveValueFormatter,
                y: effectiveValueFormatter
              }}
              className={chartClassName}
            />
          </div>
        );
      
      case 'metric':
      case 'kpi':
        const firstDataPoint = chartData.data[0];
        const metricValue = typeof firstDataPoint?.value === 'number' ? firstDataPoint.value : 0;
        return (
          <div style={containerStyles} className="flex items-center justify-center">
            <Card className="w-full max-w-sm">
              <Flex alignItems="start">
                <div>
                  <Text>{title || chartData.title || '메트릭'}</Text>
                  <Metric>{effectiveValueFormatter(metricValue)}</Metric>
                </div>
                <BadgeDelta deltaType="moderateIncrease">+12.3%</BadgeDelta>
              </Flex>
            </Card>
          </div>
        );
      
      default:
        return (
          <div 
            className="flex items-center justify-center text-red-500"
            style={{ height: chartHeight }}
          >
            지원하지 않는 차트 타입: {type}
          </div>
        );
    }
  };

  const containerStyles = getContainerStyles(renderConfig, containerWidth);
  const containerClasses = getContainerClasses(renderConfig);

  return (
    <Card 
      ref={containerRef}
      className={`${containerClasses} ${className}`}
      style={containerStyles}
    >
      {/* Title and subtitle */}
      {(title || subtitle) && (
        <div className="mb-4">
          {title && <Title>{title}</Title>}
          {subtitle && <Text>{subtitle}</Text>}
        </div>
      )}

      {/* Admin note button */}
      {note && isAdmin && (
        <button
          type="button"
          className="absolute top-4 right-4 z-10 px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50"
          onClick={() => setShowNoteModal(true)}
          title="쿼리 로그 보기"
        >
          🛈 로그
        </button>
      )}

      {/* Loading state */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-20">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div 
          className="flex items-center justify-center text-red-500 bg-red-50 rounded-lg"
          style={{ height: containerStyles.height || '16rem' }}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">⚠️</div>
            <div>{error}</div>
          </div>
        </div>
      )}

      {/* Chart content */}
      <div className="flex-1 min-h-0">
        {!loading && !error && renderChart()}
      </div>

      {/* Note modal */}
      {showNoteModal && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          onClick={() => setShowNoteModal(false)}
        >
          <div
            className="bg-white rounded-lg max-w-2xl max-h-[80vh] w-full mx-4 flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold">진단 로그</h3>
              <button
                type="button"
                className="text-gray-400 hover:text-gray-600"
                onClick={() => setShowNoteModal(false)}
              >
                ✕
              </button>
            </div>
            <div className="p-4 overflow-auto flex-1">
              <pre className="whitespace-pre-wrap text-sm">{note}</pre>
            </div>
            <div className="flex justify-end p-4 border-t">
              <button
                type="button"
                className="px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded"
                onClick={() => setShowNoteModal(false)}
              >
                닫기
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Timestamp */}
      {lastUpdated && (
        <div className="absolute bottom-2 right-2 text-xs text-gray-400">
          {lastUpdated}
        </div>
      )}
    </Card>
  );
});

ChartComponent.displayName = 'ChartComponent';

export default ChartComponent;