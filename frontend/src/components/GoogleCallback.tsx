import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const GoogleCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const auth = useAuth();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      const error = searchParams.get('error');
      const access = searchParams.get('access');
      const refresh = searchParams.get('refresh');
      const user_id = searchParams.get('user_id');
      const email = searchParams.get('email');

      if (error) {
        console.error('Google OAuth error:', error);
        setError('Google 로그인이 취소되었거나 오류가 발생했습니다.');
        setTimeout(() => navigate('/login'), 3000);
        return;
      }

      if (!access || !refresh) {
        setError('인증 토큰을 받지 못했습니다.');
        setTimeout(() => navigate('/login'), 3000);
        return;
      }

      try {
        // Store tokens in localStorage
        localStorage.setItem('access_token', access);
        localStorage.setItem('refresh_token', refresh);
        
        // Enable remember login for Google OAuth users by default
        localStorage.setItem('remember_login', 'true');
        
        // Update auth context with user info
        await (auth as any).setUserFromTokens(user_id, email);
        
        navigate('/dashboard');
      } catch (error: any) {
        console.error('Google callback error:', error);
        setError('Google 로그인 처리 중 오류가 발생했습니다.');
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    handleCallback();
  }, [searchParams, navigate, auth]);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-5 text-center bg-base-100">
        <h2 className="text-error mb-4 text-2xl font-bold">로그인 오류</h2>
        <p className="text-base-content/70 mb-6">{error}</p>
        <p className="text-base-content/50 text-sm">3초 후 로그인 페이지로 이동합니다...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-base-100">
      <span className="loading loading-spinner loading-lg text-primary mb-4"></span>
      <p className="text-base-content/70">Google 로그인 처리 중...</p>
    </div>
  );
};

export default GoogleCallback;