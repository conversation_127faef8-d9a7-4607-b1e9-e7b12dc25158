import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface PrivateRouteProps {
  children: React.ReactNode;
  requiredRole?: 'regular' | 'org_admin' | 'super_admin';
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ 
  children, 
  requiredRole = 'regular' 
}) => {
  const { isAuthenticated, user, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-base-100">
        <span className="loading loading-spinner loading-lg text-primary"></span>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role-based access
  if (user && !hasRequiredRole(user.role, requiredRole)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-5 text-center bg-base-100">
        <h2 className="text-error text-2xl font-bold mb-4">접근 권한이 없습니다</h2>
        <p className="text-base-content/70 mb-6">
          이 페이지에 접근하려면 {getRoleDisplayName(requiredRole)} 권한이 필요합니다.
        </p>
        <p className="text-base-content/50 text-sm">
          현재 권한: {getRoleDisplayName(user.role)}
        </p>
      </div>
    );
  }

  return <>{children}</>;
};

// Helper function to check if user has required role
const hasRequiredRole = (
  userRole: 'regular' | 'org_admin' | 'super_admin',
  requiredRole: 'regular' | 'org_admin' | 'super_admin'
): boolean => {
  const roleHierarchy = {
    'regular': 1,
    'org_admin': 2,
    'super_admin': 3,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};

// Helper function to get display name for roles
const getRoleDisplayName = (role: 'regular' | 'org_admin' | 'super_admin'): string => {
  const roleNames = {
    'regular': '일반 사용자',
    'org_admin': '조직 관리자',
    'super_admin': '운영 관리자',
  };

  return roleNames[role];
};

export default PrivateRoute;