import React from 'react';
import ChartComponent, {
  ChartData,
  ChartType,
  ChartComponentHandle,
} from './ChartComponent';
import ComponentErrorBoundary from './ComponentErrorBoundary';

export interface GridItem {
  id: string;
  title: string;
  chartType: ChartType;
  data?: ChartData;
  apiUrl?: string;
  componentId?: string | number;
  layout?: {
    w?: number; // column span
    h?: number; // row span
  };
  isLoading?: boolean;
}

interface ComponentGridProps {
  items: GridItem[];
  onComponentRef?: (id: string, ref: ChartComponentHandle | null) => void;
  onComponentLoaded?: (componentId: string) => void;
}

const ComponentGrid: React.FC<ComponentGridProps> = ({ items, onComponentRef, onComponentLoaded }) => {
  const getGridColSpan = (width: number) => {
    // Map component width (1-12) to Tailwind grid column spans
    if (width <= 3) return 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3';
    if (width <= 6) return 'col-span-12 md:col-span-12 lg:col-span-6 xl:col-span-6';
    if (width <= 9) return 'col-span-12 md:col-span-12 lg:col-span-9 xl:col-span-9';
    return 'col-span-12';
  };

  const getGridRowSpan = (height: number) => {
    // Map component height to Tailwind row spans (each span is ~100px)
    if (height <= 1) return 'row-span-3'; // Small charts
    if (height <= 2) return 'row-span-4'; // Medium charts
    if (height <= 3) return 'row-span-5'; // Large charts
    return 'row-span-6'; // Extra large charts
  };

  return (
    <div className="grid grid-cols-12 gap-4 auto-rows-[100px] p-4">
      {items.map((item, idx) => {
        const width = item.layout?.w || 6;
        const height = item.layout?.h || 2;
        
        const gridClasses = `${getGridColSpan(width)} ${getGridRowSpan(height)}`;
        const loadingClasses = item.isLoading ? 'opacity-70 pointer-events-none' : '';
        
        return (
          <div 
            key={item.id ?? idx} 
            className={`
              ${gridClasses}
              ${loadingClasses}
              card bg-base-100 shadow-md hover:shadow-lg transition-shadow duration-200
              relative overflow-hidden
            `}
          >
            {/* Loading indicator */}
            {item.isLoading && (
              <div className="absolute inset-0 bg-base-100/80 flex items-center justify-center z-10">
                <span className="loading loading-spinner loading-md text-primary"></span>
              </div>
            )}
            
            <div className="card-body p-4 h-full">
              <h3 className="card-title text-base font-semibold mb-2 text-base-content">
                {item.title}
              </h3>
              
              <div className="flex-1 min-h-0 overflow-hidden">
                <ComponentErrorBoundary>
                  <ChartComponent
                    type={item.chartType}
                    data={item.data}
                    apiUrl={item.apiUrl}
                    componentId={item.componentId}
                    className="w-full h-full"
                    disableAutoLoad={!!item.componentId}
                    ref={ref => onComponentRef && onComponentRef(item.id, ref)}
                    onLoaded={() => onComponentLoaded && onComponentLoaded(item.id)}
                  />
                </ComponentErrorBoundary>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ComponentGrid;
