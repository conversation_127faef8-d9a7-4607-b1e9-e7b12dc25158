/* Page Navigation Styles - Using Standard Tailwind Classes */

.page-navigation {
  @apply sticky top-20 z-40 border-b border-gray-200 bg-white;
}

.nav-content {
  @apply flex justify-between items-center p-4 max-w-7xl mx-auto gap-3;
}

.nav-left {
  @apply flex items-center flex-1 min-w-0;
}

.nav-right {
  @apply flex items-center gap-3 flex-shrink-0;
}

.page-tabs {
  @apply flex gap-2 overflow-x-auto flex-1 p-1;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.page-tabs::-webkit-scrollbar {
  display: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.no-pages-message {
  @apply p-4 text-center rounded-lg bg-gray-50;
}

.no-pages-message span {
  @apply text-gray-500 italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-content {
    @apply flex-col gap-3 p-4;
  }
  
  .nav-left {
    @apply w-full order-2;
  }
  
  .nav-right {
    @apply w-full justify-between order-1;
  }
  
  .page-tab {
    @apply text-xs px-4 py-2;
  }
  
  .scroll-button {
    @apply w-7 h-7;
  }
  
  .active-page-info {
    @apply p-2;
  }
  
  .active-page-label {
    @apply text-xs;
  }
  
  .active-page-name {
    @apply text-sm;
  }
  
  .refresh-text {
    @apply hidden;
  }
}

@media (max-width: 480px) {
  .nav-content {
    @apply p-3;
  }
  
  .page-tab {
    @apply text-xs px-3 py-2;
  }
  
  .scroll-button {
    @apply w-6 h-6;
  }
  
  .active-page-info {
    @apply flex-col gap-1 p-2 text-center;
  }
}
