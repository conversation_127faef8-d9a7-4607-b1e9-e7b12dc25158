import React, { useState } from 'react';
import { useTheme, ThemeKey } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { apiClient } from '../services/api';

interface ThemeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({ isOpen, onClose }) => {
  const { currentTheme, setTheme, availableThemes } = useTheme();
  const { user, updateUser } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);
  const [previewTheme, setPreviewTheme] = useState<ThemeKey | null>(null);

  const handleThemeSelect = async (themeKey: ThemeKey) => {
    if (themeKey === currentTheme) return;

    try {
      setIsUpdating(true);
      
      // Update locally first for immediate feedback
      setTheme(themeKey);
      
      // Update on server if user is logged in
      if (user) {
        try {
          const response = await apiClient.updateUserTheme(themeKey);
          if (response.user) {
            updateUser(response.user);
          }
        } catch (error) {
          // Don't revert local change - localStorage persistence is enough
        }
      }
      
      onClose();
    } catch (error) {
      // Theme update failed - no action needed as localStorage will persist
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePreview = (themeKey: ThemeKey) => {
    setPreviewTheme(themeKey);
    // Temporarily apply theme for preview
    document.documentElement.setAttribute('data-theme', themeKey);
  };

  const handlePreviewEnd = () => {
    setPreviewTheme(null);
    // Restore current theme
    document.documentElement.setAttribute('data-theme', currentTheme);
  };

  const getThemeIcon = (themeKey: ThemeKey) => {
    switch (themeKey) {
      case 'corporate':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
      case 'business':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M8 6V4a2 2 0 00-2 2v6.341" />
          </svg>
        );
      case 'dark':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        );
      case 'retro':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0v16a2 2 0 002 2h6a2 2 0 002-2V4M9 8h6m-6 4h6m-6 4h4" />
          </svg>
        );
      case 'cyberpunk':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
          </svg>
        );
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal modal-open">
      <div className="modal-box bg-base-100 text-base-content max-w-2xl">
        <h3 className="font-bold text-lg mb-4 text-base-content">테마 선택</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {availableThemes.map((theme) => (
            <div
              key={theme.key}
              className={`
                card cursor-pointer transition-all duration-200 hover:scale-105
                ${currentTheme === theme.key 
                  ? 'bg-primary text-primary-content border-primary' 
                  : 'bg-base-200 hover:bg-base-300 text-base-content border-base-300'
                }
              `}
              onClick={() => handleThemeSelect(theme.key)}
            >
              <div className="card-body p-4">
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 rounded-lg ${currentTheme === theme.key ? 'bg-primary-content/20' : 'bg-base-300'}`}>
                    {getThemeIcon(theme.key)}
                  </div>
                  <div>
                    <h4 className="font-semibold">{theme.name}</h4>
                    {currentTheme === theme.key && (
                      <div className="badge badge-secondary text-xs">현재 테마</div>
                    )}
                  </div>
                </div>
                <p className={`text-sm mb-3 ${currentTheme === theme.key ? 'text-primary-content/70' : 'text-base-content/70'}`}>
                  {theme.description}
                </p>
                
                {/* Theme color preview */}
                <div className="flex gap-1" data-theme={theme.key}>
                  <div className="w-4 h-4 rounded-full bg-primary"></div>
                  <div className="w-4 h-4 rounded-full bg-secondary"></div>
                  <div className="w-4 h-4 rounded-full bg-accent"></div>
                  <div className="w-4 h-4 rounded-full bg-neutral"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="modal-action">
          <button 
            className="btn btn-ghost" 
            onClick={onClose}
            disabled={isUpdating}
          >
            취소
          </button>
        </div>

        {isUpdating && (
          <div className="absolute inset-0 bg-base-100/80 flex items-center justify-center rounded-lg">
            <span className="loading loading-spinner loading-lg text-primary"></span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThemeSelector;