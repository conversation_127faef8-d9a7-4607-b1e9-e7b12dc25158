import React from 'react';

interface ComponentErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

interface ComponentErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ComponentErrorBoundary extends React.Component<
  ComponentErrorBoundaryProps,
  ComponentErrorBoundaryState
> {
  state: ComponentErrorBoundaryState = { hasError: false };

  static getDerivedStateFromError(error: Error): ComponentErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Component render error:', error, errorInfo);
    
    // Reset error boundary after a delay to allow recovery
    setTimeout(() => {
      this.setState({ hasError: false, error: undefined });
    }, 5000);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }
      return (
        <div className="chart-error p-3 text-center min-h-[200px] flex flex-col justify-center items-center border border-error rounded-lg bg-base-200">
          <div className="text-error mb-2">⚠️ 차트 렌더링 오류</div>
          <div className="text-sm text-base-content/70">
            {this.state.error?.message || '컴포넌트를 표시하는 중 오류가 발생했습니다.'}
          </div>
          <div className="text-xs text-base-content/50 mt-2">
            5초 후 자동으로 다시 시도됩니다.
          </div>
        </div>
      );
    }
    return this.props.children;
  }
}

export default ComponentErrorBoundary;

