import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import DashboardLayout from './DashboardLayout';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  const getRoleDisplayName = (role: 'regular' | 'org_admin' | 'super_admin'): string => {
    const roleNames = {
      'regular': '일반 사용자',
      'org_admin': '조직 관리자',
      'super_admin': '운영 관리자',
    };
    return roleNames[role];
  };

  return (
    <DashboardLayout>
      <div className="bg-base-100 p-8 rounded-lg shadow-lg">
        <h2 className="text-3xl font-bold text-base-content mb-6">
          대시보드 메인
        </h2>
        <p className="text-base-content/70 text-base leading-relaxed mb-6">
          대시보드 컴포넌트가 여기에 구현될 예정입니다. 
          페이지 네비게이션을 통해 다른 대시보드 페이지로 이동할 수 있습니다.
        </p>
        
        {user && (
          <div className="mt-8 p-6 bg-base-200 rounded-lg border border-base-300">
            <h3 className="text-xl font-semibold text-base-content mb-4">
              사용자 정보
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <strong className="text-base-content">이메일:</strong>
                <span className="ml-2 text-base-content/70">{user.email}</span>
              </div>
              <div>
                <strong className="text-base-content">역할:</strong>
                <span className="ml-2 text-base-content/70">{getRoleDisplayName(user.role)}</span>
              </div>
              <div>
                <strong className="text-base-content">조직:</strong>
                <span className="ml-2 text-base-content/70">{user.organization?.name || '없음'}</span>
              </div>
              <div>
                <strong className="text-base-content">가입일:</strong>
                <span className="ml-2 text-base-content/70">
                  {new Date(user.created_at).toLocaleDateString('ko-KR')}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;