import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ThemeSelector from '../ThemeSelector';
import { ThemeProvider, AVAILABLE_THEMES } from '../../contexts/ThemeContext';

// Mock the API client
jest.mock('../../services/api', () => ({
  apiClient: {
    updateUserTheme: jest.fn(),
  },
}));

// Mock the AuthContext
const mockUpdateUser = jest.fn();
const mockUseAuth = jest.fn();
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

// Get the mocked API client
const { apiClient } = require('../../services/api');

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock console methods to avoid noise in tests
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

const renderThemeSelector = (props = {}) => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    ...props,
  };

  return render(
    <ThemeProvider>
      <ThemeSelector {...defaultProps} />
    </ThemeProvider>
  );
};

describe('ThemeSelector', () => {
  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    role: 'regular',
    organization: 'Test Org',
    ui_theme: 'corporate',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('corporate');
    mockUseAuth.mockReturnValue({
      user: mockUser,
      updateUser: mockUpdateUser,
    });
    apiClient.updateUserTheme.mockResolvedValue({ user: { ...mockUser, ui_theme: 'dark' } });
    document.documentElement.removeAttribute('data-theme');
  });

  describe('Rendering', () => {
    test('renders when open', () => {
      renderThemeSelector({ isOpen: true });
      
      expect(screen.getByText('테마 선택')).toBeInTheDocument();
      expect(screen.getByText('취소')).toBeInTheDocument();
    });

    test('does not render when closed', () => {
      renderThemeSelector({ isOpen: false });
      
      expect(screen.queryByText('테마 선택')).not.toBeInTheDocument();
    });

    test('renders all available themes', () => {
      renderThemeSelector();
      
      AVAILABLE_THEMES.forEach(theme => {
        expect(screen.getByText(theme.name)).toBeInTheDocument();
        expect(screen.getByText(theme.description)).toBeInTheDocument();
      });
    });

    test('shows current theme indicator', () => {
      renderThemeSelector();
      
      expect(screen.getByText('현재 테마')).toBeInTheDocument();
    });

    test('renders theme icons for all themes', () => {
      renderThemeSelector();
      
      // Check that each theme card has an icon (svg element)
      const themeCards = screen.getAllByRole('generic').filter(el => 
        el.className.includes('card') && el.className.includes('cursor-pointer')
      );
      
      expect(themeCards).toHaveLength(AVAILABLE_THEMES.length);
    });

    test('renders theme color previews', () => {
      renderThemeSelector();
      
      // Each theme should have color preview circles
      AVAILABLE_THEMES.forEach(theme => {
        const themeCard = screen.getByText(theme.name).closest('.card');
        const themePreview = themeCard?.querySelector('[data-theme]');
        expect(themePreview).toHaveAttribute('data-theme', theme.key);
      });
    });
  });

  describe('Theme Selection', () => {
    test('selects theme and calls onClose', async () => {
      const onCloseMock = jest.fn();
      renderThemeSelector({ onClose: onCloseMock });
      
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      expect(darkThemeCard).toBeInTheDocument();
      
      await userEvent.click(darkThemeCard!);
      
      await waitFor(() => {
        expect(apiClient.updateUserTheme).toHaveBeenCalledWith('dark');
        expect(onCloseMock).toHaveBeenCalled();
      });
    });

    test('updates theme locally immediately', async () => {
      renderThemeSelector();
      
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      
      await userEvent.click(darkThemeCard!);
      
      // Theme should be applied to document immediately
      await waitFor(() => {
        expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
      });
    });

    test('updates user context when API call succeeds', async () => {
      const updatedUser = { ...mockUser, ui_theme: 'dark' };
      apiClient.updateUserTheme.mockResolvedValue({ user: updatedUser });
      
      renderThemeSelector();
      
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      await userEvent.click(darkThemeCard!);
      
      await waitFor(() => {
        expect(mockUpdateUser).toHaveBeenCalledWith(updatedUser);
      });
    });

    test('handles API failure gracefully', async () => {
      apiClient.updateUserTheme.mockRejectedValue(new Error('API Error'));
      const onCloseMock = jest.fn();
      
      renderThemeSelector({ onClose: onCloseMock });
      
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      await userEvent.click(darkThemeCard!);
      
      // Should still close and apply theme locally
      await waitFor(() => {
        expect(onCloseMock).toHaveBeenCalled();
        expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
      });
      
      // Should not update user context on API failure
      expect(mockUpdateUser).not.toHaveBeenCalled();
    });

    test('does not make API call when user is not logged in', async () => {
      mockUseAuth.mockReturnValue({
        user: null,
        updateUser: mockUpdateUser,
      });
      
      const onCloseMock = jest.fn();
      renderThemeSelector({ onClose: onCloseMock });
      
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      await userEvent.click(darkThemeCard!);
      
      await waitFor(() => {
        expect(onCloseMock).toHaveBeenCalled();
      });
      
      expect(apiClient.updateUserTheme).not.toHaveBeenCalled();
      expect(mockUpdateUser).not.toHaveBeenCalled();
    });

    test('ignores click on already selected theme', async () => {
      const onCloseMock = jest.fn();
      renderThemeSelector({ onClose: onCloseMock });
      
      // Click on corporate theme (current theme)
      const corporateThemeCard = screen.getByText('기업용').closest('.card');
      await userEvent.click(corporateThemeCard!);
      
      // Should not make API call or close modal
      expect(apiClient.updateUserTheme).not.toHaveBeenCalled();
      expect(onCloseMock).not.toHaveBeenCalled();
    });
  });

  describe('Loading States', () => {
    test('shows loading overlay during theme update', async () => {
      // Make API call take some time
      apiClient.updateUserTheme.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ user: mockUser }), 100))
      );
      
      renderThemeSelector();
      
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      await userEvent.click(darkThemeCard!);
      
      // Should show loading spinner
      expect(screen.getByText('', { selector: '.loading-spinner' })).toBeInTheDocument();
      
      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByText('', { selector: '.loading-spinner' })).not.toBeInTheDocument();
      });
    });

    test('disables cancel button during update', async () => {
      apiClient.updateUserTheme.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ user: mockUser }), 100))
      );
      
      renderThemeSelector();
      
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      const cancelButton = screen.getByText('취소');
      
      await userEvent.click(darkThemeCard!);
      
      // Cancel button should be disabled during update
      expect(cancelButton).toBeDisabled();
      
      // Wait for update to complete
      await waitFor(() => {
        expect(cancelButton).not.toBeDisabled();
      });
    });
  });

  describe('Modal Behavior', () => {
    test('calls onClose when cancel button is clicked', async () => {
      const onCloseMock = jest.fn();
      renderThemeSelector({ onClose: onCloseMock });
      
      const cancelButton = screen.getByText('취소');
      await userEvent.click(cancelButton);
      
      expect(onCloseMock).toHaveBeenCalled();
    });

    test('applies correct CSS classes for current theme', () => {
      renderThemeSelector();
      
      const corporateThemeCard = screen.getByText('기업용').closest('.card');
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      
      // Current theme should have primary styling
      expect(corporateThemeCard).toHaveClass('bg-primary', 'text-primary-content');
      
      // Other themes should have base styling
      expect(darkThemeCard).toHaveClass('bg-base-200', 'text-base-content');
    });

    test('has proper responsive grid layout', () => {
      renderThemeSelector();
      
      const themeGrid = screen.getByText('테마 선택').nextElementSibling;
      expect(themeGrid).toHaveClass('grid', 'grid-cols-1', 'md:grid-cols-2');
    });
  });

  describe('Accessibility', () => {
    test('has proper modal structure', () => {
      renderThemeSelector();
      
      const modal = screen.getByText('테마 선택').closest('.modal');
      expect(modal).toHaveClass('modal', 'modal-open');
    });

    test('theme cards are keyboard accessible', async () => {
      renderThemeSelector();
      
      const darkThemeCard = screen.getByText('다크 모드').closest('.card');
      
      // Cards should be clickable (they don't need to be focusable since they're click targets)
      expect(darkThemeCard).toHaveClass('cursor-pointer');
      
      // Should respond to click events
      await userEvent.click(darkThemeCard!);
      
      await waitFor(() => {
        expect(apiClient.updateUserTheme).toHaveBeenCalledWith('dark');
      });
    });

    test('has proper ARIA labels and roles', () => {
      renderThemeSelector();
      
      // Modal should be present with proper structure
      const modal = screen.getByText('테마 선택').closest('.modal');
      expect(modal).toBeInTheDocument();
      
      // Buttons should have proper roles
      expect(screen.getByRole('button', { name: '취소' })).toBeInTheDocument();
      
      // Modal title should be a heading
      expect(screen.getByRole('heading', { name: '테마 선택' })).toBeInTheDocument();
    });
  });
});