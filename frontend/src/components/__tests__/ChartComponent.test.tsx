import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import ChartComponent from '../ChartComponent';

// Mock API client
jest.mock('../../services/api', () => ({
  apiClient: {
    getComponentData: jest.fn(),
  },
}));

// Mock Tremor chart components
jest.mock('@tremor/react', () => {
  const React = require('react');
  return {
    AreaChart: ({ data }: any) => <div data-testid="chart">{JSON.stringify(data)}</div>,
    BarChart: ({ data }: any) => <div data-testid="chart">{JSON.stringify(data)}</div>,
    LineChart: ({ data }: any) => <div data-testid="chart">{JSON.stringify(data)}</div>,
    DonutChart: ({ data }: any) => <div data-testid="chart">{JSON.stringify(data)}</div>,
    ScatterChart: ({ data }: any) => <div data-testid="chart">{JSON.stringify(data)}</div>,
    Card: React.forwardRef(({ children, style, className }: any, ref: any) => 
      <div ref={ref} data-testid="card" style={style} className={className}>{children}</div>
    ),
    Title: ({ children }: any) => <h3 data-testid="title">{children}</h3>,
    Text: ({ children }: any) => <p data-testid="text">{children}</p>,
    Metric: ({ children }: any) => <div data-testid="metric">{children}</div>,
    Flex: ({ children }: any) => <div data-testid="flex">{children}</div>,
    BadgeDelta: ({ children }: any) => <span data-testid="badge">{children}</span>,
  };
});

// Mock ResizeObserver
(global as any).ResizeObserver = class ResizeObserver {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
  constructor(callback: ResizeObserverCallback) {}
};

describe('ChartComponent', () => {
  beforeEach(() => {
    (global.fetch as any) = jest.fn();
  });

  test('fetches data from api and renders chart', async () => {
    const mockData = { categories: ['Jan'], series: [{ name: 'Sales', data: [100] }] };
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockData),
    });

    const { container } = render(<ChartComponent type="bar" apiUrl="/api/data" />);

    await waitFor(() => {
      expect(screen.getByTestId('chart')).toBeInTheDocument();
    });

    expect(global.fetch).toHaveBeenCalledWith('/api/data');
    expect(screen.getByTestId('chart').textContent).toContain('Sales');
    const timestamp = container.querySelector('.absolute.bottom-2.right-2') as HTMLElement;
    expect(timestamp).toBeTruthy();
  });

  test('applies render configuration for fixed height', () => {
    const mockData = { data: [{ name: 'Test', value: 100 }] };
    const renderConfig = { fixedHeight: 300 };

    const { container } = render(
      <ChartComponent 
        type="bar" 
        data={mockData} 
        renderConfig={renderConfig}
      />
    );

    const cardElement = container.querySelector('[data-testid="card"]') as HTMLElement;
    expect(cardElement).toHaveStyle('height: 300px');
  });

  test('applies render configuration for aspect ratio', () => {
    const mockData = { data: [{ name: 'Test', value: 100 }] };
    const renderConfig = { aspectRatio: 2.0, minHeight: 200 };

    // Mock container width
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 400,
    });

    const { container } = render(
      <ChartComponent 
        type="bar" 
        data={mockData} 
        renderConfig={renderConfig}
      />
    );

    const cardElement = container.querySelector('[data-testid="card"]') as HTMLElement;
    // With aspect ratio 2.0 and width 400, height should be 200px (but respecting minHeight)
    expect(cardElement).toHaveStyle('height: 200px');
  });
});
