import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import LoginPage from '../LoginPage';

// Mock useAuth hook
const mockLogin = jest.fn();
const mockLoginWithGoogle = jest.fn();
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    login: mockLogin,
    loginWithGoogle: mockLoginWithGoogle,
    isAuthenticated: false,
  }),
}));

const renderWithRouter = (ui: React.ReactElement) => {
  return render(
    <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      {ui}
    </BrowserRouter>
  );
};

describe('LoginPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders login form and submits credentials', async () => {
    renderWithRouter(<LoginPage />);

    fireEvent.change(screen.getByLabelText('이메일'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText('비밀번호'), {
      target: { value: 'password123' },
    });

    fireEvent.click(screen.getByRole('button', { name: '로그인' }));

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
    });
  });

  test('shows validation errors for empty fields', async () => {
    renderWithRouter(<LoginPage />);

    fireEvent.click(screen.getByRole('button', { name: '로그인' }));

    expect(await screen.findByText('이메일을 입력해주세요.')).toBeInTheDocument();
    expect(await screen.findByText('비밀번호를 입력해주세요.')).toBeInTheDocument();
    expect(mockLogin).not.toHaveBeenCalled();
  });
});
