import React from 'react';
import { render, screen } from '@testing-library/react';
import PermissionGuard from '../PermissionGuard';
import { User } from '../../types/auth';

// Mock users for testing
const mockUsers: Record<string, User> = {
  regular: {
    id: '1',
    email: '<EMAIL>',
    role: 'regular',
    organization: 'test-org',
    created_at: '2023-01-01T00:00:00Z',
  },
  orgAdmin: {
    id: '2',
    email: '<EMAIL>',
    role: 'org_admin',
    organization: 'test-org',
    created_at: '2023-01-01T00:00:00Z',
  },
  superAdmin: {
    id: '3',
    email: '<EMAIL>',
    role: 'super_admin',
    created_at: '2023-01-01T00:00:00Z',
  },
};

// Mock the useAuth hook to return different users
const mockUseAuth = jest.fn();
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

describe('PermissionGuard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Role-based access control', () => {
    it('should render content for users with sufficient role', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.orgAdmin });

      render(
        <PermissionGuard role="org_admin">
          <div>Admin Content</div>
        </PermissionGuard>
      );

      expect(screen.getByText('Admin Content')).toBeInTheDocument();
    });

    it('should not render content for users with insufficient role', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.regular });

      render(
        <PermissionGuard role="org_admin">
          <div>Admin Content</div>
        </PermissionGuard>
      );

      expect(screen.queryByText('Admin Content')).not.toBeInTheDocument();
    });

    it('should render fallback content when access is denied', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.regular });

      render(
        <PermissionGuard 
          role="org_admin"
          fallback={<div>Access Denied</div>}
        >
          <div>Admin Content</div>
        </PermissionGuard>
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.queryByText('Admin Content')).not.toBeInTheDocument();
    });
  });

  describe('Permission-based access control', () => {
    it('should render content for users with required permission', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.orgAdmin });

      render(
        <PermissionGuard permission="manage_users">
          <div>User Management</div>
        </PermissionGuard>
      );

      expect(screen.getByText('User Management')).toBeInTheDocument();
    });

    it('should not render content for users without required permission', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.regular });

      render(
        <PermissionGuard permission="manage_users">
          <div>User Management</div>
        </PermissionGuard>
      );

      expect(screen.queryByText('User Management')).not.toBeInTheDocument();
    });
  });

  describe('Multiple permissions', () => {
    it('should render content when user has any of the required permissions (requireAll: false)', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.orgAdmin });

      render(
        <PermissionGuard 
          permissions={['manage_users', 'manage_system']}
          requireAll={false}
        >
          <div>Management Content</div>
        </PermissionGuard>
      );

      expect(screen.getByText('Management Content')).toBeInTheDocument();
    });

    it('should not render content when user lacks all required permissions (requireAll: true)', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.orgAdmin });

      render(
        <PermissionGuard 
          permissions={['manage_users', 'manage_system']}
          requireAll={true}
        >
          <div>Management Content</div>
        </PermissionGuard>
      );

      // org_admin has manage_users but not manage_system
      expect(screen.queryByText('Management Content')).not.toBeInTheDocument();
    });

    it('should render content when super admin has all required permissions (requireAll: true)', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.superAdmin });

      render(
        <PermissionGuard 
          permissions={['manage_users', 'manage_system']}
          requireAll={true}
        >
          <div>Super Admin Content</div>
        </PermissionGuard>
      );

      expect(screen.getByText('Super Admin Content')).toBeInTheDocument();
    });
  });

  describe('Error display', () => {
    it('should show error message when showError is true and access is denied', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.regular });

      render(
        <PermissionGuard 
          permission="manage_users"
          showError={true}
        >
          <div>User Management</div>
        </PermissionGuard>
      );

      expect(screen.getByText('접근 권한이 없습니다')).toBeInTheDocument();
      expect(screen.getByText(/사용자 관리 권한이.*필요합니다/)).toBeInTheDocument();
      expect(screen.queryByText('User Management')).not.toBeInTheDocument();
    });

    it('should show current user role in error message', () => {
      mockUseAuth.mockReturnValue({ user: mockUsers.regular });

      render(
        <PermissionGuard 
          permission="manage_users"
          showError={true}
        >
          <div>User Management</div>
        </PermissionGuard>
      );

      expect(screen.getByText('현재 권한: 일반 사용자')).toBeInTheDocument();
    });
  });

  describe('Unauthenticated users', () => {
    it('should not render content for unauthenticated users', () => {
      mockUseAuth.mockReturnValue({ user: null });

      render(
        <PermissionGuard permission="view_dashboard">
          <div>Dashboard Content</div>
        </PermissionGuard>
      );

      expect(screen.queryByText('Dashboard Content')).not.toBeInTheDocument();
    });
  });
});