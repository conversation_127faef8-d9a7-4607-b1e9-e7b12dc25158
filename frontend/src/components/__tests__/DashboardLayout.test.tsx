import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';

// Mock API client for logout verification and data fetching
jest.mock('../../services/api', () => {
  const mockPages = [
    { id: 1, name: '매출 대시보드', permission_level: 'regular' },
    { id: 2, name: '마케팅 분석', permission_level: 'regular' },
    { id: 3, name: '운영 현황', permission_level: 'org_admin' },
    { id: 4, name: '관리자 리포트', permission_level: 'org_admin' },
  ];

  const mockComponents: Record<string, any[]> = {
    '1': [
      {
        id: 101,
        component_template: { name: '월별 매출 추이', chart_type: 'line' },
        grid_position: { width: 6, height: 2 },
      },
    ],
    '2': [
      {
        id: 201,
        component_template: { name: '캠페인별 전환율', chart_type: 'bar' },
        grid_position: { width: 6, height: 2 },
      },
    ],
  };

  return {
    apiClient: {
      logout: jest.fn().mockResolvedValue(undefined),
      getAccessiblePages: jest.fn().mockResolvedValue({ pages: mockPages, user_role: 'regular' }),
      getPageComponents: jest.fn((pageId: string) => Promise.resolve(mockComponents[pageId] || [])),
    },
  };
});

// Mock ChartComponent to avoid rendering actual charts in tests
jest.mock('../ChartComponent', () => {
  const React = require('react');
  return React.forwardRef((props, ref) => {
    React.useImperativeHandle(ref, () => ({
      refresh: jest.fn().mockResolvedValue(undefined),
    }));
    return React.createElement('div', { 'data-testid': 'chart' });
  });
});

// Mock useAuth hook to provide user context
const mockUseAuth = jest.fn();
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth(),
}));

const DashboardLayout = require('../DashboardLayout').default;
const { apiClient } = require('../../services/api');

const renderWithRouter = (ui: React.ReactElement) => {
  return render(
    <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      {ui}
    </BrowserRouter>
  );
};

describe('DashboardLayout', () => {
  const defaultUser = {
    id: '1',
    email: '<EMAIL>',
    role: 'regular',
    organization: 'Test Org',
    created_at: '2024-01-01T00:00:00Z',
  };
  let logoutMock: jest.Mock;

  beforeEach(() => {
    logoutMock = jest.fn(() => apiClient.logout());
    mockUseAuth.mockReturnValue({ user: defaultUser, logout: logoutMock });
    apiClient.logout.mockClear();
    apiClient.getAccessiblePages.mockClear();
    apiClient.getPageComponents.mockClear();
  });

  test('renders dashboard header with user info', () => {
    renderWithRouter(<DashboardLayout />);
    expect(screen.getByText('Dynamic BI Dashboard')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('(일반 사용자)')).toBeInTheDocument();
  });

  test('renders page navigation with pages from API', async () => {
    renderWithRouter(<DashboardLayout />);
    await waitFor(() => {
      expect(screen.getAllByText('매출 대시보드').length).toBeGreaterThan(0);
      expect(screen.getAllByText('마케팅 분석').length).toBeGreaterThan(0);
    });
    // Regular user should not see admin pages
    expect(screen.queryByText('운영 현황')).not.toBeInTheDocument();
    expect(screen.queryByText('관리자 리포트')).not.toBeInTheDocument();
  });

  test('switches page content when clicking page tabs', async () => {
    renderWithRouter(<DashboardLayout />);
    const marketingTab = screen.getByText('마케팅 분석');
    fireEvent.click(marketingTab);
    await waitFor(() => {
      expect(screen.getByText('캠페인별 전환율')).toBeInTheDocument();
    });
  });

  test('shows refresh functionality', async () => {
    renderWithRouter(<DashboardLayout />);
    const refreshButton = screen.getByTitle(/새로고침/);
    expect(refreshButton).toBeInTheDocument();
    fireEvent.click(refreshButton);
    await waitFor(() => {
      expect(refreshButton).toBeDisabled();
    });
  });

  test('handles logout functionality', async () => {
    const { apiClient } = require('../../services/api');
    renderWithRouter(<DashboardLayout />);
    const logoutButton = screen.getByText('로그아웃');
    fireEvent.click(logoutButton);
    await waitFor(() => {
      expect(apiClient.logout).toHaveBeenCalled();
      expect(logoutMock).toHaveBeenCalled();
    });
  });

  test('shows different pages for different user roles', async () => {
    mockUseAuth.mockReturnValue({ user: { ...defaultUser, role: 'org_admin' }, logout: logoutMock });
    renderWithRouter(<DashboardLayout />);
    await waitFor(() => {
      expect(screen.getAllByText('매출 대시보드').length).toBeGreaterThan(0);
      expect(screen.getAllByText('마케팅 분석').length).toBeGreaterThan(0);
      expect(screen.getAllByText('운영 현황').length).toBeGreaterThan(0);
      expect(screen.getAllByText('관리자 리포트').length).toBeGreaterThan(0);
    });
  });

  test('renders responsive mobile menu toggle', () => {
    renderWithRouter(<DashboardLayout />);
    const mobileToggle = screen.getByLabelText('메뉴 토글');
    expect(mobileToggle).toBeInTheDocument();
  });
});

