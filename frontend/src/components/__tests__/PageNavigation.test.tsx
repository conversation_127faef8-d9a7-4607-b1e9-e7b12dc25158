import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import PageNavigation from '../PageNavigation';

const mockPages = [
  { id: '1', name: '매출 대시보드', permission_level: 'regular' },
  { id: '2', name: '마케팅 분석', permission_level: 'regular' },
  { id: '3', name: '운영 현황', permission_level: 'org_admin' },
];

const defaultProps = {
  pages: mockPages,
  activePage: '1',
  onPageSelect: jest.fn(),
  isRefreshing: false,
  onRefresh: jest.fn(),
};

describe('PageNavigation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders all pages as tabs', () => {
    render(<PageNavigation {...defaultProps} />);

    const tabLabels = ['매출 대시보드', '마케팅 분석', '운영 현황'];
    tabLabels.forEach(label => {
      expect(screen.getAllByText(label)[0]).toBeInTheDocument();
    });
  });

  test('highlights active page', () => {
    render(<PageNavigation {...defaultProps} />);

    const activeTab = screen.getAllByText('매출 대시보드')[0].closest('button');
    expect(activeTab).toHaveClass('active');

    const inactiveTab = screen.getByText('마케팅 분석').closest('button');
    expect(inactiveTab).not.toHaveClass('active');
  });

  test('calls onPageSelect when clicking a tab', () => {
    const onPageSelect = jest.fn();
    render(<PageNavigation {...defaultProps} onPageSelect={onPageSelect} />);

    const marketingTab = screen.getByText('마케팅 분석');
    fireEvent.click(marketingTab);

    expect(onPageSelect).toHaveBeenCalledWith('2');
  });

  test('does not call onPageSelect when clicking active tab', () => {
    const onPageSelect = jest.fn();
    render(<PageNavigation {...defaultProps} onPageSelect={onPageSelect} />);

    const activeTab = screen.getAllByText('매출 대시보드')[0];
    fireEvent.click(activeTab);

    expect(onPageSelect).not.toHaveBeenCalled();
  });

  test('shows refresh button and calls onRefresh', () => {
    const onRefresh = jest.fn();
    render(<PageNavigation {...defaultProps} onRefresh={onRefresh} />);

    const refreshButton = screen.getByTitle(/새로고침/);
    expect(refreshButton).toBeInTheDocument();

    fireEvent.click(refreshButton);
    expect(onRefresh).toHaveBeenCalledWith('1');
  });

  test('disables tabs and refresh button when refreshing', () => {
    render(<PageNavigation {...defaultProps} isRefreshing={true} />);

    const tabs = screen.getAllByRole('button').filter(btn => 
      mockPages.some(page => btn.textContent?.includes(page.name))
    );
    tabs.forEach(tab => {
      expect(tab).toBeDisabled();
    });

    const refreshButton = screen.getByTitle(/새로고침/);
    expect(refreshButton).toBeDisabled();
  });

  test('shows active page info', () => {
    render(<PageNavigation {...defaultProps} />);

    expect(screen.getByText('현재 페이지:')).toBeInTheDocument();
    expect(screen.getAllByText('매출 대시보드')[1]).toBeInTheDocument();
  });

  test('shows scroll buttons', () => {
    render(<PageNavigation {...defaultProps} />);

    expect(screen.getByTitle('왼쪽으로 스크롤')).toBeInTheDocument();
    expect(screen.getByTitle('오른쪽으로 스크롤')).toBeInTheDocument();
  });

  test('shows no pages message when pages array is empty', () => {
    render(<PageNavigation {...defaultProps} pages={[]} />);

    expect(screen.getByText('접근 가능한 페이지가 없습니다.')).toBeInTheDocument();
  });

  test('handles scroll button clicks', () => {
    render(<PageNavigation {...defaultProps} />);

    const leftScrollButton = screen.getByTitle('왼쪽으로 스크롤');
    const rightScrollButton = screen.getByTitle('오른쪽으로 스크롤');

    // These should not throw errors
    fireEvent.click(leftScrollButton);
    fireEvent.click(rightScrollButton);
  });
});