import React from 'react';
import { getMobileFirstColumnClasses, getMobileFirstPaddingClasses, getMobileFirstContainerClasses } from '../../utils/gridUtils';

/**
 * Demo component to visually test responsive grid behavior
 * This component demonstrates the mobile-first responsive grid system
 */
const ResponsiveGridDemo: React.FC = () => {
  const demoItems = [
    { id: 1, title: 'Widget 1', span: 12 },
    { id: 2, title: 'Widget 2', span: 6 },
    { id: 3, title: 'Widget 3', span: 6 },
    { id: 4, title: 'Widget 4', span: 4 },
    { id: 5, title: 'Widget 5', span: 4 },
    { id: 6, title: 'Widget 6', span: 4 },
  ];

  return (
    <div className="responsive-grid-demo p-4">
      <h1 className="text-2xl font-bold mb-4">Responsive Grid Demo</h1>
      <p className="mb-6 text-gray-600">
        Resize your browser window to see the responsive behavior:
        <br />
        • Mobile (&lt;640px): Single column
        <br />
        • Tablet (640px-1024px): 2 columns
        <br />
        • Desktop (1024px-1280px): 3 columns
        <br />
        • Large Desktop (&gt;1280px): 4 columns
      </p>
      
      <div className="grid w-full max-w-none overflow-hidden min-h-0 gap-2 sm:gap-3 lg:gap-4 px-4 sm:px-6 lg:px-8 transition-all duration-300 ease-in-out grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {demoItems.map((item) => {
          const containerClasses = getMobileFirstContainerClasses();
          const columnClasses = getMobileFirstColumnClasses(item.span);
          const paddingClasses = getMobileFirstPaddingClasses();
          
          return (
            <div
              key={item.id}
              className={`
                card bg-base-100 shadow-lg overflow-hidden flex flex-col
                ${containerClasses}
                ${columnClasses}
                ${paddingClasses}
                transition-all duration-300 ease-in-out
                border-2 border-primary/20
              `}
            >
              <h3 className="text-lg font-medium mb-2">{item.title}</h3>
              <div className="flex-1 bg-primary/10 rounded p-2 text-center">
                <p className="text-sm text-gray-600">
                  Original span: {item.span}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Responsive classes: {columnClasses}
                </p>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="mt-8 p-4 bg-base-200 rounded">
        <h2 className="text-lg font-semibold mb-2">Current Breakpoint Info</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div className="block sm:hidden bg-error text-error-content p-2 rounded">
            Mobile (&lt;640px)
          </div>
          <div className="hidden sm:block md:hidden bg-warning text-warning-content p-2 rounded">
            Small (640px-768px)
          </div>
          <div className="hidden md:block lg:hidden bg-info text-info-content p-2 rounded">
            Medium (768px-1024px)
          </div>
          <div className="hidden lg:block xl:hidden bg-success text-success-content p-2 rounded">
            Large (1024px-1280px)
          </div>
          <div className="hidden xl:block bg-primary text-primary-content p-2 rounded">
            Extra Large (&gt;1280px)
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveGridDemo;