import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  Permission, 
  Role, 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions, 
  hasRole,
  getRoleDisplayName,
  getPermissionDisplayName
} from '../utils/permissions';

interface PermissionGuardProps {
  children: React.ReactNode;
  
  // Permission-based access control
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean; // If true, requires all permissions; if false, requires any (default: false)
  
  // Role-based access control
  role?: Role;
  
  // Fallback content
  fallback?: React.ReactNode;
  
  // Show detailed error message
  showError?: boolean;
}

/**
 * PermissionGuard component - Controls access to UI elements based on user permissions/roles
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  permissions,
  requireAll = false,
  role,
  fallback = null,
  showError = false
}) => {
  const { user } = useAuth();

  // Check access based on provided criteria
  const hasAccess = (): boolean => {
    if (!user) return false;

    // Check role-based access
    if (role && !hasRole(user, role)) {
      return false;
    }

    // Check single permission
    if (permission && !hasPermission(user, permission)) {
      return false;
    }

    // Check multiple permissions
    if (permissions && permissions.length > 0) {
      if (requireAll) {
        if (!hasAllPermissions(user, permissions)) {
          return false;
        }
      } else {
        if (!hasAnyPermission(user, permissions)) {
          return false;
        }
      }
    }

    return true;
  };

  // If user has access, render children
  if (hasAccess()) {
    return <>{children}</>;
  }

  // If fallback is provided, render it
  if (fallback !== null) {
    return <>{fallback}</>;
  }

  // If showError is true, show detailed error message
  if (showError) {
    const getRequiredAccessText = (): string => {
      const parts: string[] = [];

      if (role) {
        parts.push(`${getRoleDisplayName(role)} 권한`);
      }

      if (permission) {
        parts.push(`${getPermissionDisplayName(permission)} 권한`);
      }

      if (permissions && permissions.length > 0) {
        const permissionTexts = permissions.map(getPermissionDisplayName);
        if (requireAll) {
          parts.push(`다음 권한들: ${permissionTexts.join(', ')}`);
        } else {
          parts.push(`다음 권한 중 하나: ${permissionTexts.join(', ')}`);
        }
      }

      return parts.join(' 및 ');
    };

    return (
      <div className="p-4 bg-error/10 border border-error/30 rounded-lg text-error text-center">
        <h4 className="m-0 mb-2">접근 권한이 없습니다</h4>
        <p className="m-0 text-sm">
          이 기능을 사용하려면 {getRequiredAccessText()}이(가) 필요합니다.
        </p>
        {user && (
          <p className="mt-2 mb-0 text-xs text-base-content/60">
            현재 권한: {getRoleDisplayName(user.role)}
          </p>
        )}
      </div>
    );
  }

  // Default: render nothing
  return null;
};

export default PermissionGuard;