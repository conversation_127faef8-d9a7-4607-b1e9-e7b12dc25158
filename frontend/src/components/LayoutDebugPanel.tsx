import React, { useState } from 'react';
import { DashboardLayout } from '../types/dashboard';
import { validateLayoutConfig, formatValidationResults, ValidationResult } from '../utils/layoutValidation';

interface LayoutDebugPanelProps {
  layout: DashboardLayout | null;
  isVisible?: boolean;
  onToggle?: () => void;
}

const LayoutDebugPanel: React.FC<LayoutDebugPanelProps> = ({
  layout,
  isVisible = false,
  onToggle
}) => {
  const [activeTab, setActiveTab] = useState<'validation' | 'structure' | 'widgets' | 'rows'>('validation');
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);

  // Run validation when layout changes
  React.useEffect(() => {
    if (layout) {
      const result = validateLayoutConfig(layout);
      setValidationResult(result);
    } else {
      setValidationResult(null);
    }
  }, [layout]);

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 z-50 btn btn-sm btn-primary opacity-70 hover:opacity-100"
        title="Open Layout Debug Panel"
      >
        🔧 Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 bg-base-100 border border-base-300 rounded-lg shadow-xl overflow-hidden">
      <div className="flex items-center justify-between p-3 bg-base-200 border-b border-base-300">
        <h3 className="text-sm font-semibold text-base-content">Layout Debug Panel</h3>
        <button
          onClick={onToggle}
          className="btn btn-xs btn-ghost"
          title="Close Debug Panel"
        >
          ✕
        </button>
      </div>

      <div className="flex border-b border-base-300">
        {(['validation', 'structure', 'widgets', 'rows'] as const).map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-3 py-2 text-xs font-medium capitalize transition-colors ${
              activeTab === tab
                ? 'bg-primary text-primary-content'
                : 'bg-base-100 text-base-content hover:bg-base-200'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      <div className="p-3 overflow-auto max-h-64 text-xs">
        {!layout ? (
          <div className="text-center text-base-content/60 py-4">
            No layout data available
          </div>
        ) : (
          <>
            {activeTab === 'validation' && (
              <ValidationTab validationResult={validationResult} />
            )}
            {activeTab === 'structure' && (
              <StructureTab layout={layout} />
            )}
            {activeTab === 'widgets' && (
              <WidgetsTab widgets={layout.widgets} />
            )}
            {activeTab === 'rows' && (
              <RowsTab rows={layout.rows} />
            )}
          </>
        )}
      </div>
    </div>
  );
};

const ValidationTab: React.FC<{ validationResult: ValidationResult | null }> = ({ validationResult }) => {
  if (!validationResult) {
    return <div className="text-base-content/60">Running validation...</div>;
  }

  return (
    <div className="space-y-3">
      <div className={`p-2 rounded text-xs ${
        validationResult.isValid 
          ? 'bg-success/20 text-success-content border border-success/30' 
          : 'bg-error/20 text-error-content border border-error/30'
      }`}>
        {validationResult.isValid ? '✅ Valid' : '❌ Invalid'} Layout Configuration
      </div>

      {validationResult.errors.length > 0 && (
        <div>
          <h4 className="font-semibold text-error mb-2">Errors ({validationResult.errors.length})</h4>
          <div className="space-y-2">
            {validationResult.errors.map((error, index) => (
              <div key={index} className="p-2 bg-error/10 border border-error/20 rounded">
                <div className="font-medium text-error">{error.field}</div>
                <div className="text-base-content/80">{error.message}</div>
                {error.suggestions && error.suggestions.length > 0 && (
                  <div className="mt-1 text-base-content/60">
                    💡 {error.suggestions[0]}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {validationResult.warnings.length > 0 && (
        <div>
          <h4 className="font-semibold text-warning mb-2">Warnings ({validationResult.warnings.length})</h4>
          <div className="space-y-2">
            {validationResult.warnings.map((warning, index) => (
              <div key={index} className="p-2 bg-warning/10 border border-warning/20 rounded">
                <div className="font-medium text-warning">{warning.field}</div>
                <div className="text-base-content/80">{warning.message}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {validationResult.info.length > 0 && (
        <div>
          <h4 className="font-semibold text-info mb-2">Info ({validationResult.info.length})</h4>
          <div className="space-y-2">
            {validationResult.info.map((info, index) => (
              <div key={index} className="p-2 bg-info/10 border border-info/20 rounded">
                <div className="font-medium text-info">{info.field}</div>
                <div className="text-base-content/80">{info.message}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const StructureTab: React.FC<{ layout: DashboardLayout }> = ({ layout }) => {
  return (
    <div className="space-y-3">
      <div>
        <h4 className="font-semibold mb-2">Grid Configuration</h4>
        <div className="bg-base-200 p-2 rounded font-mono">
          <div>Mode: {layout.grid.mode}</div>
          <div>Gap: {layout.grid.gap}px</div>
          <div>Breakpoints:</div>
          {Object.entries(layout.grid.breakpoints).map(([key, bp]) => (
            <div key={key} className="ml-2">
              {key}: {bp.columns} cols @ {bp.minWidth}px (row: {bp.rowUnit}px)
            </div>
          ))}
        </div>
      </div>

      <div>
        <h4 className="font-semibold mb-2">Summary</h4>
        <div className="bg-base-200 p-2 rounded">
          <div>Widgets: {layout.widgets.length}</div>
          <div>Rows: {layout.rows.length}</div>
          <div>Total Items: {layout.rows.reduce((sum, row) => sum + row.items.length, 0)}</div>
        </div>
      </div>
    </div>
  );
};

const WidgetsTab: React.FC<{ widgets: DashboardLayout['widgets'] }> = ({ widgets }) => {
  return (
    <div className="space-y-2">
      {widgets.map((widget, index) => (
        <div key={widget.id} className="p-2 bg-base-200 rounded">
          <div className="font-medium">{widget.id}</div>
          <div className="text-base-content/70">
            Type: {widget.type} | Title: {widget.title}
          </div>
          <div className="text-base-content/60">
            Data: {widget.dataSource.componentId ? `Component ${widget.dataSource.componentId}` : 
                   widget.dataSource.apiUrl ? 'API URL' : 'None'}
          </div>
          {widget.render && Object.keys(widget.render).length > 0 && (
            <div className="text-base-content/60 font-mono text-xs">
              Render: {JSON.stringify(widget.render)}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

const RowsTab: React.FC<{ rows: DashboardLayout['rows'] }> = ({ rows }) => {
  return (
    <div className="space-y-2">
      {rows.map((row, index) => (
        <div key={row.id} className="p-2 bg-base-200 rounded">
          <div className="font-medium">{row.id}</div>
          <div className="text-base-content/70">
            Height: {row.rowHeight} | Items: {row.items.length}
          </div>
          <div className="ml-2 mt-1 space-y-1">
            {row.items.map((item, itemIndex) => (
              <div key={itemIndex} className="text-xs text-base-content/60 font-mono">
                {item.widgetRef}: col {item.col}, span {item.span}, h {item.h}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default LayoutDebugPanel;