import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import PermissionGuard from './PermissionGuard';
import RoleBadge from './ui/RoleBadge';
import { 
  Role, 
  getRoleDisplayName, 
  getPermissionDisplayName, 
  getRolePermissions,
  isSuperAdmin
} from '../utils/permissions';
import { apiClient, Page, PageOrganizationAccess, OrganizationPageAccess } from '../services/api';
import './PermissionManagement.css';

interface UserInfo {
  id: string;
  email: string;
  role: Role;
  organization?: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
}

interface OrganizationInfo {
  id: number;
  name: string;
  description?: string;
  user_count?: number;
  created_at: string;
}

const PermissionManagement: React.FC = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [organizations, setOrganizations] = useState<OrganizationInfo[]>([]);
  const [pages, setPages] = useState<Page[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserInfo | null>(null);
  const [activeTab, setActiveTab] = useState<'users' | 'organizations' | 'permissions' | 'page-access'>('users');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (isSuperAdmin(user)) {
        // Super admin can see all users, organizations, and pages
        const [usersResponse, orgsResponse, pagesResponse] = await Promise.all([
          apiClient.request<UserInfo[]>({ method: 'GET', url: '/admin/users/' }),
          apiClient.getAllOrganizations(),
          apiClient.getAllPages()
        ]);
        setUsers(usersResponse);
        setOrganizations(orgsResponse);
        setPages(pagesResponse);
      } else {
        // Org admin can only see users in their organization
        const usersResponse = await apiClient.request<UserInfo[]>({ 
          method: 'GET', 
          url: '/admin/organization-users/' 
        });
        setUsers(usersResponse);
      }
    } catch (err: any) {
      setError(err.message || '데이터를 불러오는 중 오류가 발생했습니다.');
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async (userId: string, newRole: Role) => {
    try {
      await apiClient.request({
        method: 'PATCH',
        url: `/admin/users/${userId}/`,
        data: { role: newRole }
      });

      setUsers(users.map(u => u.id === userId ? { ...u, role: newRole } : u));
      
      if (selectedUser?.id === userId) {
        setSelectedUser({ ...selectedUser, role: newRole });
      }
    } catch (err: any) {
      setError(err.message || '권한 변경 중 오류가 발생했습니다.');
    }
  };

  const handleUserActivation = async (userId: string, isActive: boolean) => {
    try {
      await apiClient.request({
        method: 'PATCH',
        url: `/admin/users/${userId}/`,
        data: { is_active: isActive }
      });

      setUsers(users.map(u => u.id === userId ? { ...u, is_active: isActive } : u));
      
      if (selectedUser?.id === userId) {
        setSelectedUser({ ...selectedUser, is_active: isActive });
      }
    } catch (err: any) {
      setError(err.message || '사용자 상태 변경 중 오류가 발생했습니다.');
    }
  };

  if (loading) {
    return (
      <div className="permission-management">
        <div className="loading-spinner">
          <div className="spinner" />
          <p>권한 정보를 불러오는 중...</p>
        </div>
      </div>
    );
  }

  return (
    <PermissionGuard permission="access_admin_panel" showError>
      <div className="permission-management">
        <header className="pm-header">
          <h1>권한 관리</h1>
          <p>사용자 권한 및 조직 관리를 위한 관리자 인터페이스입니다.</p>
        </header>

        {error && (
          <div className="error-banner">
            <strong>오류:</strong> {error}
            <button 
              className="error-dismiss"
              onClick={() => setError(null)}
            >
              ×
            </button>
          </div>
        )}

        <nav className="pm-nav">
          <button 
            className={`nav-tab ${activeTab === 'users' ? 'active' : ''}`}
            onClick={() => setActiveTab('users')}
          >
            사용자 관리 ({users.length})
          </button>
          
          <PermissionGuard permission="manage_system">
            <button 
              className={`nav-tab ${activeTab === 'organizations' ? 'active' : ''}`}
              onClick={() => setActiveTab('organizations')}
            >
              조직 관리 ({organizations.length})
            </button>
          </PermissionGuard>
          
          <PermissionGuard permission="manage_system">
            <button 
              className={`nav-tab ${activeTab === 'page-access' ? 'active' : ''}`}
              onClick={() => setActiveTab('page-access')}
            >
              페이지 접근 권한
            </button>
          </PermissionGuard>
          
          <button 
            className={`nav-tab ${activeTab === 'permissions' ? 'active' : ''}`}
            onClick={() => setActiveTab('permissions')}
          >
            권한 개요
          </button>
        </nav>

        <main className="pm-content">
          {activeTab === 'users' && (
            <UsersTab 
              users={users}
              selectedUser={selectedUser}
              onUserSelect={setSelectedUser}
              onRoleChange={handleRoleChange}
              onUserActivation={handleUserActivation}
              currentUser={user}
            />
          )}

          {activeTab === 'organizations' && (
            <PermissionGuard permission="manage_system">
              <OrganizationsTab organizations={organizations} />
            </PermissionGuard>
          )}

          {activeTab === 'page-access' && (
            <PermissionGuard permission="manage_system">
              <PageAccessTab 
                pages={pages} 
                organizations={organizations} 
                onError={setError}
              />
            </PermissionGuard>
          )}

          {activeTab === 'permissions' && (
            <PermissionsTab />
          )}
        </main>
      </div>
    </PermissionGuard>
  );
};

// Users Tab Component
interface UsersTabProps {
  users: UserInfo[];
  selectedUser: UserInfo | null;
  onUserSelect: (user: UserInfo | null) => void;
  onRoleChange: (userId: string, newRole: Role) => void;
  onUserActivation: (userId: string, isActive: boolean) => void;
  currentUser: any;
}

const UsersTab: React.FC<UsersTabProps> = ({ 
  users, 
  selectedUser, 
  onUserSelect, 
  onRoleChange, 
  onUserActivation,
  currentUser 
}) => {
  return (
    <div className="users-tab">
      <div className="users-list">
        <h3>사용자 목록</h3>
        <div className="user-cards">
          {users.map(user => (
            <div 
              key={user.id}
              className={`user-card ${selectedUser?.id === user.id ? 'selected' : ''} ${!user.is_active ? 'inactive' : ''}`}
              onClick={() => onUserSelect(user)}
            >
              <div className="user-info">
                <div className="user-email">{user.email}</div>
                <div className="user-meta">
                  <RoleBadge role={user.role} size="small" />
                  {!user.is_active && (
                    <span className="inactive-badge">비활성</span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedUser && (
        <div className="user-details">
          <h3>사용자 세부 정보</h3>
          <UserDetailsPanel 
            user={selectedUser}
            onRoleChange={onRoleChange}
            onUserActivation={onUserActivation}
            canEdit={isSuperAdmin(currentUser) || selectedUser.id !== currentUser?.id}
          />
        </div>
      )}
    </div>
  );
};

// User Details Panel
interface UserDetailsPanelProps {
  user: UserInfo;
  onRoleChange: (userId: string, newRole: Role) => void;
  onUserActivation: (userId: string, isActive: boolean) => void;
  canEdit: boolean;
}

const UserDetailsPanel: React.FC<UserDetailsPanelProps> = ({
  user,
  onRoleChange,
  onUserActivation,
  canEdit
}) => {
  const permissions = getRolePermissions(user.role);

  return (
    <div className="user-details-panel">
      <div className="detail-section">
        <h4>기본 정보</h4>
        <div className="detail-item">
          <label>이메일:</label>
          <span>{user.email}</span>
        </div>
        <div className="detail-item">
          <label>가입일:</label>
          <span>{new Date(user.created_at).toLocaleDateString()}</span>
        </div>
        {user.last_login && (
          <div className="detail-item">
            <label>최근 로그인:</label>
            <span>{new Date(user.last_login).toLocaleString()}</span>
          </div>
        )}
      </div>

      <div className="detail-section">
        <h4>권한 설정</h4>
        <div className="detail-item">
          <label>현재 권한:</label>
          <div className="role-control">
            <RoleBadge role={user.role} />
            {canEdit && (
              <PermissionGuard permission="manage_users">
                <select
                  value={user.role}
                  onChange={(e) => onRoleChange(user.id, e.target.value as Role)}
                  className="role-select"
                >
                  <option value="regular">일반 사용자</option>
                  <option value="org_admin">조직 관리자</option>
                  <option value="super_admin">운영 관리자</option>
                </select>
              </PermissionGuard>
            )}
          </div>
        </div>

        <div className="detail-item">
          <label>계정 상태:</label>
          <div className="status-control">
            <span className={`status-indicator ${user.is_active ? 'active' : 'inactive'}`}>
              {user.is_active ? '활성' : '비활성'}
            </span>
            {canEdit && (
              <PermissionGuard permission="manage_users">
                <button
                  onClick={() => onUserActivation(user.id, !user.is_active)}
                  className={`status-toggle ${user.is_active ? 'deactivate' : 'activate'}`}
                >
                  {user.is_active ? '비활성화' : '활성화'}
                </button>
              </PermissionGuard>
            )}
          </div>
        </div>
      </div>

      <div className="detail-section">
        <h4>보유 권한 ({permissions.length}개)</h4>
        <div className="permissions-list">
          {permissions.map(permission => (
            <div key={permission} className="permission-item">
              {getPermissionDisplayName(permission)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Organizations Tab Component
const OrganizationsTab: React.FC<{ organizations: OrganizationInfo[] }> = ({ organizations }) => {
  return (
    <div className="organizations-tab">
      <h3>조직 관리</h3>
      <div className="org-cards">
        {organizations.map(org => (
          <div key={org.id} className="org-card">
            <h4>{org.name}</h4>
            {org.description && <p>{org.description}</p>}
            <div className="org-stats">
              <span>사용자: {org.user_count}명</span>
              <span>생성일: {new Date(org.created_at).toLocaleDateString()}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Page Access Tab Component
interface PageAccessTabProps {
  pages: Page[];
  organizations: OrganizationInfo[];
  onError: (error: string) => void;
}

const PageAccessTab: React.FC<PageAccessTabProps> = ({ pages, organizations, onError }) => {
  const [selectedPage, setSelectedPage] = useState<Page | null>(null);
  const [selectedOrganization, setSelectedOrganization] = useState<OrganizationInfo | null>(null);
  const [pageAccess, setPageAccess] = useState<PageOrganizationAccess[]>([]);
  const [orgAccess, setOrgAccess] = useState<OrganizationPageAccess[]>([]);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'by-page' | 'by-organization'>('by-page');

  const loadPageAccess = async (page: Page) => {
    try {
      setLoading(true);
      const response = await apiClient.getPageAccessibleOrganizations(Number(page.id));
      setPageAccess(response.organizations);
    } catch (err: any) {
      onError(err.message || '페이지 접근 권한을 불러오는 중 오류가 발생했습니다.');
    } finally {
      setLoading(false);
    }
  };

  const loadOrganizationAccess = async (org: OrganizationInfo) => {
    try {
      setLoading(true);
      const response = await apiClient.getOrganizationAccessiblePages(Number(org.id));
      setOrgAccess(response.pages);
    } catch (err: any) {
      onError(err.message || '조직 접근 권한을 불러오는 중 오류가 발생했습니다.');
    } finally {
      setLoading(false);
    }
  };

  const handleGrantAccess = async (pageId: number, orgId: number) => {
    try {
      const response = await apiClient.grantPageAccess(pageId, orgId);
      onError(''); // Clear any previous errors
      
      // Refresh the current view
      if (viewMode === 'by-page' && selectedPage) {
        await loadPageAccess(selectedPage);
      } else if (viewMode === 'by-organization' && selectedOrganization) {
        await loadOrganizationAccess(selectedOrganization);
      }
      
      // Show success message briefly
      setTimeout(() => onError(''), 3000);
      onError(`✅ ${response.message}`);
    } catch (err: any) {
      onError(err.message || '권한 부여 중 오류가 발생했습니다.');
    }
  };

  const handleRevokeAccess = async (pageId: number, orgId: number) => {
    try {
      const response = await apiClient.revokePageAccess(pageId, orgId);
      onError(''); // Clear any previous errors
      
      // Refresh the current view
      if (viewMode === 'by-page' && selectedPage) {
        await loadPageAccess(selectedPage);
      } else if (viewMode === 'by-organization' && selectedOrganization) {
        await loadOrganizationAccess(selectedOrganization);
      }
      
      // Show success message briefly
      setTimeout(() => onError(''), 3000);
      onError(`✅ ${response.message}`);
    } catch (err: any) {
      onError(err.message || '권한 해제 중 오류가 발생했습니다.');
    }
  };

  const handlePageSelect = (page: Page) => {
    setSelectedPage(page);
    setSelectedOrganization(null);
    loadPageAccess(page);
  };

  const handleOrganizationSelect = (org: OrganizationInfo) => {
    setSelectedOrganization(org);
    setSelectedPage(null);
    loadOrganizationAccess(org);
  };

  return (
    <div className="page-access-tab">
      <div className="page-access-header">
        <h3>페이지 접근 권한 관리</h3>
        <div className="view-mode-toggle">
          <button 
            className={`toggle-btn ${viewMode === 'by-page' ? 'active' : ''}`}
            onClick={() => setViewMode('by-page')}
          >
            페이지별 보기
          </button>
          <button 
            className={`toggle-btn ${viewMode === 'by-organization' ? 'active' : ''}`}
            onClick={() => setViewMode('by-organization')}
          >
            조직별 보기
          </button>
        </div>
      </div>

      <div className="page-access-content">
        {viewMode === 'by-page' ? (
          <PageByPageView 
            pages={pages}
            organizations={organizations}
            selectedPage={selectedPage}
            pageAccess={pageAccess}
            loading={loading}
            onPageSelect={handlePageSelect}
            onGrantAccess={handleGrantAccess}
            onRevokeAccess={handleRevokeAccess}
          />
        ) : (
          <PageByOrganizationView 
            pages={pages}
            organizations={organizations}
            selectedOrganization={selectedOrganization}
            orgAccess={orgAccess}
            loading={loading}
            onOrganizationSelect={handleOrganizationSelect}
            onGrantAccess={handleGrantAccess}
            onRevokeAccess={handleRevokeAccess}
          />
        )}
      </div>
    </div>
  );
};

// Page-by-page view component
interface PageByPageViewProps {
  pages: Page[];
  organizations: OrganizationInfo[];
  selectedPage: Page | null;
  pageAccess: PageOrganizationAccess[];
  loading: boolean;
  onPageSelect: (page: Page) => void;
  onGrantAccess: (pageId: number, orgId: number) => void;
  onRevokeAccess: (pageId: number, orgId: number) => void;
}

const PageByPageView: React.FC<PageByPageViewProps> = ({
  pages,
  organizations,
  selectedPage,
  pageAccess,
  loading,
  onPageSelect,
  onGrantAccess,
  onRevokeAccess
}) => {
  const accessibleOrgIds = new Set(pageAccess.map(access => access.organization_id));

  return (
    <div className="page-by-page-view">
      <div className="pages-list">
        <h4>페이지 목록 ({pages.length})</h4>
        <div className="list-container">
          {pages.map(page => (
            <div 
              key={page.id}
              className={`list-item ${selectedPage?.id === page.id ? 'selected' : ''}`}
              onClick={() => onPageSelect(page)}
            >
              <div className="item-name">{page.name}</div>
              <div className="item-meta">
                <span className="permission-level">{page.permission_level}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="access-details">
        {selectedPage ? (
          <>
            <h4>"{selectedPage.name}" 페이지 접근 권한</h4>
            {loading ? (
              <div className="loading-spinner">
                <div className="spinner" />
                <p>권한 정보를 불러오는 중...</p>
              </div>
            ) : (
              <div className="organizations-access">
                {organizations.map(org => {
                  const hasAccess = accessibleOrgIds.has(Number(org.id));
                  const accessInfo = pageAccess.find(access => access.organization_id === Number(org.id));
                  
                  return (
                    <div key={org.id} className={`org-access-item ${hasAccess ? 'has-access' : 'no-access'}`}>
                      <div className="org-info">
                        <div className="org-name">{org.name}</div>
                        {hasAccess && accessInfo && (
                          <div className="access-meta">
                            <span>부여일: {new Date(accessInfo.granted_at).toLocaleDateString()}</span>
                            {accessInfo.granted_by && (
                              <span>부여자: {accessInfo.granted_by}</span>
                            )}
                          </div>
                        )}
                      </div>
                      <div className="access-controls">
                        {hasAccess ? (
                          <button 
                            className="revoke-btn"
                            onClick={() => onRevokeAccess(Number(selectedPage.id), Number(org.id))}
                          >
                            권한 해제
                          </button>
                        ) : (
                          <button 
                            className="grant-btn"
                            onClick={() => onGrantAccess(Number(selectedPage.id), Number(org.id))}
                          >
                            권한 부여
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        ) : (
          <div className="no-selection">
            <p>페이지를 선택하여 접근 권한을 관리하세요.</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Organization-by-organization view component
interface PageByOrganizationViewProps {
  pages: Page[];
  organizations: OrganizationInfo[];
  selectedOrganization: OrganizationInfo | null;
  orgAccess: OrganizationPageAccess[];
  loading: boolean;
  onOrganizationSelect: (org: OrganizationInfo) => void;
  onGrantAccess: (pageId: number, orgId: number) => void;
  onRevokeAccess: (pageId: number, orgId: number) => void;
}

const PageByOrganizationView: React.FC<PageByOrganizationViewProps> = ({
  pages,
  organizations,
  selectedOrganization,
  orgAccess,
  loading,
  onOrganizationSelect,
  onGrantAccess,
  onRevokeAccess
}) => {
  const accessiblePageIds = new Set(orgAccess.map(access => access.page_id));

  return (
    <div className="page-by-organization-view">
      <div className="organizations-list">
        <h4>조직 목록 ({organizations.length})</h4>
        <div className="list-container">
          {organizations.map(org => (
            <div 
              key={org.id}
              className={`list-item ${selectedOrganization?.id === org.id ? 'selected' : ''}`}
              onClick={() => onOrganizationSelect(org)}
            >
              <div className="item-name">{org.name}</div>
              <div className="item-meta">
                <span>사용자: {org.user_count || 0}명</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="access-details">
        {selectedOrganization ? (
          <>
            <h4>"{selectedOrganization.name}" 조직 페이지 접근 권한</h4>
            {loading ? (
              <div className="loading-spinner">
                <div className="spinner" />
                <p>권한 정보를 불러오는 중...</p>
              </div>
            ) : (
              <div className="pages-access">
                {pages.map(page => {
                  const hasAccess = accessiblePageIds.has(Number(page.id));
                  const accessInfo = orgAccess.find(access => access.page_id === Number(page.id));
                  
                  return (
                    <div key={page.id} className={`page-access-item ${hasAccess ? 'has-access' : 'no-access'}`}>
                      <div className="page-info">
                        <div className="page-name">{page.name}</div>
                        <div className="page-meta">
                          <span className="permission-level">{page.permission_level}</span>
                          {hasAccess && accessInfo && (
                            <>
                              <span>부여일: {new Date(accessInfo.granted_at).toLocaleDateString()}</span>
                              {accessInfo.granted_by && (
                                <span>부여자: {accessInfo.granted_by}</span>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                      <div className="access-controls">
                        {hasAccess ? (
                          <button 
                            className="revoke-btn"
                            onClick={() => onRevokeAccess(Number(page.id), Number(selectedOrganization.id))}
                          >
                            권한 해제
                          </button>
                        ) : (
                          <button 
                            className="grant-btn"
                            onClick={() => onGrantAccess(Number(page.id), Number(selectedOrganization.id))}
                          >
                            권한 부여
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        ) : (
          <div className="no-selection">
            <p>조직을 선택하여 페이지 접근 권한을 관리하세요.</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Permissions Tab Component
const PermissionsTab: React.FC = () => {
  const roles: Role[] = ['regular', 'org_admin', 'super_admin'];

  return (
    <div className="permissions-tab">
      <h3>권한 시스템 개요</h3>
      <div className="permissions-overview">
        {roles.map(role => (
          <div key={role} className="role-section">
            <h4>
              <RoleBadge role={role} /> {getRoleDisplayName(role)}
            </h4>
            <div className="role-permissions">
              {getRolePermissions(role).map(permission => (
                <div key={permission} className="permission-item">
                  <span className="permission-name">
                    {getPermissionDisplayName(permission)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PermissionManagement;