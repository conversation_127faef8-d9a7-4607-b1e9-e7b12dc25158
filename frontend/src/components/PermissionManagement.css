.permission-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.pm-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.pm-header h1 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
}

.pm-header p {
  margin: 0;
  color: #4a5568;
  font-size: 14px;
}

/* Loading */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #4a5568;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Banner */
.error-banner {
  background-color: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.error-dismiss {
  background: none;
  border: none;
  color: #c53030;
  font-size: 18px;
  cursor: pointer;
  padding: 0 4px;
}

/* Navigation */
.pm-nav {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 24px;
  overflow-x: auto;
}

.nav-tab {
  background: none;
  border: none;
  padding: 12px 16px;
  color: #4a5568;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  transition: all 0.2s;
}

.nav-tab:hover {
  color: #2d3748;
  background-color: #f7fafc;
}

.nav-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

/* Main Content */
.pm-content {
  min-height: 400px;
}

/* Users Tab */
.users-tab {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.users-list h3 {
  margin: 0 0 16px 0;
  color: #2d3748;
}

.user-cards {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 500px;
  overflow-y: auto;
}

.user-card {
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.user-card:hover {
  border-color: #cbd5e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.user-card.selected {
  border-color: #667eea;
  background-color: #f7fafc;
}

.user-card.inactive {
  opacity: 0.6;
  background-color: #f8f9fa;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-email {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.inactive-badge {
  background-color: #f56565;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

/* User Details */
.user-details h3 {
  margin: 0 0 16px 0;
  color: #2d3748;
}

.user-details-panel {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.detail-section {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.detail-section:last-child {
  border-bottom: none;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  font-weight: 500;
  color: #4a5568;
  font-size: 13px;
}

.role-control, .status-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.role-select {
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 12px;
  background: white;
}

.status-indicator {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.active {
  background-color: #c6f6d5;
  color: #22543d;
}

.status-indicator.inactive {
  background-color: #fed7d7;
  color: #c53030;
}

.status-toggle {
  padding: 4px 8px;
  border: 1px solid;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.status-toggle.activate {
  background-color: #c6f6d5;
  border-color: #38a169;
  color: #22543d;
}

.status-toggle.deactivate {
  background-color: #fed7d7;
  border-color: #f56565;
  color: #c53030;
}

.permissions-list {
  display: grid;
  gap: 4px;
}

.permission-item {
  background-color: #f7fafc;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #4a5568;
  border-left: 3px solid #667eea;
}

/* Organizations Tab */
.organizations-tab h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
}

.org-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.org-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.org-card h4 {
  margin: 0 0 8px 0;
  color: #2d3748;
}

.org-card p {
  margin: 0 0 12px 0;
  color: #4a5568;
  font-size: 14px;
}

.org-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #718096;
}

/* Permissions Tab */
.permissions-tab h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
}

.permissions-overview {
  display: grid;
  gap: 20px;
}

.role-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.role-section h4 {
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2d3748;
}

.role-permissions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.role-permissions .permission-item {
  margin: 0;
}

.permission-name {
  font-weight: 500;
}

/* Page Access Tab */
.page-access-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.page-access-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-access-header h3 {
  margin: 0;
  color: #2d3748;
}

.view-mode-toggle {
  display: flex;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.toggle-btn {
  background: white;
  border: none;
  padding: 8px 16px;
  color: #4a5568;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.toggle-btn:hover {
  background-color: #f7fafc;
}

.toggle-btn.active {
  background-color: #667eea;
  color: white;
}

.page-access-content {
  flex: 1;
}

/* Page by Page View */
.page-by-page-view,
.page-by-organization-view {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
  min-height: 500px;
}

.pages-list,
.organizations-list {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.pages-list h4,
.organizations-list h4 {
  margin: 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e2e8f0;
  color: #2d3748;
  font-size: 14px;
  font-weight: 600;
}

.list-container {
  max-height: 400px;
  overflow-y: auto;
}

.list-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.2s;
}

.list-item:hover {
  background-color: #f7fafc;
}

.list-item.selected {
  background-color: #edf2f7;
  border-left: 3px solid #667eea;
}

.list-item:last-child {
  border-bottom: none;
}

.item-name {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
  margin-bottom: 4px;
}

.item-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #718096;
}

.permission-level {
  background-color: #e2e8f0;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

/* Access Details */
.access-details {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.access-details h4 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 16px;
  font-weight: 600;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #718096;
  font-style: italic;
}

/* Organization Access Items */
.organizations-access,
.pages-access {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.org-access-item,
.page-access-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s;
}

.org-access-item.has-access,
.page-access-item.has-access {
  background-color: #f0fff4;
  border-color: #9ae6b4;
}

.org-access-item.no-access,
.page-access-item.no-access {
  background-color: #fffaf0;
  border-color: #fbd38d;
}

.org-info,
.page-info {
  flex: 1;
}

.org-name,
.page-name {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
  margin-bottom: 4px;
}

.access-meta,
.page-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
  color: #718096;
}

.access-controls {
  display: flex;
  gap: 8px;
}

.grant-btn,
.revoke-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.grant-btn {
  background-color: #48bb78;
  color: white;
}

.grant-btn:hover {
  background-color: #38a169;
}

.revoke-btn {
  background-color: #f56565;
  color: white;
}

.revoke-btn:hover {
  background-color: #e53e3e;
}

/* Success message styling */
.error-banner:has-text("✅") {
  background-color: #c6f6d5;
  border-color: #9ae6b4;
  color: #22543d;
}

/* Responsive */
@media (max-width: 768px) {
  .permission-management {
    padding: 12px;
  }
  
  .users-tab {
    grid-template-columns: 1fr;
  }
  
  .page-by-page-view,
  .page-by-organization-view {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .page-access-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .view-mode-toggle {
    align-self: stretch;
  }
  
  .toggle-btn {
    flex: 1;
  }
  
  .pm-nav {
    flex-direction: column;
    border-bottom: none;
  }
  
  .nav-tab {
    border-bottom: 1px solid #e2e8f0;
    border-right: none;
  }
  
  .nav-tab.active {
    border-bottom-color: #e2e8f0;
    background-color: #667eea;
    color: white;
  }
  
  .org-cards {
    grid-template-columns: 1fr;
  }
  
  .role-permissions {
    grid-template-columns: 1fr;
  }
  
  .org-access-item,
  .page-access-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .access-controls {
    justify-content: center;
  }
}