import React from 'react';

interface LayoutErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  enableRecovery?: boolean;
  recoveryDelay?: number;
}

interface LayoutErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
  retryCount: number;
}

export class LayoutErrorBoundary extends React.Component<
  LayoutErrorBoundaryProps,
  LayoutErrorBoundaryState
> {
  private retryTimeoutId?: NodeJS.Timeout;
  private maxRetries = 3;

  state: LayoutErrorBoundaryState = { 
    hasError: false, 
    retryCount: 0 
  };

  static getDerivedStateFromError(error: Error): Partial<LayoutErrorBoundaryState> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Layout rendering error:', error, errorInfo);
    
    // Store error info for debugging
    this.setState({ errorInfo });
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log detailed error information for debugging
    this.logLayoutError(error, errorInfo);
    
    // Auto-recovery mechanism
    if (this.props.enableRecovery !== false && this.state.retryCount < this.maxRetries) {
      const delay = this.props.recoveryDelay || 3000;
      this.retryTimeoutId = setTimeout(() => {
        this.handleRetry();
      }, delay);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  private logLayoutError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Enhanced error logging for layout issues
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      retryCount: this.state.retryCount,
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Layout Error Details');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.table(errorDetails);
      console.groupEnd();
    }

    // In production, you might want to send this to an error tracking service
    // Example: errorTrackingService.captureException(error, errorDetails);
  };

  private handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: prevState.retryCount + 1,
    }));
  };

  private handleManualRetry = () => {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
    this.handleRetry();
  };

  private getErrorType = (error?: Error): string => {
    if (!error) return 'Unknown';
    
    const message = error.message.toLowerCase();
    
    if (message.includes('layout') || message.includes('grid')) {
      return 'Layout Configuration';
    }
    if (message.includes('widget') || message.includes('component')) {
      return 'Widget Reference';
    }
    if (message.includes('render') || message.includes('display')) {
      return 'Rendering';
    }
    if (message.includes('validation') || message.includes('schema')) {
      return 'Schema Validation';
    }
    
    return 'Component';
  };

  private getErrorSuggestions = (error?: Error): string[] => {
    if (!error) return [];
    
    const message = error.message.toLowerCase();
    const suggestions: string[] = [];
    
    if (message.includes('widget') && message.includes('not found')) {
      suggestions.push('위젯 ID가 올바른지 확인하세요');
      suggestions.push('위젯이 삭제되었거나 이름이 변경되었을 수 있습니다');
    }
    
    if (message.includes('layout') || message.includes('grid')) {
      suggestions.push('레이아웃 설정을 확인하세요');
      suggestions.push('그리드 구성이 올바른지 검토하세요');
    }
    
    if (message.includes('render') || message.includes('config')) {
      suggestions.push('렌더링 설정을 확인하세요');
      suggestions.push('컴포넌트 크기 제약을 검토하세요');
    }
    
    if (suggestions.length === 0) {
      suggestions.push('페이지를 새로고침해 보세요');
      suggestions.push('브라우저 콘솔에서 자세한 오류를 확인하세요');
    }
    
    return suggestions;
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const errorType = this.getErrorType(this.state.error);
      const suggestions = this.getErrorSuggestions(this.state.error);
      const canRetry = this.state.retryCount < this.maxRetries;
      const isAutoRetrying = this.props.enableRecovery !== false && canRetry;

      return (
        <div className="layout-error-boundary bg-error/10 border border-error/20 rounded-lg p-6 m-4">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-error/20 rounded-full flex items-center justify-center">
                <span className="text-error text-xl">⚠️</span>
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-error mb-2">
                레이아웃 렌더링 오류
              </h3>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-base-content/80 mb-1">
                    <strong>오류 유형:</strong> {errorType}
                  </p>
                  <p className="text-sm text-base-content/70">
                    {this.state.error?.message || '레이아웃을 렌더링하는 중 오류가 발생했습니다.'}
                  </p>
                </div>

                {suggestions.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-base-content/80 mb-2">해결 방법:</p>
                    <ul className="text-sm text-base-content/70 space-y-1">
                      {suggestions.map((suggestion, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-primary mt-0.5">•</span>
                          <span>{suggestion}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="flex items-center gap-3 pt-2">
                  {canRetry && (
                    <button
                      onClick={this.handleManualRetry}
                      className="btn btn-sm btn-primary"
                      disabled={isAutoRetrying}
                    >
                      다시 시도
                    </button>
                  )}
                  
                  {isAutoRetrying && (
                    <div className="flex items-center gap-2 text-sm text-base-content/60">
                      <span className="loading loading-spinner loading-xs"></span>
                      <span>자동으로 다시 시도 중... ({this.maxRetries - this.state.retryCount}회 남음)</span>
                    </div>
                  )}
                  
                  {!canRetry && (
                    <p className="text-sm text-base-content/60">
                      최대 재시도 횟수에 도달했습니다. 페이지를 새로고침하세요.
                    </p>
                  )}
                </div>

                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <details className="mt-4">
                    <summary className="text-sm font-medium text-base-content/80 cursor-pointer hover:text-base-content">
                      개발자 정보 (클릭하여 펼치기)
                    </summary>
                    <div className="mt-2 p-3 bg-base-200 rounded text-xs font-mono overflow-auto max-h-40">
                      <div className="mb-2">
                        <strong>Error Stack:</strong>
                        <pre className="whitespace-pre-wrap text-xs mt-1">{this.state.error.stack}</pre>
                      </div>
                      {this.state.errorInfo && (
                        <div>
                          <strong>Component Stack:</strong>
                          <pre className="whitespace-pre-wrap text-xs mt-1">{this.state.errorInfo.componentStack}</pre>
                        </div>
                      )}
                    </div>
                  </details>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default LayoutErrorBoundary;