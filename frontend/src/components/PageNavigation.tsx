import React, { useRef, useEffect } from 'react';
import './PageNavigation.css';

interface Page {
  id: string;
  name: string;
  permission_level: string;
}

interface PageNavigationProps {
  pages: Page[];
  activePage: string;
  onPageSelect: (pageId: string) => void;
  isRefreshing: boolean;
  onRefresh: (pageId: string) => void;
}

const PageNavigation: React.FC<PageNavigationProps> = ({
  pages,
  activePage,
  onPageSelect,
  isRefreshing,
  onRefresh,
}) => {
  const tabsRef = useRef<HTMLDivElement>(null);
  const activeTabRef = useRef<HTMLButtonElement>(null);

  // Scroll active tab into view when it changes
  useEffect(() => {
    if (activeTabRef.current && tabsRef.current) {
      const activeTab = activeTabRef.current;
      const tabsContainer = tabsRef.current;
      
      const tabRect = activeTab.getBoundingClientRect();
      const containerRect = tabsContainer.getBoundingClientRect();
      
      // Check if tab is fully visible
      const isTabVisible = 
        tabRect.left >= containerRect.left && 
        tabRect.right <= containerRect.right;
      
      if (!isTabVisible) {
        // Calculate scroll position to center the active tab
        const scrollLeft = activeTab.offsetLeft - 
          (tabsContainer.clientWidth / 2) + 
          (activeTab.clientWidth / 2);
        
        tabsContainer.scrollTo?.({
          left: scrollLeft,
          behavior: 'smooth'
        });
      }
    }
  }, [activePage]);

  const handleTabClick = (pageId: string) => {
    if (pageId !== activePage) {
      onPageSelect(pageId);
    }
  };

  const handleRefreshClick = () => {
    if (activePage && !isRefreshing) {
      onRefresh(activePage);
    }
  };

  const getActivePageName = () => {
    const page = pages.find(p => p.id === activePage);
    return page ? page.name : '';
  };

  if (pages.length === 0) {
    return (
      <nav className="page-navigation">
        <div className="nav-content">
          <div className="no-pages-message">
            <span>접근 가능한 페이지가 없습니다.</span>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav className="page-navigation">
      <div className="nav-content">
        <div className="nav-left">
          <div className="page-tabs" ref={tabsRef} role="tablist">
            {pages.map((page) => (
              <button
                key={page.id}
                ref={page.id === activePage ? activeTabRef : null}
                className={`btn btn-sm page-tab ${activePage === page.id ? 'active' : 'btn-ghost'}`}
                onClick={() => handleTabClick(page.id)}
                disabled={isRefreshing}
                title={`${page.name} 페이지로 이동`}
                role="tab"
                aria-selected={activePage === page.id}
                aria-controls={`panel-${page.id}`}
                tabIndex={activePage === page.id ? 0 : -1}
              >
                <span>{page.name}</span>
              </button>
            ))}
          </div>
          
        </div>
        
        <div className="nav-right">
          
          <button
            className="refresh-button"
            onClick={handleRefreshClick}
            disabled={!activePage || isRefreshing}
            title={`${getActivePageName()} 페이지 새로고침`}
          >
            <span className={`refresh-icon ${isRefreshing ? 'spinning' : ''}`}>🔄</span>
            <span className="refresh-text">새로고침</span>
          </button>
        </div>
      </div>
    </nav>
  );
};

export default PageNavigation;