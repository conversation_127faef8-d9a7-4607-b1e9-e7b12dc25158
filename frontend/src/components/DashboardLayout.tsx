import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import PageNavigation from './PageNavigation';
import PageContent, { PageContentHandle } from './PageContent';
import PermissionGuard from './PermissionGuard';
import RoleBadge from './ui/RoleBadge';
import ThemeSelector from './ThemeSelector';
import { apiClient } from '../services/api';
import { mockPages } from '../mocks/pages';
import { hasRole } from '../utils/permissions';

interface Page {
  id: string;
  name: string;
  permission_level: string;
}

interface PageDetail {
  id: number;
  name: string;
  permission_level: string;
  layout_config: any;
  components: any[];
  created_by: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface DashboardLayoutProps {
  children?: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const { currentTheme, setTheme, initializeFromUser } = useTheme();
  const [pages, setPages] = useState<Page[]>([]);
  const [activePage, setActivePage] = useState<string>('');
  const [currentPageDetail, setCurrentPageDetail] = useState<PageDetail | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isThemeSelectorOpen, setIsThemeSelectorOpen] = useState(false);
  const pageContentRef = useRef<PageContentHandle>(null);

  // Initialize theme from user data when user is loaded
  useEffect(() => {
    if (user) {
      // Call initializeFromUser regardless of whether ui_theme exists
      // This handles both cases: user has theme set, or user has no theme (use frontend default)
      initializeFromUser(user.ui_theme);
    }
  }, [user, initializeFromUser]);

  useEffect(() => {
    const fetchPages = async () => {
      if (!user) return;
      try {
        let fetchedPages: Page[];
        if (process.env.NODE_ENV === 'test') {
          fetchedPages = mockPages;
        } else {
          const data = await apiClient.getAccessiblePages();
          fetchedPages = (data.pages || []).map((p: any) => ({
            id: String(p.id),
            name: p.name,
            permission_level: p.permission_level,
          }));
        }

        const filteredPages = fetchedPages.filter(page => {
          if (hasRole(user, 'super_admin')) return true;
          if (hasRole(user, 'org_admin')) return ['regular', 'org_admin'].includes(page.permission_level);
          if (user.role === 'regular') return page.permission_level === 'regular';
          return false;
        });

        setPages(filteredPages);
        if (filteredPages.length > 0 && !activePage) {
          const firstPageId = filteredPages[0].id;
          setActivePage(firstPageId);
          await fetchPageDetail(firstPageId);
        }
      } catch (error) {
        console.error('Failed to load pages:', error);
      }
    };
    fetchPages();
  }, [user, activePage]);


  const handleLogout = async () => {
    try {
      logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const fetchPageDetail = async (pageId: string) => {
    try {
      const pageDetail = await apiClient.getPageDetail(pageId);
      setCurrentPageDetail(pageDetail);
    } catch (error) {
      console.error('Failed to load page detail:', error);
      setCurrentPageDetail(null);
    }
  };

  const handlePageChange = async (pageId: string) => {
    setActivePage(pageId);
    setIsMobileMenuOpen(false);
    
    // Fetch detailed page information
    if (pageId) {
      await fetchPageDetail(pageId);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await pageContentRef.current?.refresh();
    } catch (error) {
      console.error('Refresh error:', error);
    } finally {
      setIsRefreshing(false);
    }
  };


  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-base-100">
      {/* Header */}
      <header className="navbar bg-base-200 shadow-md">
        <div className="navbar-start">
          <h1 className="text-xl font-bold text-base-content">Dynamic BI Dashboard</h1>
          <div className="ml-4 flex items-center gap-2">
            <span className="text-sm text-base-content/70">{user.email}</span>
            <RoleBadge role={user.role} size="small" variant="subtle" />
          </div>
        </div>
        
        <div className="navbar-end gap-2">
          {/* Theme selector button */}
          <button 
            className="btn btn-primary btn-sm"
            onClick={() => setIsThemeSelectorOpen(true)}
            title="테마 변경"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z" />
            </svg>
            <span className="hidden sm:inline text-xs">{currentTheme}</span>
          </button>
          
          {/* Admin Panel Access Button */}
          <PermissionGuard permission="access_admin_panel">
            <Link to="/admin" className="btn btn-outline btn-sm">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span className="hidden sm:inline">관리자</span>
            </Link>
          </PermissionGuard>

          {/* Mobile menu toggle */}
          <button
            className="btn btn-square btn-ghost lg:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="메뉴 토글"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          
          <button className="btn btn-primary btn-sm" onClick={handleLogout}>
            로그아웃
          </button>
        </div>
      </header>

      {/* Page Navigation */}
      <div className={`bg-base-200 border-b ${isMobileMenuOpen ? 'block' : 'hidden lg:block'}`}>
        <PageNavigation
          pages={pages}
          activePage={activePage}
          onPageSelect={handlePageChange}
          isRefreshing={isRefreshing}
          onRefresh={handleRefresh}
        />
      </div>

      {/* Main Content Area */}
      <main className={`relative ${isRefreshing ? 'pointer-events-none' : ''}`}>
        {/* Loading overlay */}
        {isRefreshing && (
          <div className="absolute inset-0 bg-base-100/80 flex items-center justify-center z-50">
            <div className="flex flex-col items-center gap-4">
              <span className="loading loading-spinner loading-lg text-primary"></span>
              <span className="text-base-content/70">새로고침 중...</span>
            </div>
          </div>
        )}
        
        {(() => {
          // If a page is selected, show PageContent regardless of children
          if (currentPageDetail) {
            return (
              <PageContent
                ref={pageContentRef}
                page={currentPageDetail}
                isRefreshing={isRefreshing}
              />
            );
          }
          // Otherwise, show children (default dashboard content)
          if (children) {
            return children;
          }
          // Fallback
          return (
            <div className="flex flex-col items-center justify-center min-h-[60vh] p-8 text-center">
              <div className="max-w-md">
                <h2 className="text-2xl font-bold mb-4 text-base-content">
                  페이지를 선택해주세요
                </h2>
                <p className="text-base-content/70 mb-6">
                  상단의 페이지 탭을 클릭하여 대시보드를 확인하세요.
                </p>
                <div className="animate-pulse">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
            </div>
          );
        })()}
      </main>

      {/* Theme Selector Modal */}
      <ThemeSelector 
        isOpen={isThemeSelectorOpen} 
        onClose={() => setIsThemeSelectorOpen(false)} 
      />
    </div>
  );
};

export default DashboardLayout;