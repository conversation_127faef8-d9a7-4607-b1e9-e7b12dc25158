import React, {
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
  useCallback,
} from 'react';
import ComponentGrid, { GridItem } from './ComponentGrid';
import DashboardGrid from './DashboardGrid';
import LayoutErrorBoundary from './LayoutErrorBoundary';
import LayoutDebugPanel from './LayoutDebugPanel';
import { ChartType, ChartComponentHandle } from './ChartComponent';
import { apiClient } from '../services/api';
import { buildMockPageContent, getMockChartData } from '../mocks/pageContent';
import { DashboardLayout } from '../types/dashboard';
import { validateLayoutConfig, formatValidationResults } from '../utils/layoutValidation';

interface Page {
  id: string | number;
  name: string;
  permission_level: string;
}

interface PageDetail extends Page {
  layout_config: DashboardLayout | any;
  normalized_layout?: DashboardLayout | any;
  layout_version?: string;
  components: any[];
  created_by: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface PageContentProps {
  page: PageDetail | null;
  isRefreshing: boolean;
}

export interface PageContentHandle {
  refresh: () => Promise<void>;
}

interface PageComponentResponse {
  id: number;
  component_template: {
    name: string;
    chart_type: string;
  };
  grid_position?: { 
    x?: number; 
    y?: number; 
    w?: number; 
    h?: number; 
    width?: number; 
    height?: number; 
  };
}

const PageContent = forwardRef<PageContentHandle, PageContentProps>(
({ page, isRefreshing }, ref) => {
  const [components, setComponents] = useState<GridItem[]>([]);
  const [dashboardLayout, setDashboardLayout] = useState<DashboardLayout | null>(null);
  const [loading, setLoading] = useState(false);
  const [refreshError, setRefreshError] = useState(false);
  const [debugPanelVisible, setDebugPanelVisible] = useState(false);
  const componentRefs = useRef<Record<string, ChartComponentHandle | null>>({});
  
  // API loading queue management
  const [apiLoadingQueue, setApiLoadingQueue] = useState<string[]>([]);
  const [activeApiLoading, setActiveApiLoading] = useState<Set<string>>(new Set());
  const [completedApiLoading, setCompletedApiLoading] = useState<Set<string>>(new Set());
  const [loadingComponents, setLoadingComponents] = useState<Set<string>>(new Set());
  const MAX_CONCURRENT_API_LOADS = 5;

  // API loading queue processing logic
  const processApiQueue = useCallback(() => {
    const currentActiveCount = activeApiLoading.size;
    const availableSlots = MAX_CONCURRENT_API_LOADS - currentActiveCount;
    
    if (availableSlots <= 0 || apiLoadingQueue.length === 0) return;

    const nextComponentIds = apiLoadingQueue.slice(0, availableSlots);
    const newActiveApiLoading = new Set(activeApiLoading);
    
    nextComponentIds.forEach(componentId => {
      newActiveApiLoading.add(componentId);
      setLoadingComponents(prev => {
        const newSet = new Set(prev);
        newSet.add(componentId);
        return newSet;
      });
      
      // Trigger API call for this component after a short delay to ensure ref is set
      setTimeout(() => {
        const componentRef = componentRefs.current[componentId];
        if (componentRef && componentRef.loadData) {
          componentRef.loadData().catch(() => {
            // Handle error by calling completion handler - inline to avoid circular dependency
            setActiveApiLoading(prev => {
              const newSet = new Set(prev);
              newSet.delete(componentId);
              return newSet;
            });
            
            setCompletedApiLoading(prev => {
              const newSet = new Set(prev);
              newSet.add(componentId);
              return newSet;
            });
            
            setLoadingComponents(prev => {
              const newSet = new Set(prev);
              newSet.delete(componentId);
              return newSet;
            });
          });
        }
      }, 100);
    });
    
    setActiveApiLoading(newActiveApiLoading);
    setApiLoadingQueue(prev => prev.slice(availableSlots));
  }, [apiLoadingQueue, activeApiLoading]);

  // API completion handler
  const handleApiLoadCompleted = useCallback((componentId: string) => {
    setActiveApiLoading(prev => {
      const newSet = new Set(prev);
      newSet.delete(componentId);
      return newSet;
    });
    
    setCompletedApiLoading(prev => {
      const newSet = new Set(prev);
      newSet.add(componentId);
      return newSet;
    });
    
    setLoadingComponents(prev => {
      const newSet = new Set(prev);
      newSet.delete(componentId);
      return newSet;
    });
    
    // Process queue after API completion
    setTimeout(() => processApiQueue(), 100);
  }, [processApiQueue]);

  // Effect to process API queue when it changes
  useEffect(() => {
    processApiQueue();
  }, [processApiQueue]);

  // Debug useEffect to track dashboardLayout changes
  useEffect(() => {
    console.log('🔍 Dashboard layout state changed:', {
      hasDashboardLayout: !!dashboardLayout,
      widgetCount: dashboardLayout?.widgets?.length || 0,
      rowCount: dashboardLayout?.rows?.length || 0
    });
  }, [dashboardLayout]);

  // Helper function to check if layout_config has new schema with comprehensive validation
  const hasNewSchema = (layout_config: any): layout_config is DashboardLayout => {
    try {
      console.log('🔍 Checking layout schema for:', layout_config);

      // Basic structure check first
      if (!layout_config || typeof layout_config !== 'object') {
        console.log('❌ Layout config is not an object');
        return false;
      }

      // Check for new schema structure - simplified check
      const hasGrid = layout_config.grid && typeof layout_config.grid === 'object';
      const hasWidgets = Array.isArray(layout_config.widgets);
      const hasRows = Array.isArray(layout_config.rows);

      console.log('🔍 Schema structure check:', { hasGrid, hasWidgets, hasRows });
      console.log('🔍 Grid details:', layout_config.grid);
      console.log('🔍 Widgets count:', layout_config.widgets?.length);
      console.log('🔍 Rows count:', layout_config.rows?.length);

      if (hasGrid && hasWidgets && hasRows) {
        console.log('✅ Basic new schema structure detected - using new schema');
        return true;
      } else {
        console.log('❌ Missing required new schema structure - using legacy schema');
        return false;
      }

    } catch (error) {
      console.error('❌ Error validating layout config:', error);
      return false;
    }
  };

  useEffect(() => {
    const fetchComponents = async () => {
      if (!page) {
        setComponents([]);
        setDashboardLayout(null);
        setApiLoadingQueue([]);
        setActiveApiLoading(new Set());
        setCompletedApiLoading(new Set());
        setLoadingComponents(new Set());
        return;
      }
      setLoading(true);
      try {
        // Check if page uses new dashboard schema
        console.log('Page data:', page);
        console.log('Layout config:', page.layout_config);
        console.log('Normalized layout:', page.normalized_layout);

        // Use normalized_layout if available, otherwise fall back to layout_config
        const layoutToUse = page.normalized_layout || page.layout_config;
        console.log('Layout to use:', layoutToUse);

        // Force new schema for page 7 (temporary fix for debugging)
        const forceNewSchema = page.id === 7 || page.id === '7';
        console.log('🔧 Force new schema for page 7:', forceNewSchema);

        if (forceNewSchema || hasNewSchema(layoutToUse)) {
          console.log('✅ Using new dashboard schema');

          try {
            // Additional validation for widget references in rows
            const widgetIds = new Set(layoutToUse.widgets.map((w: any) => w.id));
            const referencedWidgets = new Set();

            console.log('🔍 Widget IDs:', Array.from(widgetIds));

            layoutToUse.rows.forEach((row: any) => {
              console.log('🔍 Processing row:', row.id, 'with items:', row.items);
              row.items.forEach((item: any) => {
                referencedWidgets.add(item.widgetRef);
                if (!widgetIds.has(item.widgetRef)) {
                  throw new Error(`Widget reference '${item.widgetRef}' not found in widgets array`);
                }
              });
            });

            console.log('🔍 Referenced widgets:', Array.from(referencedWidgets));

            // Check for orphaned widgets (widgets not referenced in any row)
            const orphanedWidgets = Array.from(widgetIds).filter(id => !referencedWidgets.has(id));
            if (orphanedWidgets.length > 0) {
              console.warn('⚠️ Found orphaned widgets (not referenced in any row):', orphanedWidgets);
            }

            console.log('🔧 Setting dashboard layout...');
            console.log('🔧 Layout to set:', layoutToUse);
            setDashboardLayout(layoutToUse);
            setComponents([]); // Clear old components

            // Force a re-render to ensure the layout is applied
            setTimeout(() => {
              console.log('🔧 Dashboard layout after timeout:', dashboardLayout);
            }, 100);

            // Reset API loading state
            setActiveApiLoading(new Set());
            setCompletedApiLoading(new Set());
            setLoadingComponents(new Set());

            // Setup API loading queue for widgets that need API calls
            const widgetsNeedingApi = layoutToUse.widgets
              .filter((widget: any) => widget.dataSource?.componentId)
              .map((widget: any) => widget.id);
            console.log('🔧 Widgets needing API calls:', widgetsNeedingApi);
            setApiLoadingQueue(widgetsNeedingApi);

            console.log('✅ New schema setup completed successfully');

          } catch (layoutError) {
            console.error('❌ New schema validation failed, falling back to legacy:', layoutError);
            // Fall back to legacy schema processing
            setDashboardLayout(null);
            throw new Error('New schema validation failed');
          }
          
        } else {
          console.log('❌ Using legacy component schema');
          console.log('Page components:', page.components);
          setDashboardLayout(null);
          let items: GridItem[] = [];
          
          if (process.env.NODE_ENV === 'test') {
            const mock = buildMockPageContent(String(page.id));
            items = (mock.components || []).map((comp, index) => {
              const chartType = comp.type.replace('-chart', '') as ChartType;
              return {
                id: String(index),
                title: comp.name,
                chartType,
                data: getMockChartData(chartType),
                layout: comp.layout,
              };
            });
          } else {
            // Check if page already has components data (from page detail)
            if (page.components && page.components.length > 0) {
              items = ((page.components as PageComponentResponse[]) || []).map((comp) => ({
                id: String(comp.id),
                title: comp.component_template.name,
                chartType: comp.component_template.chart_type as ChartType,
                componentId: comp.id,
                layout: {
                  x: comp.grid_position?.x,
                  y: comp.grid_position?.y,
                  w: comp.grid_position?.w,
                  h: comp.grid_position?.h,
                },
              }));
            } else {
              // Fallback to fetching components separately
              const response = await apiClient.getPageComponents(page.id);
              // Ensure response is an array, handle both array and object with data property
              const componentData = Array.isArray(response) ? response : ((response as any)?.data || (response as any)?.results || []);
              items = (componentData || []).map((comp: PageComponentResponse) => ({
                id: String(comp.id),
                title: comp.component_template.name,
                chartType: comp.component_template.chart_type as ChartType,
                componentId: comp.id,
                layout: {
                  x: comp.grid_position?.x,
                  y: comp.grid_position?.y,
                  w: comp.grid_position?.w,
                  h: comp.grid_position?.h,
                },
              }));
            }
          }
          
          // Set all components for immediate rendering
          setComponents(items);
          
          // Reset API loading state
          setActiveApiLoading(new Set());
          setCompletedApiLoading(new Set());
          setLoadingComponents(new Set());
          
          // Setup API loading queue only for components that need API calls
          const componentsNeedingApi = items
            .filter(item => item.componentId && !item.data)
            .map(item => item.id);
          setApiLoadingQueue(componentsNeedingApi);
        }
        
      } catch (error) {
        console.error('Failed to load components:', error);
        
        // If new schema failed, try to fall back to legacy schema
        const fallbackLayout = page.normalized_layout || page.layout_config;
        if (fallbackLayout && !hasNewSchema(fallbackLayout)) {
          console.log('Attempting fallback to legacy component processing');
          
          try {
            setDashboardLayout(null);
            let fallbackItems: GridItem[] = [];
            
            // Try to extract components from page data or fetch them
            if (page.components && page.components.length > 0) {
              fallbackItems = ((page.components as PageComponentResponse[]) || []).map((comp) => ({
                id: String(comp.id),
                title: comp.component_template.name,
                chartType: comp.component_template.chart_type as ChartType,
                componentId: comp.id,
                layout: {
                  x: comp.grid_position?.x || 0,
                  y: comp.grid_position?.y || 0,
                  w: comp.grid_position?.w || 4,
                  h: comp.grid_position?.h || 3,
                },
              }));
              
              setComponents(fallbackItems);
              
              // Setup API loading for fallback components
              const componentsNeedingApi = fallbackItems
                .filter(item => item.componentId && !item.data)
                .map(item => item.id);
              setApiLoadingQueue(componentsNeedingApi);
              
              console.log('Successfully fell back to legacy schema with page components');
            } else {
              console.warn('No fallback data available, showing empty state');
              setComponents([]);
              setApiLoadingQueue([]);
            }
            
            // Reset API loading state
            setActiveApiLoading(new Set());
            setCompletedApiLoading(new Set());
            setLoadingComponents(new Set());
            
          } catch (fallbackError) {
            console.error('Fallback to legacy schema also failed:', fallbackError);
            setComponents([]);
            setDashboardLayout(null);
            setApiLoadingQueue([]);
          }
        } else {
          // Complete failure - clear everything
          setComponents([]);
          setDashboardLayout(null);
          setApiLoadingQueue([]);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchComponents();
  }, [page]);

  const refreshComponents = async () => {
    let error = false;
    for (const id of Object.keys(componentRefs.current)) {
      const comp = componentRefs.current[id];
      if (comp) {
        try {
          await comp.refresh();
        } catch {
          error = true;
        }
      }
    }
    if (error) {
      setRefreshError(true);
    }
  };

  useImperativeHandle(ref, () => ({ refresh: refreshComponents }));

  if (!page) {
    return (
      <div className="bg-base-100 p-12 rounded-lg shadow-md text-center">
        <h2 className="text-2xl font-medium mb-4 text-base-content">페이지를 선택해주세요</h2>
        <p className="text-lg text-base-content/70">
          상단의 페이지 탭을 클릭하여 대시보드를 확인하세요.
        </p>
      </div>
    );
  }

  const showOverlay = loading || isRefreshing;

  return (
    <div className="bg-base-100 p-8 rounded-lg shadow-md relative overflow-x-hidden w-full">
      {showOverlay && (
        <div className="absolute inset-0 bg-base-100/60 flex items-center justify-center z-50">
          <span className="loading loading-spinner loading-lg text-primary"></span>
        </div>
      )}

      {refreshError && (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
          <div className="card bg-base-100 shadow-xl p-6 text-center border border-error">
            <p className="text-base-content mb-4">일부 데이터를 새로고침하지 못했습니다. 이전 데이터가 유지됩니다.</p>
            <button className="btn btn-error" onClick={() => setRefreshError(false)}>닫기</button>
          </div>
        </div>
      )}

      <div className="mb-8">
        <h2 className="text-3xl font-bold text-base-content">{page.name}</h2>
      </div>

      {dashboardLayout ? (
        <LayoutErrorBoundary
          enableRecovery={true}
          recoveryDelay={3000}
          onError={(error, errorInfo) => {
            console.error('Dashboard layout error:', error, errorInfo);
            // In production, you might want to send this to an error tracking service
          }}
        >
          <DashboardGrid
            layout={dashboardLayout}
            loadingComponents={loadingComponents}
            onComponentRef={(id, ref) => {
              componentRefs.current[id] = ref;
            }}
            onComponentLoaded={handleApiLoadCompleted}
          />
        </LayoutErrorBoundary>
      ) : (
        <LayoutErrorBoundary
          enableRecovery={true}
          recoveryDelay={2000}
          onError={(error, errorInfo) => {
            console.error('Component grid error:', error, errorInfo);
          }}
        >
          <ComponentGrid
            items={(components || []).map(component => ({
              ...component,
              isLoading: loadingComponents.has(component.id)
            }))}
            onComponentRef={(id, ref) => {
              componentRefs.current[id] = ref;
            }}
            onComponentLoaded={handleApiLoadCompleted}
          />
        </LayoutErrorBoundary>
      )}
      
      {/* API loading queue status */}
      {apiLoadingQueue.length > 0 && (
        <div className="alert alert-info mb-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="loading loading-spinner loading-sm"></span>
            <span className="text-sm text-base-content">데이터 로딩 중...</span>
          </div>
          <div className="text-sm text-base-content/70">
            로딩 완료: {completedApiLoading.size} / {components.length} |
            활성 API 호출: {activeApiLoading.size} |
            대기열: {apiLoadingQueue.length}
          </div>
        </div>
      )}
      
      {(() => {
        const shouldShowNoComponents = !dashboardLayout && components.length === 0 && !loading;
        console.log('🔍 Render condition check:', {
          dashboardLayout: !!dashboardLayout,
          componentsLength: components.length,
          loading,
          shouldShowNoComponents
        });

        if (shouldShowNoComponents) {
          console.log('❌ Showing "no components" message');
        }

        return shouldShowNoComponents && (
          <div className="p-8 text-center">
            <p className="text-base text-base-content/70">이 페이지에는 구성요소가 없습니다.</p>
          </div>
        );
      })()}
      
      {dashboardLayout && dashboardLayout.widgets.length === 0 && !loading && (
        <div className="p-8 text-center">
          <p className="text-base text-base-content/70">이 대시보드에는 위젯이 없습니다.</p>
        </div>
      )}

      <div className="mt-6 p-4 rounded-lg bg-base-200 border border-base-300">
        <strong className="text-base text-base-content">페이지 정보:</strong>
        <span className="text-sm text-base-content/70 block mt-1">
          권한 레벨: {page.permission_level === 'regular' ? '일반 사용자' : '조직 관리자'} |
          페이지 ID: {page.id} |
          마지막 업데이트: {new Date().toLocaleString('ko-KR')}
        </span>
      </div>

      {/* Development-only debug panel */}
      {process.env.NODE_ENV === 'development' && (
        <LayoutDebugPanel
          layout={dashboardLayout}
          isVisible={debugPanelVisible}
          onToggle={() => setDebugPanelVisible(!debugPanelVisible)}
        />
      )}
    </div>
  );
});

export default PageContent;