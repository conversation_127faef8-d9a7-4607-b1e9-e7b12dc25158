import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import PermissionGuard from './PermissionGuard';
import RoleBadge from './ui/RoleBadge';
import { getRoleDisplayName, hasPermission } from '../utils/permissions';

const AdminPanel: React.FC = () => {
  const { user } = useAuth();

  const adminFeatures = [
    {
      title: '권한 관리',
      description: '사용자 권한 및 조직 관리',
      permission: 'manage_users',
      path: '/admin/permissions',
      icon: '👥'
    },
    {
      title: '대시보드 관리',
      description: '대시보드 설정 및 구성',
      permission: 'edit_dashboard',
      path: '/admin/dashboards',
      icon: '📊'
    },
    {
      title: '템플릿 관리',
      description: '컴포넌트 및 페이지 템플릿',
      permission: 'manage_templates',
      path: '/admin/templates',
      icon: '🎨'
    },
    {
      title: '시스템 설정',
      description: '전체 시스템 관리 및 설정',
      permission: 'manage_system',
      path: '/admin/system',
      icon: '⚙️'
    }
  ];

  return (
    <div className="p-5">
      <header className="mb-6 p-5 bg-base-200 rounded-xl border border-base-300">
        <div className="flex items-center gap-3 mb-2">
          <h1 className="m-0 text-base-content text-3xl font-bold">관리자 패널</h1>
          {user && <RoleBadge role={user.role} />}
        </div>
        <p className="m-0 text-base-content/70 text-base">
          시스템 관리 및 사용자 권한 관리를 위한 관리자 인터페이스입니다.
        </p>
      </header>

      <main>
        <div className="mb-6">
          <div className="grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-4">
            {adminFeatures.map(feature => (
              <PermissionGuard 
                key={feature.title}
                permission={feature.permission as any}
                fallback={
                  <div className="bg-base-200 p-5 rounded-lg border border-base-300 opacity-60 text-center">
                    <div className="text-2xl mb-2">{feature.icon}</div>
                    <h3 className="m-0 mb-2 text-base-content/70">{feature.title}</h3>
                    <p className="m-0 mb-3 text-base-content/70 text-sm">
                      {feature.description}
                    </p>
                    <div className="bg-warning text-warning-content px-3 py-1.5 rounded text-xs inline-block">
                      접근 권한 없음
                    </div>
                  </div>
                }
              >
                <Link 
                  to={feature.path}
                  className="block bg-base-100 p-5 rounded-lg shadow-md border border-base-300 no-underline text-base-content transition-all duration-200 ease-in-out hover:-translate-y-0.5 hover:shadow-lg"
                >
                  <div className="text-3xl mb-3">{feature.icon}</div>
                  <h3 className="m-0 mb-2 text-base-content">{feature.title}</h3>
                  <p className="m-0 text-base-content/70 text-sm">
                    {feature.description}
                  </p>
                </Link>
              </PermissionGuard>
            ))}
          </div>
        </div>

        {user && (
          <div className="bg-base-100 p-5 rounded-lg border border-base-300">
            <h3 className="m-0 mb-4 text-base-content">현재 사용자 정보</h3>
            
            <div className="grid grid-cols-[repeat(auto-fit,minmax(250px,1fr))] gap-4">
              <div>
                <h4 className="m-0 mb-2 text-base-content/80">기본 정보</h4>
                <p className="m-0 mb-1 text-sm">
                  <strong>이메일:</strong> {user.email}
                </p>
                <p className="m-0 mb-1 text-sm">
                  <strong>권한:</strong> {getRoleDisplayName(user.role)}
                </p>
                {user.organization ? (
                  <p className="m-0 text-sm">
                    <strong>소속 조직:</strong> {user.organization.name}
                  </p>
                ) : (
                  <p className="m-0 text-sm text-base-content/60">
                    <strong>소속 조직:</strong> 없음
                  </p>
                )}
              </div>
              
              <div>
                <h4 className="m-0 mb-2 text-base-content/80">보유 권한</h4>
                <div className="flex flex-wrap gap-1.5">
                  {[
                    { key: 'view_dashboard', label: '대시보드 조회' },
                    { key: 'edit_dashboard', label: '대시보드 편집' },
                    { key: 'manage_users', label: '사용자 관리' },
                    { key: 'manage_organization', label: '조직 관리' },
                    { key: 'manage_templates', label: '템플릿 관리' },
                    { key: 'manage_system', label: '시스템 관리' }
                  ].filter(perm => hasPermission(user, perm.key as any)).map(perm => (
                    <span 
                      key={perm.key}
                      className="bg-primary/20 text-primary px-2 py-1 rounded-full text-xs font-medium"
                    >
                      {perm.label}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default AdminPanel;