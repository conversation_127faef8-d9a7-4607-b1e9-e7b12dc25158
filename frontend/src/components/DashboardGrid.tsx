/**
 * DashboardGrid Component
 * 
 * Renders dashboard layouts using the new DashboardLayout schema with proper
 * responsive behavior, widget render configurations, and CSS Grid positioning.
 * 
 * Key Features:
 * - Responsive grid system using Tailwind CSS utilities
 * - Mobile-first responsive design approach
 * - Widget render configuration support (aspect ratios, height constraints)
 * - Auto-height calculation for dynamic content
 * - Error boundaries for graceful failure handling
 * - CSS overflow management and content containment
 * 
 * <AUTHOR> Layout System
 * @version 2.0
 */

import React, { useEffect, useRef, useState } from 'react';
import { DashboardLayout, Widget, RowLayout } from '../types/dashboard';
import { getCurrentBreakpoint } from '../utils/layoutUtils';
import ChartComponent, { ChartType, ChartComponentHandle } from './ChartComponent';
import ComponentErrorBoundary from './ComponentErrorBoundary';
import { getMobileFirstColumnClasses, getMobileFirstPaddingClasses, getMobileFirstContainerClasses } from '../utils/gridUtils';
import { getRenderConfigClasses, mergeRenderConfig, validateRenderConfig } from '../utils/renderConfigUtils';

/**
 * Props for the DashboardGrid component
 * 
 * @interface DashboardGridProps
 * @property {DashboardLayout} layout - Complete dashboard layout configuration using new schema
 * @property {function} [onComponentRef] - Callback to receive chart component references for external control
 * @property {function} [onComponentLoaded] - Callback fired when individual components finish loading
 * @property {Set<string>} [loadingComponents] - Set of component IDs currently in loading state
 */
interface DashboardGridProps {
  layout: DashboardLayout;
  onComponentRef?: (id: string, ref: ChartComponentHandle | null) => void;
  onComponentLoaded?: (componentId: string) => void;
  loadingComponents?: Set<string>;
}

/**
 * Main DashboardGrid component that renders dashboard layouts using the new schema
 * 
 * This component handles:
 * - Responsive breakpoint detection and updates
 * - Row-based layout rendering with proper grid positioning
 * - Widget render configuration application
 * - Loading state management
 * - Error boundary integration
 * 
 * @param {DashboardGridProps} props - Component props
 * @returns {JSX.Element} Rendered dashboard grid
 */
const DashboardGrid: React.FC<DashboardGridProps> = ({
  layout,
  onComponentRef,
  onComponentLoaded,
  loadingComponents = new Set(),
}) => {
  // State for responsive behavior - tracks window width changes
  const [, setWindowWidth] = useState(window.innerWidth);
  const [currentBreakpoint, setCurrentBreakpoint] = useState(() => {
    const breakpointKey = getCurrentBreakpoint(window.innerWidth);
    return layout.grid.breakpoints[breakpointKey];
  });

  /**
   * Handle window resize events for responsive layout updates
   * Updates the current breakpoint when viewport width changes
   */
  useEffect(() => {
    const handleResize = () => {
      const newWidth = window.innerWidth;
      setWindowWidth(newWidth);
      const breakpointKey = getCurrentBreakpoint(newWidth);
      setCurrentBreakpoint(layout.grid.breakpoints[breakpointKey]);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [layout.grid.breakpoints]);

  return (
    <div className="dashboard-grid w-full max-w-none overflow-hidden min-h-0 px-4 sm:px-6 lg:px-8 transition-all duration-300 ease-in-out">
      {layout.rows.map((row) => (
        <RowGrid
          key={row.id}
          row={row}
          widgets={layout.widgets}
          breakpoint={currentBreakpoint}
          gap={layout.grid.gap}
          onComponentRef={onComponentRef}
          onComponentLoaded={onComponentLoaded}
          loadingComponents={loadingComponents}
        />
      ))}
    </div>
  );
};

/**
 * Props for the RowGrid component that renders individual dashboard rows
 * 
 * @interface RowGridProps
 * @property {RowLayout} row - Row layout configuration with widget placements
 * @property {Widget[]} widgets - Array of available widgets to render
 * @property {Object} breakpoint - Current responsive breakpoint configuration
 * @property {number} breakpoint.columns - Number of columns at current breakpoint
 * @property {number} breakpoint.rowUnit - Base row height unit in pixels
 * @property {number} gap - Gap between grid items in pixels
 * @property {function} [onComponentRef] - Callback for chart component references
 * @property {function} [onComponentLoaded] - Callback for component load completion
 * @property {Set<string>} loadingComponents - Set of component IDs in loading state
 */
interface RowGridProps {
  row: RowLayout;
  widgets: Widget[];
  breakpoint: { columns: number; rowUnit: number };
  gap: number;
  onComponentRef?: (id: string, ref: ChartComponentHandle | null) => void;
  onComponentLoaded?: (componentId: string) => void;
  loadingComponents: Set<string>;
}

/**
 * RowGrid component renders a single row of widgets with proper grid positioning
 * 
 * Key responsibilities:
 * - Applies Tailwind CSS Grid classes for responsive layout
 * - Handles auto-height calculation for dynamic content
 * - Manages widget render configurations and constraints
 * - Provides fallback UI for missing widgets
 * - Implements mobile-first responsive design patterns
 * 
 * @param {RowGridProps} props - Component props
 * @returns {JSX.Element} Rendered row with positioned widgets
 */
const RowGrid: React.FC<RowGridProps> = ({
  row,
  widgets,
  breakpoint,
  gap,
  onComponentRef,
  onComponentLoaded,
  loadingComponents,
}) => {
  const rowRef = useRef<HTMLDivElement>(null);
  const [isAutoHeightApplied, setIsAutoHeightApplied] = useState(false);



  /**
   * Auto-height calculation effect for rows with rowHeight='auto'
   * 
   * This effect:
   * 1. Waits for all charts in the row to render
   * 2. Calculates the maximum height among all widgets
   * 3. Applies uniform height to all widgets in the row
   * 4. Ensures consistent row appearance regardless of content variation
   * 
   * The calculation is delayed to allow chart rendering to complete,
   * and only runs once per row to avoid layout thrashing.
   */
  useEffect(() => {
    if (row.rowHeight !== 'auto' || isAutoHeightApplied) return;

    const applyAutoHeight = () => {
      const el = rowRef.current;
      if (!el) return;

      const cards = Array.from(el.querySelectorAll<HTMLElement>(':scope > .grid-item'));
      if (cards.length === 0) return;

      // Wait for charts to render and calculate maximum height
      setTimeout(() => {
        const maxHeight = cards.reduce((max, card) => {
          const height = card.offsetHeight;
          return height > max ? height : max;
        }, 0);

        // Apply uniform height to all cards in the row
        if (maxHeight > 0) {
          cards.forEach(card => {
            card.style.height = `${maxHeight}px`;
          });
          setIsAutoHeightApplied(true);
        }
      }, 100);
    };

    // Apply auto height after component mounting and loading
    const timeoutId = setTimeout(applyAutoHeight, 200);
    
    return () => clearTimeout(timeoutId);
  }, [row.rowHeight, isAutoHeightApplied, loadingComponents]);

  // Reset auto height when row changes
  useEffect(() => {
    setIsAutoHeightApplied(false);
  }, [row.id]);

  /**
   * Generates Tailwind CSS classes for responsive grid layout
   * 
   * Uses mobile-first approach with responsive breakpoints:
   * - Mobile (base): Single column layout
   * - Small (sm): Single column layout  
   * - Medium (md): 2 columns
   * - Large (lg): 3 columns
   * - Extra Large (xl): 4 columns
   * 
   * Includes smooth transitions for responsive layout changes
   * and proper overflow handling for content containment.
   * 
   * @returns {string} Complete Tailwind CSS class string
   */
  const getGridClasses = () => {
    const baseClasses = 'grid w-full overflow-hidden min-h-0';
    const gapClass = `gap-${Math.min(gap, 8)}`; // Tailwind supports gap-0 to gap-8
    const marginClass = `mb-${Math.min(gap, 8)}`;
    
    // Use Tailwind's responsive prefixes with mobile-first approach
    // Base: mobile (grid-cols-1), then responsive overrides
    const responsiveGridClasses = 'grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
    
    // Add smooth transitions for layout changes
    const transitionClasses = 'transition-all duration-300 ease-in-out';
    
    return `${baseClasses} ${gapClass} ${marginClass} ${responsiveGridClasses} ${transitionClasses}`;
  };

  // Custom styles for non-standard values
  const customStyle: React.CSSProperties = {};
  
  // Handle custom gap values not supported by Tailwind
  if (gap > 8) {
    customStyle.gap = `${gap}px`;
    customStyle.marginBottom = `${gap}px`;
  }
  
  // Handle fixed row height
  if (typeof row.rowHeight === 'number') {
    customStyle.gridAutoRows = `${row.rowHeight}px`;
  }

  return (
    <div 
      ref={rowRef} 
      className={`row-grid ${getGridClasses()}`}
      style={Object.keys(customStyle).length > 0 ? customStyle : undefined}
    >
      {row.items.map((item) => {
        const widget = widgets.find(w => w.id === item.widgetRef);
        if (!widget) {
          console.warn(`Widget not found: ${item.widgetRef}`, {
            availableWidgets: widgets.map(w => w.id),
            requestedWidget: item.widgetRef,
            rowId: row.id
          });
          // Calculate grid positioning for missing widget
          const missingColStart = Math.min(item.col, breakpoint.columns);
          const missingColSpan = Math.min(item.span, breakpoint.columns - item.col + 1);
          
          const getMissingWidgetClasses = () => {
            const baseClasses = 'grid-item component-card card bg-warning text-warning-content shadow-lg overflow-hidden flex flex-col';
            
            // Use mobile-first utilities for consistent responsive behavior
            const containerClasses = getMobileFirstContainerClasses();
            const responsiveColClasses = getMobileFirstColumnClasses(missingColSpan);
            const responsivePaddingClasses = getMobileFirstPaddingClasses();
            
            // Add smooth transitions
            const transitionClasses = 'transition-all duration-300 ease-in-out';
            
            return `${baseClasses} ${containerClasses} ${responsiveColClasses} ${responsivePaddingClasses} ${transitionClasses}`;
          };

          const missingCustomStyle: React.CSSProperties = {};
          if (missingColStart > 12 || missingColSpan > 12 || missingColStart < 1 || missingColSpan < 1) {
            missingCustomStyle.gridColumn = `${missingColStart} / span ${missingColSpan}`;
          }

          return (
            <div
              key={item.widgetRef}
              className={getMissingWidgetClasses()}
              style={Object.keys(missingCustomStyle).length > 0 ? missingCustomStyle : undefined}
            >
              <h3 className="component-title text-lg font-medium m-0 mb-3 text-warning-content truncate">⚠️ Widget Missing</h3>
              <div className="flex-1 min-h-0 overflow-hidden break-words">Widget ID: {item.widgetRef} not found</div>
            </div>
          );
        }

        /**
         * Grid positioning calculation with boundary validation
         * 
         * colStart: Ensures starting column doesn't exceed available columns
         * colSpan: Calculates span width, ensuring it fits within remaining columns
         * 
         * This prevents widgets from extending beyond grid boundaries
         * and handles responsive column reduction gracefully.
         */
        const colStart = Math.min(item.col, breakpoint.columns);
        const colSpan = Math.min(item.span, breakpoint.columns - item.col + 1);
        
        /**
         * Widget render configuration processing pipeline:
         * 
         * 1. mergeRenderConfig: Combines chart-type defaults with widget-specific config
         * 2. validateRenderConfig: Ensures all values are within valid ranges
         * 3. getRenderConfigClasses: Converts config to Tailwind CSS classes and custom styles
         * 
         * This ensures consistent rendering behavior across all widget types
         * while allowing for customization per widget instance.
         */
        const mergedRenderConfig = mergeRenderConfig(widget.type, widget.render);
        const validatedRenderConfig = validateRenderConfig(mergedRenderConfig);
        const { classes: renderClasses, customStyles: renderStyles, customProperties } = getRenderConfigClasses(validatedRenderConfig);

        /**
         * Generates comprehensive CSS classes for grid item positioning and styling
         * 
         * Class composition strategy:
         * 1. Base classes: Core styling and layout structure
         * 2. Container classes: Width, overflow, and containment
         * 3. Responsive classes: Mobile-first column positioning
         * 4. Transition classes: Smooth animations for layout changes
         * 5. Render config classes: Widget-specific sizing and constraints
         * 6. State classes: Auto-height vs fixed-height behavior
         * 7. Fallback classes: Default styling when config is missing
         * 
         * This layered approach ensures consistent styling while allowing
         * for flexible customization through render configurations.
         */
        const getGridItemClasses = () => {
          const baseClasses = 'grid-item component-card card bg-base-100 shadow-lg overflow-hidden flex flex-col';
          
          // Use mobile-first utilities for responsive behavior
          const containerClasses = getMobileFirstContainerClasses();
          const responsiveColClasses = getMobileFirstColumnClasses(colSpan);
          const responsivePaddingClasses = getMobileFirstPaddingClasses();
          
          // Add smooth transitions for responsive changes
          const transitionClasses = 'transition-all duration-300 ease-in-out';
          
          // Add render configuration classes and CSS custom property support
          const renderConfigClasses = 'widget-render-config';
          const autoHeightClass = validatedRenderConfig.autoHeight ? 'widget-auto-height' : 'widget-fixed-height';
          
          // Add fallback class if no render config is provided
          const fallbackClass = Object.keys(validatedRenderConfig).length === 0 ? 'widget-render-fallback' : '';
          
          return `${baseClasses} ${containerClasses} ${responsiveColClasses} ${responsivePaddingClasses} ${transitionClasses} ${renderClasses} ${renderConfigClasses} ${autoHeightClass} ${fallbackClass}`.trim();
        };

        /**
         * Custom CSS styles for values beyond Tailwind's utility classes
         * 
         * Combines:
         * 1. renderStyles: Height, aspect-ratio, and overflow styles from render config
         * 2. customProperties: CSS custom properties for dynamic values
         * 3. Grid positioning fallbacks: For values outside Tailwind's range (1-12)
         * 4. Row height overrides: For fixed-height rows with multi-row spans
         * 
         * This hybrid approach maximizes Tailwind usage while providing
         * fallbacks for edge cases and dynamic values.
         */
        const customGridStyle: React.CSSProperties = { 
          ...renderStyles,
          ...Object.fromEntries(
            Object.entries(customProperties).map(([key, value]) => [key, value])
          )
        };
        
        // Fallback to custom CSS for unsupported grid values
        // Tailwind col-span and col-start classes only support 1-12
        if (colStart > 12 || colSpan > 12 || colStart < 1 || colSpan < 1) {
          customGridStyle.gridColumn = `${colStart} / span ${colSpan}`;
        }
        
        // Handle multi-row spans for fixed-height rows
        if (typeof row.rowHeight === 'number') {
          customGridStyle.gridRow = `span ${item.h || 1}`;
        }

        const isLoading = loadingComponents.has(widget.id);
        const loadingClass = isLoading ? 'component-loading' : '';

        return (
          <div
            key={item.widgetRef}
            className={`${getGridItemClasses()} ${loadingClass}`.trim()}
            style={Object.keys(customGridStyle).length > 0 ? customGridStyle : undefined}
          >
            <h3 className="component-title text-lg font-medium m-0 mb-3 text-base-content truncate">{widget.title}</h3>
            <div className={`component-chart-wrapper ${validatedRenderConfig.autoHeight ? 'flex-1 min-h-0' : 'flex-shrink-0'} overflow-hidden relative`}>
              <ComponentErrorBoundary>
                <div className="chart-content-container w-full h-full overflow-hidden">
                  <ChartComponent
                    type={widget.type as ChartType}
                    componentId={widget.dataSource.componentId}
                    apiUrl={widget.dataSource.apiUrl}
                    className="component-chart w-full h-full overflow-hidden"
                    disableAutoLoad={!!widget.dataSource.componentId}
                    ref={ref => onComponentRef && onComponentRef(widget.id, ref)}
                    onLoaded={() => onComponentLoaded && onComponentLoaded(widget.id)}
                    renderConfig={validatedRenderConfig}
                    title={widget.title}
                  />
                </div>
              </ComponentErrorBoundary>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default DashboardGrid;