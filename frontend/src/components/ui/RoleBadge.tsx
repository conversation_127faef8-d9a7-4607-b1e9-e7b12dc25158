import React from 'react';
import { Role, getRoleDisplayName } from '../../utils/permissions';

interface RoleBadgeProps {
  role: Role;
  size?: 'small' | 'medium' | 'large';
  variant?: 'solid' | 'outline' | 'subtle';
}

const RoleBadge: React.FC<RoleBadgeProps> = ({ 
  role, 
  size = 'medium', 
  variant = 'solid' 
}) => {
  const getRoleBadgeClasses = (role: Role, size: string, variant: string): string => {
    let baseClasses = 'badge';
    
    // Size classes
    switch (size) {
      case 'small':
        baseClasses += ' badge-sm';
        break;
      case 'large':
        baseClasses += ' badge-lg';
        break;
      default: // medium
        baseClasses += ' badge-md';
    }
    
    // Role-specific color and variant
    switch (role) {
      case 'regular':
        if (variant === 'outline') baseClasses += ' badge-primary badge-outline';
        else if (variant === 'subtle') baseClasses += ' badge-primary badge-ghost';
        else baseClasses += ' badge-primary';
        break;
      case 'org_admin':
        if (variant === 'outline') baseClasses += ' badge-success badge-outline';
        else if (variant === 'subtle') baseClasses += ' badge-success badge-ghost';
        else baseClasses += ' badge-success';
        break;
      case 'super_admin':
        if (variant === 'outline') baseClasses += ' badge-secondary badge-outline';
        else if (variant === 'subtle') baseClasses += ' badge-secondary badge-ghost';
        else baseClasses += ' badge-secondary';
        break;
      default:
        if (variant === 'outline') baseClasses += ' badge-neutral badge-outline';
        else if (variant === 'subtle') baseClasses += ' badge-neutral badge-ghost';
        else baseClasses += ' badge-neutral';
    }
    
    return baseClasses;
  };

  const displayName = getRoleDisplayName(role);
  const badgeClasses = getRoleBadgeClasses(role, size, variant);

  return (
    <span 
      className={badgeClasses}
      title={displayName}
    >
      {displayName}
    </span>
  );
};

export default RoleBadge;