import React from 'react';
import './Card.css';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'filled' | 'outlined' | 'elevated';
  padding?: 'none' | 'small' | 'medium' | 'large';
}

const Card: React.FC<CardProps> = ({
  variant = 'filled',
  padding = 'medium',
  children,
  className = '',
  ...props
}) => {
  // Map Material Design variants to DaisyUI card classes
  const variantClass = {
    filled: 'bg-base-100',
    outlined: 'border border-base-300',
    elevated: 'shadow-lg'
  }[variant];

  const paddingClass = {
    none: '',
    small: 'p-2',
    medium: 'p-4',
    large: 'p-6'
  }[padding];

  const classes = `card ${variantClass} ${paddingClass} ${className}`.trim();

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};

export default Card;