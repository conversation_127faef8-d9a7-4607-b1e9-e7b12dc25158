import React from 'react';
import './Button.css';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'filled' | 'outlined' | 'tonal' | 'text';
  size?: 'small' | 'medium' | 'large';
  icon?: React.ReactNode;
  iconPosition?: 'start' | 'end';
}

const Button: React.FC<ButtonProps> = ({
  variant = 'filled',
  size = 'medium',
  icon,
  iconPosition = 'start',
  children,
  className = '',
  disabled,
  ...props
}) => {
  // Map Material Design variants to DaisyUI button classes
  const variantClass = {
    filled: 'btn-primary',
    outlined: 'btn-outline',
    tonal: 'btn-secondary',
    text: 'btn-ghost'
  }[variant];

  const sizeClass = {
    small: 'btn-sm',
    medium: '',
    large: 'btn-lg'
  }[size];

  const classes = `btn ${variantClass} ${sizeClass} ${className}`.trim();

  return (
    <button
      className={classes}
      disabled={disabled}
      {...props}
    >
      {icon && iconPosition === 'start' && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
      {icon && iconPosition === 'end' && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
};

export default Button;