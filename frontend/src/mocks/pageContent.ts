import { ChartData, ChartType } from '../components/ChartComponent';

interface RawComponent {
  name: string;
  type: string;
  layout?: { w?: number; h?: number };
}

export const buildMockPageContent = (pageId: string): {
  title: string;
  description: string;
  components: RawComponent[];
} => {
  switch (pageId) {
    case '1':
      return {
        title: '매출 대시보드',
        description: '월별 매출 현황과 주요 지표를 확인할 수 있습니다.',
        components: [
          { name: '월별 매출 추이', type: 'line-chart', layout: { w: 6, h: 2 } },
          { name: '제품별 매출 비중', type: 'pie-chart', layout: { w: 3, h: 2 } },
          { name: '지역별 매출 현황', type: 'bar-chart', layout: { w: 3, h: 2 } },
          { name: '매출 목표 달성률', type: 'gauge-chart', layout: { w: 12, h: 2 } },
        ],
      };
    case '2':
      return {
        title: '마케팅 분석',
        description: '마케팅 캠페인 성과와 고객 획득 현황을 분석합니다.',
        components: [
          { name: '캠페인별 전환율', type: 'column-chart', layout: { w: 6, h: 2 } },
          { name: '채널별 트래픽', type: 'area-chart', layout: { w: 6, h: 2 } },
          { name: '고객 획득 비용', type: 'scatter-chart', layout: { w: 6, h: 2 } },
          { name: 'ROI 분석', type: 'heatmap', layout: { w: 6, h: 2 } },
        ],
      };
    case '3':
      return {
        title: '운영 현황',
        description: '시스템 운영 상태와 성능 지표를 모니터링합니다.',
        components: [
          { name: '서버 성능 지표', type: 'line-chart', layout: { w: 6, h: 2 } },
          { name: '에러율 추이', type: 'area-chart', layout: { w: 6, h: 2 } },
          { name: '사용자 활동', type: 'bar-chart', layout: { w: 6, h: 2 } },
          { name: '시스템 상태', type: 'gauge-chart', layout: { w: 6, h: 2 } },
        ],
      };
    case '4':
      return {
        title: '관리자 리포트',
        description: '전체 시스템 현황과 관리자 전용 데이터를 제공합니다.',
        components: [
          { name: '사용자 통계', type: 'pie-chart', layout: { w: 4, h: 2 } },
          { name: '데이터 처리량', type: 'column-chart', layout: { w: 4, h: 2 } },
          { name: '보안 이벤트', type: 'heatmap', layout: { w: 4, h: 2 } },
          { name: '시스템 리소스', type: 'radar-chart', layout: { w: 12, h: 2 } },
          { name: '자동 크기 테스트', type: 'line-chart' }, // Test component without layout
        ],
      };
    default:
      return {
        title: 'Unknown Page',
        description: '페이지 내용을 로드하고 있습니다.',
        components: [],
      };
  }
};

export const getMockChartData = (chartType: ChartType): ChartData => {
  switch (chartType) {
    case 'line':
    case 'area':
    case 'bar':
      return {
        data: [
          { name: 'Jan', A: 10, B: 5 },
          { name: 'Feb', A: 20, B: 15 },
          { name: 'Mar', A: 30, B: 25 },
          { name: 'Apr', A: 40, B: 35 },
          { name: 'May', A: 50, B: 45 },
        ],
        categories: ['A', 'B'],
      };
    case 'donut':
      return {
        data: [
          { name: 'Chrome', value: 46 },
          { name: 'Safari', value: 20 },
          { name: 'Firefox', value: 15 },
          { name: 'Edge', value: 10 },
          { name: 'Others', value: 9 },
        ],
      };
    case 'scatter':
      return {
        data: [
          { name: 'Point 1', x: 10, y: 20 },
          { name: 'Point 2', x: 20, y: 30 },
          { name: 'Point 3', x: 30, y: 25 },
          { name: 'Point 4', x: 40, y: 35 },
          { name: 'Point 5', x: 50, y: 45 },
        ],
        categories: ['x', 'y'],
      };
    case 'metric':
    case 'kpi':
      return {
        data: [
          { name: 'Total Revenue', value: 125420 },
        ],
      };
    default:
      return { 
        data: [{ name: 'No Data', value: 0 }]
      };
  }
};

