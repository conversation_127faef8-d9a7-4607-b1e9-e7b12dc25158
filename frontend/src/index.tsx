import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>hRouter } from 'react-router-dom';
import './index.css';
import App from './App';

// Initialize theme early
const savedTheme = localStorage.getItem('ui-theme') || 'corporate';
document.documentElement.setAttribute('data-theme', savedTheme);

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <HashRouter>
      <App />
    </HashRouter>
  </React.StrictMode>
);