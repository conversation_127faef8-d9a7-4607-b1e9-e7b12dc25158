/**
 * Tailwind CSS Grid Utility Functions
 * 
 * This module provides utility functions for generating Tailwind CSS grid classes
 * for dynamic dashboard layouts. It supports responsive grid systems and proper
 * component positioning within CSS Grid containers.
 */

export interface GridBreakpoint {
  columns: number;
  minWidth?: number;
}

export interface GridPosition {
  x: number;
  y: number;
  w: number;
  h: number;
}

export interface ResponsiveGridConfig {
  sm: GridBreakpoint;
  md: GridBreakpoint;
  lg: GridBreakpoint;
  xl: GridBreakpoint;
}

/**
 * Default responsive grid configuration following Tailwind's breakpoint system
 */
export const DEFAULT_GRID_CONFIG: ResponsiveGridConfig = {
  sm: { columns: 1, minWidth: 640 },
  md: { columns: 2, minWidth: 768 },
  lg: { columns: 3, minWidth: 1024 },
  xl: { columns: 4, minWidth: 1280 }
};

/**
 * Generates Tailwind CSS grid column classes for component positioning
 * Uses col-span-{n} and col-start-{n} utilities for precise grid placement
 * 
 * @param position - Grid position with x, y, w, h coordinates
 * @param totalColumns - Total number of columns in the grid
 * @returns Tailwind CSS classes for column positioning
 */
export function getGridColumnClass(position: GridPosition, totalColumns: number = 12): string {
  const { x, w } = position;
  
  // Ensure values are within valid ranges
  const startCol = Math.max(1, Math.min(x + 1, totalColumns)); // Convert 0-based to 1-based
  const spanCols = Math.max(1, Math.min(w, totalColumns - startCol + 1));
  
  // Generate Tailwind classes
  const startClass = startCol > 1 ? `col-start-${startCol}` : '';
  const spanClass = `col-span-${spanCols}`;
  
  return [startClass, spanClass].filter(Boolean).join(' ');
}

/**
 * Generates Tailwind CSS grid row classes for multi-row components
 * Uses row-span-{n} for components that span multiple rows
 * 
 * @param position - Grid position with x, y, w, h coordinates
 * @param maxRows - Maximum number of rows to span (default: 6)
 * @returns Tailwind CSS classes for row positioning
 */
export function getGridRowClass(position: GridPosition, maxRows: number = 6): string {
  const { h } = position;
  
  // Limit row span to reasonable maximum
  const rowSpan = Math.max(1, Math.min(h, maxRows));
  
  return rowSpan > 1 ? `row-span-${rowSpan}` : '';
}

/**
 * Generates responsive grid container classes using Tailwind's responsive prefixes
 * Creates mobile-first responsive grid with proper column counts at each breakpoint
 * 
 * @param config - Responsive grid configuration (optional, uses default if not provided)
 * @returns Tailwind CSS classes for responsive grid container
 */
export function getResponsiveGridClasses(config: ResponsiveGridConfig = DEFAULT_GRID_CONFIG): string {
  const baseClasses = 'grid w-full max-w-none overflow-hidden min-h-0';
  const gapClasses = 'gap-2 sm:gap-3 lg:gap-4';
  const paddingClasses = 'px-4 sm:px-6 lg:px-8';
  const transitionClasses = 'transition-all duration-300 ease-in-out';
  
  // Mobile-first responsive grid columns
  const responsiveClasses = [
    'grid-cols-1',  // Mobile: single column
    'sm:grid-cols-1',  // Small: still single column
    `md:grid-cols-${config.md.columns}`,  // Medium: 2 columns
    `lg:grid-cols-${config.lg.columns}`,  // Large: 3 columns
    `xl:grid-cols-${config.xl.columns}`   // Extra large: 4 columns
  ].join(' ');
  
  return `${baseClasses} ${gapClasses} ${paddingClasses} ${transitionClasses} ${responsiveClasses}`;
}

/**
 * Generates complete grid item classes combining position and responsive behavior
 * 
 * @param position - Grid position with x, y, w, h coordinates
 * @param totalColumns - Total number of columns in the grid
 * @param includeRowSpan - Whether to include row span classes
 * @returns Complete Tailwind CSS classes for grid item
 */
export function getGridItemClasses(
  position: GridPosition, 
  totalColumns: number = 12,
  includeRowSpan: boolean = true
): string {
  const columnClasses = getGridColumnClass(position, totalColumns);
  const rowClasses = includeRowSpan ? getGridRowClass(position) : '';
  
  const baseClasses = 'min-h-0 overflow-hidden';
  
  return [baseClasses, columnClasses, rowClasses].filter(Boolean).join(' ');
}

/**
 * Calculates responsive column span based on current breakpoint
 * Adjusts component width to fit within available columns at different screen sizes
 * 
 * @param originalSpan - Original column span from layout configuration
 * @param currentBreakpoint - Current responsive breakpoint
 * @param maxColumns - Maximum columns available at current breakpoint
 * @returns Adjusted column span for current breakpoint
 */
export function getResponsiveColumnSpan(
  originalSpan: number, 
  currentBreakpoint: keyof ResponsiveGridConfig,
  maxColumns: number
): number {
  // Scale down column span for smaller breakpoints
  const scalingFactors = {
    sm: 1.0,  // Full width on mobile
    md: 0.8,  // Slightly smaller on tablet
    lg: 0.7,  // Smaller on desktop
    xl: 0.6   // Smallest on large desktop
  };
  
  const scaleFactor = scalingFactors[currentBreakpoint] || 1.0;
  const scaledSpan = Math.ceil(originalSpan * scaleFactor);
  
  return Math.max(1, Math.min(scaledSpan, maxColumns));
}

/**
 * Generates mobile-first responsive column span classes
 * Uses Tailwind's responsive prefixes to create adaptive layouts
 * 
 * @param originalSpan - Original column span from layout configuration
 * @returns Tailwind CSS classes for responsive column spans
 */
export function getMobileFirstColumnClasses(originalSpan: number = 12): string {
  // Mobile-first approach: start with full width, then add responsive overrides
  const baseClass = 'col-span-12';  // Full width on mobile
  
  // Calculate responsive spans based on original span
  const mdSpan = Math.min(originalSpan, 6);   // Max 6 columns on medium screens
  const lgSpan = Math.min(originalSpan, 4);   // Max 4 columns on large screens  
  const xlSpan = Math.min(originalSpan, 3);   // Max 3 columns on extra large screens
  
  const responsiveClasses = [
    baseClass,
    `md:col-span-${mdSpan}`,
    `lg:col-span-${lgSpan}`,
    `xl:col-span-${xlSpan}`
  ].join(' ');
  
  return responsiveClasses;
}

/**
 * Generates mobile-first responsive padding classes
 * Provides appropriate spacing for different screen sizes
 * 
 * @returns Tailwind CSS classes for responsive padding
 */
export function getMobileFirstPaddingClasses(): string {
  return 'p-2 sm:p-3 lg:p-4';
}

/**
 * Generates mobile-first responsive container classes
 * Ensures proper width and overflow handling across screen sizes
 * 
 * @returns Tailwind CSS classes for responsive containers
 */
export function getMobileFirstContainerClasses(): string {
  return 'w-full max-w-none overflow-hidden min-h-0';
}

/**
 * Validates grid position to ensure it fits within grid boundaries
 * 
 * @param position - Grid position to validate
 * @param totalColumns - Total number of columns in the grid
 * @param totalRows - Total number of rows in the grid (optional)
 * @returns Validated and corrected grid position
 */
export function validateGridPosition(
  position: GridPosition, 
  totalColumns: number = 12,
  totalRows?: number
): GridPosition {
  const validated = { ...position };
  
  // Ensure position is within grid boundaries
  validated.x = Math.max(0, Math.min(position.x, totalColumns - 1));
  validated.w = Math.max(1, Math.min(position.w, totalColumns - validated.x));
  
  if (totalRows) {
    validated.y = Math.max(0, Math.min(position.y, totalRows - 1));
    validated.h = Math.max(1, Math.min(position.h, totalRows - validated.y));
  }
  
  return validated;
}

/**
 * Generates CSS custom properties for dynamic grid values
 * Useful for runtime grid adjustments that can't be handled by static Tailwind classes
 * 
 * @param position - Grid position
 * @param totalColumns - Total columns in grid
 * @returns CSS custom properties object
 */
export function getGridCustomProperties(
  position: GridPosition,
  totalColumns: number = 12
): Record<string, string> {
  const { x, y, w, h } = position;
  
  return {
    '--grid-column-start': String(x + 1),
    '--grid-column-end': String(x + w + 1),
    '--grid-row-start': String(y + 1),
    '--grid-row-end': String(y + h + 1),
    '--grid-total-columns': String(totalColumns)
  };
}