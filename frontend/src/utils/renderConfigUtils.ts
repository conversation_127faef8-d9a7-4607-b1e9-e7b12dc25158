/**
 * Render Configuration Utility Functions
 * 
 * This module provides utility functions for converting widget render configurations
 * to Tailwind CSS classes and CSS custom properties for dynamic dashboard layouts.
 */

import { WidgetRenderConfig } from '../types/dashboard';

export interface RenderConfigClasses {
  classes: string;
  customStyles: React.CSSProperties;
  customProperties: Record<string, string>;
}

/**
 * Converts widget render configuration to Tailwind CSS classes and custom styles
 * 
 * @param renderConfig - Widget render configuration
 * @returns Object containing Tailwind classes, custom styles, and CSS custom properties
 */
export function getRenderConfigClasses(renderConfig: WidgetRenderConfig = {}): RenderConfigClasses {
  const classes: string[] = [];
  const customStyles: React.CSSProperties = {};
  const customProperties: Record<string, string> = {};

  // Handle aspect ratio
  if (renderConfig.aspectRatio) {
    const ratio = renderConfig.aspectRatio;
    
    // Use Tailwind's built-in aspect ratio utilities for common ratios
    if (ratio === 1.0) {
      classes.push('aspect-square');
    } else if (Math.abs(ratio - 16/9) < 0.01) {
      classes.push('aspect-video');
    } else if (Math.abs(ratio - 4/3) < 0.01) {
      classes.push('aspect-[4/3]');
    } else if (Math.abs(ratio - 3/2) < 0.01) {
      classes.push('aspect-[3/2]');
    } else if (Math.abs(ratio - 5/4) < 0.01) {
      classes.push('aspect-[5/4]');
    } else if (Math.abs(ratio - 2/1) < 0.01) {
      classes.push('aspect-[2/1]');
    } else if (Math.abs(ratio - 5/3) < 0.01) {
      classes.push('aspect-[5/3]');
    } else {
      // Use arbitrary value for custom ratios
      const width = Math.round(ratio * 100);
      const height = 100;
      classes.push(`aspect-[${width}/${height}]`);
    }
    
    // Store aspect ratio as custom property for potential CSS usage
    customProperties['--aspect-ratio'] = String(ratio);
  }

  // Handle fixed height
  if (renderConfig.fixedHeight) {
    const height = renderConfig.fixedHeight;
    
    // Use Tailwind height classes for common values (multiples of 4px up to 384px)
    if (height <= 384 && height % 4 === 0) {
      const heightClass = height / 4;
      classes.push(`h-${heightClass}`);
    } else {
      // Use arbitrary value for custom heights
      classes.push(`h-[${height}px]`);
    }
    
    customProperties['--fixed-height'] = `${height}px`;
  } else if (!renderConfig.autoHeight) {
    classes.push('h-auto');
  }

  // Handle min height
  if (renderConfig.minHeight) {
    const minHeight = renderConfig.minHeight;
    
    // Use Tailwind min-height classes for common values
    if (minHeight <= 384 && minHeight % 4 === 0) {
      const minHeightClass = minHeight / 4;
      classes.push(`min-h-${minHeightClass}`);
    } else {
      // Use arbitrary value for custom min heights
      classes.push(`min-h-[${minHeight}px]`);
    }
    
    customProperties['--min-height'] = `${minHeight}px`;
  }

  // Handle max height
  if (renderConfig.maxHeight) {
    const maxHeight = renderConfig.maxHeight;
    
    // Use Tailwind max-height classes for common values
    if (maxHeight <= 384 && maxHeight % 4 === 0) {
      const maxHeightClass = maxHeight / 4;
      classes.push(`max-h-${maxHeightClass}`);
    } else {
      // Use arbitrary value for custom max heights
      classes.push(`max-h-[${maxHeight}px]`);
    }
    
    customProperties['--max-height'] = `${maxHeight}px`;
  }

  // Handle auto height behavior
  if (renderConfig.autoHeight) {
    classes.push('h-auto');
    customProperties['--auto-height'] = 'true';
  }

  // Add overflow handling based on render config
  const overflow = renderConfig.overflow || 'hidden';
  switch (overflow) {
    case 'hidden':
      classes.push('overflow-hidden');
      break;
    case 'scroll':
      classes.push('overflow-auto');
      break;
    case 'visible':
      classes.push('overflow-visible');
      break;
    default:
      classes.push('overflow-hidden');
  }

  // Add padding if specified
  if (renderConfig.padding) {
    const padding = renderConfig.padding;
    if (padding <= 32 && padding % 4 === 0) {
      const paddingClass = padding / 4;
      classes.push(`p-${paddingClass}`);
    } else {
      classes.push(`p-[${padding}px]`);
    }
    customProperties['--padding'] = `${padding}px`;
  }

  return {
    classes: classes.join(' '),
    customStyles,
    customProperties
  };
}

/**
 * Generates fallback render configuration for widgets without explicit config
 * 
 * @param chartType - Type of chart component
 * @returns Default render configuration based on chart type
 */
export function getDefaultRenderConfig(chartType: string): WidgetRenderConfig {
  const defaults: Record<string, WidgetRenderConfig> = {
    'radar': { aspectRatio: 1.0, minHeight: 220, autoHeight: false },
    'pie': { aspectRatio: 1.0, minHeight: 200, autoHeight: false },
    'donut': { aspectRatio: 1.0, minHeight: 200, autoHeight: false },
    'bar': { aspectRatio: 0.6, minHeight: 180, autoHeight: false },
    'line': { aspectRatio: 0.5, minHeight: 160, autoHeight: false },
    'area': { aspectRatio: 0.5, minHeight: 160, autoHeight: false },
    'column': { aspectRatio: 0.6, minHeight: 180, autoHeight: false },
    'gauge': { aspectRatio: 1.0, minHeight: 180, autoHeight: false },
    'heatmap': { aspectRatio: 0.8, minHeight: 200, autoHeight: false },
    'scatter': { aspectRatio: 0.7, minHeight: 180, autoHeight: false },
    'bubble': { aspectRatio: 0.7, minHeight: 180, autoHeight: false },
    'treemap': { aspectRatio: 0.8, minHeight: 200, autoHeight: false },
    'boxplot': { aspectRatio: 0.6, minHeight: 180, autoHeight: false }
  };

  return defaults[chartType] || { 
    aspectRatio: 0.7, 
    minHeight: 200, 
    autoHeight: false,
    overflow: 'hidden'
  };
}

/**
 * Merges default render config with widget-specific config
 * 
 * @param chartType - Type of chart component
 * @param widgetConfig - Widget-specific render configuration
 * @returns Merged render configuration
 */
export function mergeRenderConfig(
  chartType: string, 
  widgetConfig: WidgetRenderConfig = {}
): WidgetRenderConfig {
  const defaultConfig = getDefaultRenderConfig(chartType);
  
  return {
    ...defaultConfig,
    ...widgetConfig
  };
}

/**
 * Validates render configuration values
 * 
 * @param renderConfig - Render configuration to validate
 * @returns Validated render configuration with corrected values
 */
export function validateRenderConfig(renderConfig: WidgetRenderConfig): WidgetRenderConfig {
  const validated = { ...renderConfig };

  // Validate aspect ratio
  if (validated.aspectRatio !== undefined) {
    validated.aspectRatio = Math.max(0.1, Math.min(10, validated.aspectRatio));
  }

  // Validate height values
  if (validated.minHeight !== undefined) {
    validated.minHeight = Math.max(50, Math.min(1000, validated.minHeight));
  }

  if (validated.maxHeight !== undefined) {
    validated.maxHeight = Math.max(100, Math.min(2000, validated.maxHeight));
  }

  if (validated.fixedHeight !== undefined) {
    validated.fixedHeight = Math.max(50, Math.min(2000, validated.fixedHeight));
  }

  // Ensure min height is less than max height
  if (validated.minHeight && validated.maxHeight && validated.minHeight > validated.maxHeight) {
    validated.maxHeight = validated.minHeight + 50;
  }

  // Validate padding
  if (validated.padding !== undefined) {
    validated.padding = Math.max(0, Math.min(64, validated.padding));
  }

  // Validate overflow
  if (validated.overflow && !['hidden', 'scroll', 'visible'].includes(validated.overflow)) {
    validated.overflow = 'hidden';
  }

  return validated;
}

/**
 * Generates CSS custom properties for dynamic render configuration
 * Useful for runtime adjustments that can't be handled by static Tailwind classes
 * 
 * @param renderConfig - Widget render configuration
 * @returns CSS custom properties object
 */
export function getRenderConfigCustomProperties(renderConfig: WidgetRenderConfig): Record<string, string> {
  const properties: Record<string, string> = {};

  if (renderConfig.aspectRatio) {
    properties['--widget-aspect-ratio'] = String(renderConfig.aspectRatio);
  }

  if (renderConfig.minHeight) {
    properties['--widget-min-height'] = `${renderConfig.minHeight}px`;
  }

  if (renderConfig.maxHeight) {
    properties['--widget-max-height'] = `${renderConfig.maxHeight}px`;
  }

  if (renderConfig.fixedHeight) {
    properties['--widget-fixed-height'] = `${renderConfig.fixedHeight}px`;
  }

  if (renderConfig.padding) {
    properties['--widget-padding'] = `${renderConfig.padding}px`;
  }

  properties['--widget-auto-height'] = renderConfig.autoHeight ? 'true' : 'false';
  properties['--widget-overflow'] = renderConfig.overflow || 'hidden';

  return properties;
}