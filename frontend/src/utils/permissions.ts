import { User } from '../types/auth';

export type Role = 'regular' | 'org_admin' | 'super_admin';
export type Permission = 
  | 'view_dashboard'
  | 'edit_dashboard'
  | 'manage_users'
  | 'manage_organization'
  | 'manage_templates'
  | 'manage_system'
  | 'access_admin_panel';

// Role hierarchy for comparison
const ROLE_HIERARCHY: Record<Role, number> = {
  regular: 1,
  org_admin: 2,
  super_admin: 3,
};

// Permission definitions by role
const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  regular: [
    'view_dashboard'
  ],
  org_admin: [
    'view_dashboard',
    'edit_dashboard',
    'manage_users',
    'manage_organization',
    'access_admin_panel'
  ],
  super_admin: [
    'view_dashboard',
    'edit_dashboard',
    'manage_users',
    'manage_organization',
    'manage_templates',
    'manage_system',
    'access_admin_panel'
  ]
};

/**
 * Check if a user has a specific permission
 */
export const hasPermission = (user: User | null, permission: Permission): boolean => {
  if (!user) return false;
  return ROLE_PERMISSIONS[user.role]?.includes(permission) ?? false;
};

/**
 * Check if a user has any of the specified permissions
 */
export const hasAnyPermission = (user: User | null, permissions: Permission[]): boolean => {
  if (!user) return false;
  return permissions.some(permission => hasPermission(user, permission));
};

/**
 * Check if a user has all of the specified permissions
 */
export const hasAllPermissions = (user: User | null, permissions: Permission[]): boolean => {
  if (!user) return false;
  return permissions.every(permission => hasPermission(user, permission));
};

/**
 * Check if a user has the required role or higher
 */
export const hasRole = (user: User | null, requiredRole: Role): boolean => {
  if (!user) return false;
  return ROLE_HIERARCHY[user.role] >= ROLE_HIERARCHY[requiredRole];
};

/**
 * Check if a user has access to admin features
 */
export const isAdmin = (user: User | null): boolean => {
  return hasRole(user, 'org_admin');
};

/**
 * Check if a user is a super admin
 */
export const isSuperAdmin = (user: User | null): boolean => {
  return user?.role === 'super_admin';
};

/**
 * Get display name for a role
 */
export const getRoleDisplayName = (role: Role): string => {
  const roleNames: Record<Role, string> = {
    regular: '일반 사용자',
    org_admin: '조직 관리자',
    super_admin: '운영 관리자',
  };
  return roleNames[role];
};

/**
 * Get display name for a permission
 */
export const getPermissionDisplayName = (permission: Permission): string => {
  const permissionNames: Record<Permission, string> = {
    view_dashboard: '대시보드 조회',
    edit_dashboard: '대시보드 편집',
    manage_users: '사용자 관리',
    manage_organization: '조직 관리',
    manage_templates: '템플릿 관리',
    manage_system: '시스템 관리',
    access_admin_panel: '관리자 패널 접근',
  };
  return permissionNames[permission];
};

/**
 * Get all permissions for a role
 */
export const getRolePermissions = (role: Role): Permission[] => {
  return ROLE_PERMISSIONS[role] || [];
};

/**
 * Check if user can access a specific organization
 */
export const canAccessOrganization = (user: User | null, organizationId?: number): boolean => {
  if (!user) return false;
  
  // Super admin can access all organizations
  if (user.role === 'super_admin') return true;
  
  // Org admin and regular users can only access their own organization
  return user.organization?.id === organizationId;
};