import { DashboardLayout, GridConfig, RowLayout, Widget, WidgetRenderConfig, GridBreakpoint } from '../types/dashboard';

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  suggestions?: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  info: ValidationError[];
}

/**
 * Validates a complete dashboard layout configuration
 */
export function validateLayoutConfig(layout: any): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  // Basic structure validation
  if (!layout || typeof layout !== 'object') {
    errors.push({
      field: 'layout',
      message: 'Layout configuration must be an object',
      severity: 'error',
      suggestions: ['Ensure the layout is a valid JSON object']
    });
    return { isValid: false, errors, warnings, info };
  }

  // Validate required top-level properties
  const requiredProps = ['grid', 'rows', 'widgets'];
  for (const prop of requiredProps) {
    if (!(prop in layout)) {
      errors.push({
        field: prop,
        message: `Missing required property: ${prop}`,
        severity: 'error',
        suggestions: [`Add the ${prop} property to the layout configuration`]
      });
    }
  }

  // If basic structure is invalid, return early
  if (errors.length > 0) {
    return { isValid: false, errors, warnings, info };
  }

  // Validate grid configuration
  const gridValidation = validateGridConfig(layout.grid);
  errors.push(...gridValidation.errors);
  warnings.push(...gridValidation.warnings);
  info.push(...gridValidation.info);

  // Validate widgets
  const widgetsValidation = validateWidgets(layout.widgets);
  errors.push(...widgetsValidation.errors);
  warnings.push(...widgetsValidation.warnings);
  info.push(...widgetsValidation.info);

  // Validate rows
  const rowsValidation = validateRows(layout.rows);
  errors.push(...rowsValidation.errors);
  warnings.push(...rowsValidation.warnings);
  info.push(...rowsValidation.info);

  // Cross-reference validation (widgets referenced in rows)
  const crossRefValidation = validateWidgetReferences(layout.widgets, layout.rows);
  errors.push(...crossRefValidation.errors);
  warnings.push(...crossRefValidation.warnings);
  info.push(...crossRefValidation.info);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    info
  };
}

/**
 * Validates grid configuration
 */
export function validateGridConfig(grid: any): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!grid || typeof grid !== 'object') {
    errors.push({
      field: 'grid',
      message: 'Grid configuration must be an object',
      severity: 'error',
      suggestions: ['Provide a valid grid configuration object']
    });
    return { isValid: false, errors, warnings, info };
  }

  // Validate mode
  if (!grid.mode) {
    errors.push({
      field: 'grid.mode',
      message: 'Grid mode is required',
      severity: 'error',
      suggestions: ['Set mode to "strict-grid" or "masonry"']
    });
  } else if (!['strict-grid', 'masonry'].includes(grid.mode)) {
    errors.push({
      field: 'grid.mode',
      message: `Invalid grid mode: ${grid.mode}`,
      severity: 'error',
      suggestions: ['Use "strict-grid" or "masonry" as the grid mode']
    });
  }

  // Validate gap
  if (typeof grid.gap !== 'number') {
    errors.push({
      field: 'grid.gap',
      message: 'Grid gap must be a number',
      severity: 'error',
      suggestions: ['Set gap to a numeric value (e.g., 4, 8, 16)']
    });
  } else if (grid.gap < 0) {
    errors.push({
      field: 'grid.gap',
      message: 'Grid gap cannot be negative',
      severity: 'error',
      suggestions: ['Use a positive number for gap']
    });
  } else if (grid.gap > 32) {
    warnings.push({
      field: 'grid.gap',
      message: 'Large gap values may cause layout issues',
      severity: 'warning',
      suggestions: ['Consider using a smaller gap value (4-16 recommended)']
    });
  }

  // Validate breakpoints
  if (!grid.breakpoints || typeof grid.breakpoints !== 'object') {
    errors.push({
      field: 'grid.breakpoints',
      message: 'Breakpoints configuration is required',
      severity: 'error',
      suggestions: ['Provide breakpoints for sm, md, lg, xl']
    });
  } else {
    const breakpointValidation = validateBreakpoints(grid.breakpoints);
    errors.push(...breakpointValidation.errors);
    warnings.push(...breakpointValidation.warnings);
    info.push(...breakpointValidation.info);
  }

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates breakpoints configuration
 */
export function validateBreakpoints(breakpoints: any): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  const requiredBreakpoints = ['sm', 'md', 'lg', 'xl'];
  const expectedMinWidths = { sm: 640, md: 768, lg: 1024, xl: 1280 };

  for (const bp of requiredBreakpoints) {
    if (!(bp in breakpoints)) {
      errors.push({
        field: `grid.breakpoints.${bp}`,
        message: `Missing breakpoint: ${bp}`,
        severity: 'error',
        suggestions: [`Add ${bp} breakpoint configuration`]
      });
      continue;
    }

    const breakpoint = breakpoints[bp];
    
    // Validate breakpoint structure
    if (!breakpoint || typeof breakpoint !== 'object') {
      errors.push({
        field: `grid.breakpoints.${bp}`,
        message: `Breakpoint ${bp} must be an object`,
        severity: 'error',
        suggestions: [`Provide minWidth, columns, and rowUnit for ${bp}`]
      });
      continue;
    }

    // Validate minWidth
    if (typeof breakpoint.minWidth !== 'number') {
      errors.push({
        field: `grid.breakpoints.${bp}.minWidth`,
        message: `Breakpoint ${bp} minWidth must be a number`,
        severity: 'error',
        suggestions: [`Set minWidth to ${expectedMinWidths[bp as keyof typeof expectedMinWidths]}`]
      });
    } else if (breakpoint.minWidth !== expectedMinWidths[bp as keyof typeof expectedMinWidths]) {
      warnings.push({
        field: `grid.breakpoints.${bp}.minWidth`,
        message: `Breakpoint ${bp} minWidth (${breakpoint.minWidth}) differs from standard (${expectedMinWidths[bp as keyof typeof expectedMinWidths]})`,
        severity: 'warning',
        suggestions: [`Consider using standard minWidth: ${expectedMinWidths[bp as keyof typeof expectedMinWidths]}`]
      });
    }

    // Validate columns
    if (typeof breakpoint.columns !== 'number') {
      errors.push({
        field: `grid.breakpoints.${bp}.columns`,
        message: `Breakpoint ${bp} columns must be a number`,
        severity: 'error',
        suggestions: ['Set columns to a positive integer (typically 1-12)']
      });
    } else if (breakpoint.columns < 1 || breakpoint.columns > 24) {
      warnings.push({
        field: `grid.breakpoints.${bp}.columns`,
        message: `Breakpoint ${bp} has unusual column count: ${breakpoint.columns}`,
        severity: 'warning',
        suggestions: ['Consider using 1-12 columns for better compatibility']
      });
    }

    // Validate rowUnit
    if (typeof breakpoint.rowUnit !== 'number') {
      errors.push({
        field: `grid.breakpoints.${bp}.rowUnit`,
        message: `Breakpoint ${bp} rowUnit must be a number`,
        severity: 'error',
        suggestions: ['Set rowUnit to a positive number (e.g., 60, 80, 100)']
      });
    } else if (breakpoint.rowUnit < 20 || breakpoint.rowUnit > 200) {
      warnings.push({
        field: `grid.breakpoints.${bp}.rowUnit`,
        message: `Breakpoint ${bp} rowUnit (${breakpoint.rowUnit}) may cause usability issues`,
        severity: 'warning',
        suggestions: ['Consider using rowUnit between 60-120 for optimal display']
      });
    }
  }

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates widgets array
 */
export function validateWidgets(widgets: any): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!Array.isArray(widgets)) {
    errors.push({
      field: 'widgets',
      message: 'Widgets must be an array',
      severity: 'error',
      suggestions: ['Provide an array of widget configurations']
    });
    return { isValid: false, errors, warnings, info };
  }

  if (widgets.length === 0) {
    warnings.push({
      field: 'widgets',
      message: 'No widgets defined',
      severity: 'warning',
      suggestions: ['Add at least one widget to display content']
    });
  }

  const widgetIds = new Set<string>();

  widgets.forEach((widget, index) => {
    const widgetValidation = validateWidget(widget, index);
    errors.push(...widgetValidation.errors);
    warnings.push(...widgetValidation.warnings);
    info.push(...widgetValidation.info);

    // Check for duplicate IDs
    if (widget && widget.id) {
      if (widgetIds.has(widget.id)) {
        errors.push({
          field: `widgets[${index}].id`,
          message: `Duplicate widget ID: ${widget.id}`,
          severity: 'error',
          suggestions: ['Ensure all widget IDs are unique']
        });
      } else {
        widgetIds.add(widget.id);
      }
    }
  });

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates a single widget
 */
export function validateWidget(widget: any, index: number): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!widget || typeof widget !== 'object') {
    errors.push({
      field: `widgets[${index}]`,
      message: 'Widget must be an object',
      severity: 'error',
      suggestions: ['Provide a valid widget configuration object']
    });
    return { isValid: false, errors, warnings, info };
  }

  // Validate required properties
  const requiredProps = ['id', 'type', 'title', 'dataSource', 'render'];
  for (const prop of requiredProps) {
    if (!(prop in widget)) {
      errors.push({
        field: `widgets[${index}].${prop}`,
        message: `Missing required property: ${prop}`,
        severity: 'error',
        suggestions: [`Add ${prop} to widget configuration`]
      });
    }
  }

  // Validate ID
  if (widget.id && typeof widget.id !== 'string') {
    errors.push({
      field: `widgets[${index}].id`,
      message: 'Widget ID must be a string',
      severity: 'error',
      suggestions: ['Use a string value for widget ID']
    });
  } else if (widget.id && widget.id.trim() === '') {
    errors.push({
      field: `widgets[${index}].id`,
      message: 'Widget ID cannot be empty',
      severity: 'error',
      suggestions: ['Provide a non-empty string for widget ID']
    });
  }

  // Validate type
  if (widget.type && typeof widget.type !== 'string') {
    errors.push({
      field: `widgets[${index}].type`,
      message: 'Widget type must be a string',
      severity: 'error',
      suggestions: ['Use a valid chart type (e.g., "bar", "line", "pie")']
    });
  }

  // Validate title
  if (widget.title && typeof widget.title !== 'string') {
    errors.push({
      field: `widgets[${index}].title`,
      message: 'Widget title must be a string',
      severity: 'error',
      suggestions: ['Provide a string title for the widget']
    });
  } else if (widget.title && widget.title.trim() === '') {
    warnings.push({
      field: `widgets[${index}].title`,
      message: 'Widget title is empty',
      severity: 'warning',
      suggestions: ['Consider providing a descriptive title']
    });
  }

  // Validate dataSource
  if (widget.dataSource) {
    const dataSourceValidation = validateDataSource(widget.dataSource, index);
    errors.push(...dataSourceValidation.errors);
    warnings.push(...dataSourceValidation.warnings);
    info.push(...dataSourceValidation.info);
  }

  // Validate render config
  if (widget.render) {
    const renderValidation = validateRenderConfig(widget.render, index);
    errors.push(...renderValidation.errors);
    warnings.push(...renderValidation.warnings);
    info.push(...renderValidation.info);
  }

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates widget data source
 */
export function validateDataSource(dataSource: any, widgetIndex: number): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!dataSource || typeof dataSource !== 'object') {
    errors.push({
      field: `widgets[${widgetIndex}].dataSource`,
      message: 'DataSource must be an object',
      severity: 'error',
      suggestions: ['Provide componentId or apiUrl for data source']
    });
    return { isValid: false, errors, warnings, info };
  }

  // At least one data source method should be provided
  if (!dataSource.componentId && !dataSource.apiUrl) {
    warnings.push({
      field: `widgets[${widgetIndex}].dataSource`,
      message: 'No data source specified',
      severity: 'warning',
      suggestions: ['Provide either componentId or apiUrl for data']
    });
  }

  // Validate componentId
  if (dataSource.componentId !== undefined) {
    if (typeof dataSource.componentId !== 'number') {
      errors.push({
        field: `widgets[${widgetIndex}].dataSource.componentId`,
        message: 'ComponentId must be a number',
        severity: 'error',
        suggestions: ['Use a numeric component ID']
      });
    } else if (dataSource.componentId <= 0) {
      errors.push({
        field: `widgets[${widgetIndex}].dataSource.componentId`,
        message: 'ComponentId must be positive',
        severity: 'error',
        suggestions: ['Use a positive component ID']
      });
    }
  }

  // Validate apiUrl
  if (dataSource.apiUrl !== undefined) {
    if (typeof dataSource.apiUrl !== 'string') {
      errors.push({
        field: `widgets[${widgetIndex}].dataSource.apiUrl`,
        message: 'ApiUrl must be a string',
        severity: 'error',
        suggestions: ['Provide a valid URL string']
      });
    } else if (dataSource.apiUrl.trim() === '') {
      errors.push({
        field: `widgets[${widgetIndex}].dataSource.apiUrl`,
        message: 'ApiUrl cannot be empty',
        severity: 'error',
        suggestions: ['Provide a valid API endpoint URL']
      });
    } else {
      // Basic URL validation
      try {
        new URL(dataSource.apiUrl);
      } catch {
        // If it's not a full URL, check if it's a relative path
        if (!dataSource.apiUrl.startsWith('/')) {
          warnings.push({
            field: `widgets[${widgetIndex}].dataSource.apiUrl`,
            message: 'ApiUrl may not be a valid URL',
            severity: 'warning',
            suggestions: ['Ensure the URL is valid and accessible']
          });
        }
      }
    }
  }

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates widget render configuration
 */
export function validateRenderConfig(renderConfig: any, widgetIndex: number): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!renderConfig || typeof renderConfig !== 'object') {
    warnings.push({
      field: `widgets[${widgetIndex}].render`,
      message: 'Render configuration is missing or invalid',
      severity: 'warning',
      suggestions: ['Provide render configuration for better layout control']
    });
    return { isValid: true, errors, warnings, info };
  }

  // Validate autoHeight
  if (renderConfig.autoHeight !== undefined && typeof renderConfig.autoHeight !== 'boolean') {
    errors.push({
      field: `widgets[${widgetIndex}].render.autoHeight`,
      message: 'autoHeight must be a boolean',
      severity: 'error',
      suggestions: ['Use true or false for autoHeight']
    });
  }

  // Validate aspectRatio
  if (renderConfig.aspectRatio !== undefined) {
    if (typeof renderConfig.aspectRatio !== 'number') {
      errors.push({
        field: `widgets[${widgetIndex}].render.aspectRatio`,
        message: 'aspectRatio must be a number',
        severity: 'error',
        suggestions: ['Use a numeric value (e.g., 1.0 for square, 0.5 for 2:1)']
      });
    } else if (renderConfig.aspectRatio <= 0 || renderConfig.aspectRatio > 5) {
      warnings.push({
        field: `widgets[${widgetIndex}].render.aspectRatio`,
        message: `Unusual aspect ratio: ${renderConfig.aspectRatio}`,
        severity: 'warning',
        suggestions: ['Consider using aspect ratios between 0.3 and 3.0']
      });
    }
  }

  // Validate height constraints
  const heightProps = ['minHeight', 'maxHeight', 'fixedHeight'];
  for (const prop of heightProps) {
    if (renderConfig[prop] !== undefined) {
      if (typeof renderConfig[prop] !== 'number') {
        errors.push({
          field: `widgets[${widgetIndex}].render.${prop}`,
          message: `${prop} must be a number`,
          severity: 'error',
          suggestions: ['Use a numeric pixel value']
        });
      } else if (renderConfig[prop] < 0) {
        errors.push({
          field: `widgets[${widgetIndex}].render.${prop}`,
          message: `${prop} cannot be negative`,
          severity: 'error',
          suggestions: ['Use a positive pixel value']
        });
      } else if (renderConfig[prop] < 50 || renderConfig[prop] > 1000) {
        warnings.push({
          field: `widgets[${widgetIndex}].render.${prop}`,
          message: `${prop} value (${renderConfig[prop]}) may cause display issues`,
          severity: 'warning',
          suggestions: ['Consider using values between 100-600 pixels']
        });
      }
    }
  }

  // Validate height constraint logic
  if (renderConfig.minHeight && renderConfig.maxHeight && renderConfig.minHeight > renderConfig.maxHeight) {
    errors.push({
      field: `widgets[${widgetIndex}].render`,
      message: 'minHeight cannot be greater than maxHeight',
      severity: 'error',
      suggestions: ['Ensure minHeight ≤ maxHeight']
    });
  }

  // Validate overflow
  if (renderConfig.overflow !== undefined) {
    const validOverflow = ['hidden', 'scroll', 'visible'];
    if (!validOverflow.includes(renderConfig.overflow)) {
      errors.push({
        field: `widgets[${widgetIndex}].render.overflow`,
        message: `Invalid overflow value: ${renderConfig.overflow}`,
        severity: 'error',
        suggestions: ['Use "hidden", "scroll", or "visible"']
      });
    }
  }

  // Validate padding
  if (renderConfig.padding !== undefined) {
    if (typeof renderConfig.padding !== 'number') {
      errors.push({
        field: `widgets[${widgetIndex}].render.padding`,
        message: 'padding must be a number',
        severity: 'error',
        suggestions: ['Use a numeric pixel value']
      });
    } else if (renderConfig.padding < 0 || renderConfig.padding > 50) {
      warnings.push({
        field: `widgets[${widgetIndex}].render.padding`,
        message: `Unusual padding value: ${renderConfig.padding}`,
        severity: 'warning',
        suggestions: ['Consider using padding between 0-24 pixels']
      });
    }
  }

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates rows array
 */
export function validateRows(rows: any): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!Array.isArray(rows)) {
    errors.push({
      field: 'rows',
      message: 'Rows must be an array',
      severity: 'error',
      suggestions: ['Provide an array of row configurations']
    });
    return { isValid: false, errors, warnings, info };
  }

  if (rows.length === 0) {
    warnings.push({
      field: 'rows',
      message: 'No rows defined',
      severity: 'warning',
      suggestions: ['Add at least one row to display widgets']
    });
  }

  const rowIds = new Set<string>();

  rows.forEach((row, index) => {
    const rowValidation = validateRow(row, index);
    errors.push(...rowValidation.errors);
    warnings.push(...rowValidation.warnings);
    info.push(...rowValidation.info);

    // Check for duplicate row IDs
    if (row && row.id) {
      if (rowIds.has(row.id)) {
        errors.push({
          field: `rows[${index}].id`,
          message: `Duplicate row ID: ${row.id}`,
          severity: 'error',
          suggestions: ['Ensure all row IDs are unique']
        });
      } else {
        rowIds.add(row.id);
      }
    }
  });

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates a single row
 */
export function validateRow(row: any, index: number): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!row || typeof row !== 'object') {
    errors.push({
      field: `rows[${index}]`,
      message: 'Row must be an object',
      severity: 'error',
      suggestions: ['Provide a valid row configuration object']
    });
    return { isValid: false, errors, warnings, info };
  }

  // Validate required properties
  const requiredProps = ['id', 'rowHeight', 'items'];
  for (const prop of requiredProps) {
    if (!(prop in row)) {
      errors.push({
        field: `rows[${index}].${prop}`,
        message: `Missing required property: ${prop}`,
        severity: 'error',
        suggestions: [`Add ${prop} to row configuration`]
      });
    }
  }

  // Validate ID
  if (row.id && typeof row.id !== 'string') {
    errors.push({
      field: `rows[${index}].id`,
      message: 'Row ID must be a string',
      severity: 'error',
      suggestions: ['Use a string value for row ID']
    });
  }

  // Validate rowHeight
  if (row.rowHeight !== undefined) {
    if (row.rowHeight !== 'auto' && typeof row.rowHeight !== 'number') {
      errors.push({
        field: `rows[${index}].rowHeight`,
        message: 'rowHeight must be "auto" or a number',
        severity: 'error',
        suggestions: ['Use "auto" for automatic height or a numeric pixel value']
      });
    } else if (typeof row.rowHeight === 'number' && (row.rowHeight < 50 || row.rowHeight > 800)) {
      warnings.push({
        field: `rows[${index}].rowHeight`,
        message: `Unusual row height: ${row.rowHeight}`,
        severity: 'warning',
        suggestions: ['Consider using row heights between 100-400 pixels']
      });
    }
  }

  // Validate items
  if (row.items) {
    if (!Array.isArray(row.items)) {
      errors.push({
        field: `rows[${index}].items`,
        message: 'Row items must be an array',
        severity: 'error',
        suggestions: ['Provide an array of row item configurations']
      });
    } else {
      row.items.forEach((item: any, itemIndex: number) => {
        const itemValidation = validateRowItem(item, index, itemIndex);
        errors.push(...itemValidation.errors);
        warnings.push(...itemValidation.warnings);
        info.push(...itemValidation.info);
      });
    }
  }

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates a row item
 */
export function validateRowItem(item: any, rowIndex: number, itemIndex: number): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!item || typeof item !== 'object') {
    errors.push({
      field: `rows[${rowIndex}].items[${itemIndex}]`,
      message: 'Row item must be an object',
      severity: 'error',
      suggestions: ['Provide a valid row item configuration']
    });
    return { isValid: false, errors, warnings, info };
  }

  // Validate required properties
  const requiredProps = ['widgetRef', 'col', 'span', 'h'];
  for (const prop of requiredProps) {
    if (!(prop in item)) {
      errors.push({
        field: `rows[${rowIndex}].items[${itemIndex}].${prop}`,
        message: `Missing required property: ${prop}`,
        severity: 'error',
        suggestions: [`Add ${prop} to row item configuration`]
      });
    }
  }

  // Validate widgetRef
  if (item.widgetRef && typeof item.widgetRef !== 'string') {
    errors.push({
      field: `rows[${rowIndex}].items[${itemIndex}].widgetRef`,
      message: 'widgetRef must be a string',
      severity: 'error',
      suggestions: ['Reference a valid widget ID']
    });
  }

  // Validate numeric properties
  const numericProps = ['col', 'span', 'h'];
  for (const prop of numericProps) {
    if (item[prop] !== undefined) {
      if (typeof item[prop] !== 'number') {
        errors.push({
          field: `rows[${rowIndex}].items[${itemIndex}].${prop}`,
          message: `${prop} must be a number`,
          severity: 'error',
          suggestions: ['Use a positive integer value']
        });
      } else if (item[prop] < 1) {
        errors.push({
          field: `rows[${rowIndex}].items[${itemIndex}].${prop}`,
          message: `${prop} must be positive`,
          severity: 'error',
          suggestions: ['Use a value of 1 or greater']
        });
      } else if (prop === 'col' && item[prop] > 12) {
        warnings.push({
          field: `rows[${rowIndex}].items[${itemIndex}].${prop}`,
          message: `Column position ${item[prop]} exceeds typical grid width`,
          severity: 'warning',
          suggestions: ['Consider using columns 1-12 for better compatibility']
        });
      } else if (prop === 'span' && item[prop] > 12) {
        warnings.push({
          field: `rows[${rowIndex}].items[${itemIndex}].${prop}`,
          message: `Column span ${item[prop]} exceeds typical grid width`,
          severity: 'warning',
          suggestions: ['Consider using spans 1-12 for better compatibility']
        });
      }
    }
  }

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Validates widget references between widgets and rows
 */
export function validateWidgetReferences(widgets: Widget[], rows: RowLayout[]): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const info: ValidationError[] = [];

  if (!Array.isArray(widgets) || !Array.isArray(rows)) {
    return { isValid: true, errors, warnings, info };
  }

  const widgetIds = new Set(widgets.map(w => w.id));
  const referencedWidgets = new Set<string>();

  // Check all widget references in rows
  rows.forEach((row, rowIndex) => {
    if (Array.isArray(row.items)) {
      row.items.forEach((item, itemIndex) => {
        if (item.widgetRef) {
          referencedWidgets.add(item.widgetRef);
          
          if (!widgetIds.has(item.widgetRef)) {
            errors.push({
              field: `rows[${rowIndex}].items[${itemIndex}].widgetRef`,
              message: `Widget reference '${item.widgetRef}' not found`,
              severity: 'error',
              suggestions: [
                'Check if the widget ID is correct',
                'Ensure the widget exists in the widgets array',
                'Remove the reference if the widget is no longer needed'
              ]
            });
          }
        }
      });
    }
  });

  // Check for orphaned widgets (widgets not referenced in any row)
  const orphanedWidgets = widgets.filter(w => !referencedWidgets.has(w.id));
  if (orphanedWidgets.length > 0) {
    warnings.push({
      field: 'widgets',
      message: `Found ${orphanedWidgets.length} orphaned widget(s): ${orphanedWidgets.map(w => w.id).join(', ')}`,
      severity: 'warning',
      suggestions: [
        'Add references to these widgets in rows',
        'Remove unused widgets from the configuration'
      ]
    });
  }

  return { isValid: errors.length === 0, errors, warnings, info };
}

/**
 * Utility function to format validation results for display
 */
export function formatValidationResults(result: ValidationResult): string {
  const lines: string[] = [];
  
  if (result.isValid) {
    lines.push('✅ Layout configuration is valid');
  } else {
    lines.push('❌ Layout configuration has errors');
  }

  if (result.errors.length > 0) {
    lines.push('\n🚨 Errors:');
    result.errors.forEach(error => {
      lines.push(`  • ${error.field}: ${error.message}`);
      if (error.suggestions) {
        error.suggestions.forEach(suggestion => {
          lines.push(`    💡 ${suggestion}`);
        });
      }
    });
  }

  if (result.warnings.length > 0) {
    lines.push('\n⚠️ Warnings:');
    result.warnings.forEach(warning => {
      lines.push(`  • ${warning.field}: ${warning.message}`);
      if (warning.suggestions) {
        warning.suggestions.forEach(suggestion => {
          lines.push(`    💡 ${suggestion}`);
        });
      }
    });
  }

  if (result.info.length > 0) {
    lines.push('\nℹ️ Information:');
    result.info.forEach(info => {
      lines.push(`  • ${info.field}: ${info.message}`);
    });
  }

  return lines.join('\n');
}