/**
 * CSS-Based Size Constraints Utilities
 * 
 * This module provides utility functions for generating Tailwind CSS classes
 * for component size constraints, aspect ratios, and responsive sizing.
 * Uses modern CSS features like aspect-ratio and container queries.
 */

export interface WidgetRenderConfig {
  autoHeight?: boolean;
  aspectRatio?: number;
  minHeight?: number;
  maxHeight?: number;
  fixedHeight?: number;
  overflow?: 'hidden' | 'scroll' | 'visible';
  padding?: number;
}

export interface SizeConstraints {
  aspectRatioClass: string;
  heightClasses: string;
  overflowClasses: string;
  paddingClasses: string;
  customProperties: Record<string, string>;
}

/**
 * Predefined aspect ratio mappings to Tailwind CSS classes
 * Covers common dashboard widget proportions
 */
const ASPECT_RATIO_CLASSES: Record<number, string> = {
  1.0: 'aspect-square',
  0.5: 'aspect-[2/1]',
  0.6: 'aspect-[5/3]',
  0.75: 'aspect-[4/3]',
  0.8: 'aspect-[5/4]',
  1.33: 'aspect-[3/4]',
  1.5: 'aspect-[2/3]',
  1.77: 'aspect-video', // 16:9
  2.0: 'aspect-[1/2]'
};

/**
 * Generates Tailwind aspect-ratio class for maintaining widget proportions
 * 
 * @param aspectRatio - Desired aspect ratio (height/width)
 * @returns Tailwind CSS aspect-ratio class
 */
export function getAspectRatioClass(aspectRatio?: number): string {
  if (!aspectRatio) return 'aspect-auto';
  
  // Find closest predefined aspect ratio
  const closest = Object.keys(ASPECT_RATIO_CLASSES)
    .map(Number)
    .reduce((prev, curr) => 
      Math.abs(curr - aspectRatio) < Math.abs(prev - aspectRatio) ? curr : prev
    );
  
  // Use predefined class if close enough (within 0.05 tolerance)
  if (Math.abs(closest - aspectRatio) < 0.05) {
    return ASPECT_RATIO_CLASSES[closest];
  }
  
  // Generate custom aspect ratio using arbitrary values
  const ratio = Math.round(aspectRatio * 100) / 100;
  const width = 100;
  const height = Math.round(width * ratio);
  
  return `aspect-[${width}/${height}]`;
}

/**
 * Generates Tailwind height constraint classes
 * Handles min-height, max-height, and fixed height constraints
 * 
 * @param renderConfig - Widget render configuration
 * @returns Tailwind CSS height classes
 */
export function getHeightConstraintClasses(renderConfig: WidgetRenderConfig): string {
  const classes: string[] = [];
  
  if (renderConfig.fixedHeight) {
    // Fixed height takes precedence
    classes.push(`h-[${renderConfig.fixedHeight}px]`);
  } else {
    // Auto height with constraints
    if (renderConfig.autoHeight) {
      classes.push('h-auto');
    }
    
    if (renderConfig.minHeight) {
      classes.push(`min-h-[${renderConfig.minHeight}px]`);
    }
    
    if (renderConfig.maxHeight) {
      classes.push(`max-h-[${renderConfig.maxHeight}px]`);
    }
  }
  
  return classes.join(' ');
}

/**
 * Generates Tailwind overflow classes based on configuration
 * 
 * @param overflow - Overflow behavior ('hidden', 'scroll', 'visible')
 * @returns Tailwind CSS overflow classes
 */
export function getOverflowClasses(overflow: string = 'hidden'): string {
  const overflowMap: Record<string, string> = {
    hidden: 'overflow-hidden',
    scroll: 'overflow-auto',
    visible: 'overflow-visible'
  };
  
  return overflowMap[overflow] || 'overflow-hidden';
}

/**
 * Generates Tailwind padding classes
 * 
 * @param padding - Padding value in pixels
 * @returns Tailwind CSS padding classes
 */
export function getPaddingClasses(padding?: number): string {
  if (!padding) return '';
  
  // Map common padding values to Tailwind classes
  const paddingMap: Record<number, string> = {
    4: 'p-1',
    8: 'p-2',
    12: 'p-3',
    16: 'p-4',
    20: 'p-5',
    24: 'p-6',
    32: 'p-8'
  };
  
  return paddingMap[padding] || `p-[${padding}px]`;
}

/**
 * Generates complete size constraint classes and properties
 * 
 * @param renderConfig - Widget render configuration
 * @returns Complete size constraints object
 */
export function getSizeConstraints(renderConfig: WidgetRenderConfig): SizeConstraints {
  return {
    aspectRatioClass: getAspectRatioClass(renderConfig.aspectRatio),
    heightClasses: getHeightConstraintClasses(renderConfig),
    overflowClasses: getOverflowClasses(renderConfig.overflow),
    paddingClasses: getPaddingClasses(renderConfig.padding),
    customProperties: getSizeCustomProperties(renderConfig)
  };
}

/**
 * Generates CSS custom properties for dynamic sizing
 * Useful for values that can't be handled by static Tailwind classes
 * 
 * @param renderConfig - Widget render configuration
 * @returns CSS custom properties object
 */
export function getSizeCustomProperties(renderConfig: WidgetRenderConfig): Record<string, string> {
  const properties: Record<string, string> = {};
  
  if (renderConfig.aspectRatio) {
    properties['--aspect-ratio'] = String(renderConfig.aspectRatio);
  }
  
  if (renderConfig.minHeight) {
    properties['--min-height'] = `${renderConfig.minHeight}px`;
  }
  
  if (renderConfig.maxHeight) {
    properties['--max-height'] = `${renderConfig.maxHeight}px`;
  }
  
  if (renderConfig.fixedHeight) {
    properties['--fixed-height'] = `${renderConfig.fixedHeight}px`;
  }
  
  if (renderConfig.padding) {
    properties['--padding'] = `${renderConfig.padding}px`;
  }
  
  return properties;
}

/**
 * Generates container query classes for responsive chart sizing
 * Uses modern CSS container queries for component-level responsiveness
 * 
 * @param breakpoints - Container size breakpoints
 * @returns CSS classes with container queries
 */
export function getContainerQueryClasses(breakpoints?: {
  sm?: number;
  md?: number;
  lg?: number;
}): string {
  const defaultBreakpoints = {
    sm: 320,
    md: 480,
    lg: 640,
    ...breakpoints
  };
  
  // Base container query setup
  const baseClasses = '@container';
  
  // Generate responsive classes based on container size
  const responsiveClasses = [
    `@[${defaultBreakpoints.sm}px]:text-sm`,
    `@[${defaultBreakpoints.md}px]:text-base`,
    `@[${defaultBreakpoints.lg}px]:text-lg`
  ].join(' ');
  
  return `${baseClasses} ${responsiveClasses}`;
}

/**
 * Generates CSS Grid minmax() function for flexible sizing within constraints
 * 
 * @param minSize - Minimum size (e.g., '200px', '1fr')
 * @param maxSize - Maximum size (e.g., '400px', '1fr')
 * @returns CSS minmax() function string
 */
export function getMinMaxGridSize(minSize: string, maxSize: string = '1fr'): string {
  return `minmax(${minSize}, ${maxSize})`;
}

/**
 * Generates complete grid template columns with minmax constraints
 * 
 * @param columns - Number of columns
 * @param minColumnWidth - Minimum column width
 * @param maxColumnWidth - Maximum column width
 * @returns CSS grid-template-columns value
 */
export function getFlexibleGridColumns(
  columns: number,
  minColumnWidth: string = '200px',
  maxColumnWidth: string = '1fr'
): string {
  const columnDefinition = getMinMaxGridSize(minColumnWidth, maxColumnWidth);
  return `repeat(${columns}, ${columnDefinition})`;
}

/**
 * Chart-specific size constraints based on chart type
 * Provides optimized sizing for different visualization types
 */
export const CHART_TYPE_CONSTRAINTS: Record<string, Partial<WidgetRenderConfig>> = {
  radar: {
    aspectRatio: 1.0,
    minHeight: 220,
    overflow: 'hidden'
  },
  pie: {
    aspectRatio: 1.0,
    minHeight: 200,
    overflow: 'hidden'
  },
  donut: {
    aspectRatio: 1.0,
    minHeight: 200,
    overflow: 'hidden'
  },
  bar: {
    aspectRatio: 0.6,
    minHeight: 180,
    overflow: 'hidden'
  },
  line: {
    aspectRatio: 0.5,
    minHeight: 160,
    overflow: 'hidden'
  },
  area: {
    aspectRatio: 0.5,
    minHeight: 160,
    overflow: 'hidden'
  },
  column: {
    aspectRatio: 0.6,
    minHeight: 180,
    overflow: 'hidden'
  },
  gauge: {
    aspectRatio: 1.0,
    minHeight: 180,
    overflow: 'hidden'
  },
  heatmap: {
    aspectRatio: 0.8,
    minHeight: 200,
    overflow: 'scroll'
  },
  scatter: {
    aspectRatio: 0.75,
    minHeight: 200,
    overflow: 'hidden'
  }
};

/**
 * Gets optimized render configuration for specific chart type
 * 
 * @param chartType - Type of chart (e.g., 'bar', 'line', 'pie')
 * @param customConfig - Custom configuration to override defaults
 * @returns Optimized render configuration
 */
export function getChartTypeConstraints(
  chartType: string,
  customConfig?: Partial<WidgetRenderConfig>
): WidgetRenderConfig {
  const defaultConstraints = CHART_TYPE_CONSTRAINTS[chartType] || {
    aspectRatio: 0.7,
    minHeight: 200,
    overflow: 'hidden'
  };
  
  return {
    ...defaultConstraints,
    ...customConfig
  };
}