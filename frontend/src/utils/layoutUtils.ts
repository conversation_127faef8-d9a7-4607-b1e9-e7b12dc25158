/**
 * Layout Utilities
 * 
 * Combined utilities for CSS Grid layout and size constraints
 * Provides a unified interface for dashboard component layout management
 */

import { 
  GridPosition, 
  ResponsiveGridConfig, 
  DEFAULT_GRID_CONFIG,
  getGridColumnClass,
  getGridRowClass,
  getResponsiveGridClasses,
  getGridItemClasses,
  getResponsiveColumnSpan,
  validateGridPosition,
  getGridCustomProperties
} from './gridUtils';

import {
  WidgetRenderConfig,
  SizeConstraints,
  getSizeConstraints,
  getAspectRatioClass,
  getHeightConstraintClasses,
  getOverflowClasses,
  getPaddingClasses,
  getContainerQueryClasses,
  getFlexibleGridColumns,
  getChartTypeConstraints,
  CHART_TYPE_CONSTRAINTS
} from './sizeConstraints';

export interface LayoutConfig {
  position: GridPosition;
  renderConfig: WidgetRenderConfig;
  chartType?: string;
  responsive?: boolean;
  debug?: boolean;
}

export interface ComputedLayoutClasses {
  containerClasses: string;
  itemClasses: string;
  contentClasses: string;
  customProperties: Record<string, string>;
}

/**
 * Main layout utility function that combines grid positioning and size constraints
 * 
 * @param config - Complete layout configuration
 * @param totalColumns - Total columns in the grid (default: 12)
 * @param gridConfig - Responsive grid configuration
 * @returns Complete set of CSS classes and properties
 */
export function computeLayoutClasses(
  config: LayoutConfig,
  totalColumns: number = 12,
  gridConfig: ResponsiveGridConfig = DEFAULT_GRID_CONFIG
): ComputedLayoutClasses {
  const { position, renderConfig, chartType, responsive = true, debug = false } = config;
  
  // Validate and normalize position
  const validatedPosition = validateGridPosition(position, totalColumns);
  
  // Get optimized render config for chart type
  const optimizedRenderConfig = chartType 
    ? getChartTypeConstraints(chartType, renderConfig)
    : renderConfig;
  
  // Generate grid positioning classes
  const gridItemClasses = getGridItemClasses(validatedPosition, totalColumns, true);
  
  // Generate size constraint classes
  const sizeConstraints = getSizeConstraints(optimizedRenderConfig);
  
  // Generate responsive classes if enabled
  const responsiveClasses = responsive 
    ? getResponsiveGridClasses(gridConfig)
    : `grid gap-4 grid-cols-${totalColumns}`;
  
  // Generate container query classes
  const containerQueryClasses = getContainerQueryClasses();
  
  // Debug classes
  const debugClasses = debug ? 'grid-debug' : '';
  
  // Combine all custom properties
  const customProperties = {
    ...getGridCustomProperties(validatedPosition, totalColumns),
    ...sizeConstraints.customProperties
  };
  
  return {
    containerClasses: [
      responsiveClasses,
      containerQueryClasses,
      debugClasses,
      'grid-transition'
    ].filter(Boolean).join(' '),
    
    itemClasses: [
      gridItemClasses,
      sizeConstraints.aspectRatioClass,
      sizeConstraints.heightClasses,
      sizeConstraints.overflowClasses,
      sizeConstraints.paddingClasses,
      'grid-item-transition',
      'chart-container'
    ].filter(Boolean).join(' '),
    
    contentClasses: [
      'chart-content',
      'flex-1',
      'min-h-0',
      'overflow-hidden'
    ].join(' '),
    
    customProperties
  };
}

/**
 * Generates layout classes specifically for dashboard grids
 * Optimized for dashboard component rendering
 * 
 * @param widgets - Array of widget configurations
 * @param totalColumns - Total columns in the grid
 * @param gridConfig - Responsive grid configuration
 * @returns Layout classes for each widget
 */
export function computeDashboardLayout(
  widgets: Array<LayoutConfig & { id: string }>,
  totalColumns: number = 12,
  gridConfig: ResponsiveGridConfig = DEFAULT_GRID_CONFIG
): Record<string, ComputedLayoutClasses> {
  const layout: Record<string, ComputedLayoutClasses> = {};
  
  widgets.forEach(widget => {
    layout[widget.id] = computeLayoutClasses(widget, totalColumns, gridConfig);
  });
  
  return layout;
}

/**
 * Generates CSS classes for auto-fit grid layouts
 * Useful for dynamic column counts based on content
 * 
 * @param minColumnWidth - Minimum column width (e.g., '300px')
 * @param gap - Grid gap size
 * @param minRowHeight - Minimum row height (e.g., '200px')
 * @returns CSS classes for auto-fit grid
 */
export function getAutoFitGridClasses(
  minColumnWidth: string = '300px',
  gap: string = '1rem',
  minRowHeight: string = '200px'
): string {
  return [
    'auto-fit-grid',
    'grid',
    `gap-[${gap}]`
  ].join(' ');
}

/**
 * Generates CSS custom properties for auto-fit grids
 * 
 * @param minColumnWidth - Minimum column width
 * @param minRowHeight - Minimum row height
 * @returns CSS custom properties
 */
export function getAutoFitGridProperties(
  minColumnWidth: string = '300px',
  minRowHeight: string = '200px'
): Record<string, string> {
  return {
    '--min-column-width': minColumnWidth,
    '--min-row-height': minRowHeight
  };
}

/**
 * Utility for responsive breakpoint detection
 * 
 * @param width - Current viewport width
 * @param gridConfig - Responsive grid configuration
 * @returns Current breakpoint key
 */
export function getCurrentBreakpoint(
  width: number,
  gridConfig: ResponsiveGridConfig = DEFAULT_GRID_CONFIG
): keyof ResponsiveGridConfig {
  if (width >= (gridConfig.xl.minWidth || 1280)) return 'xl';
  if (width >= (gridConfig.lg.minWidth || 1024)) return 'lg';
  if (width >= (gridConfig.md.minWidth || 768)) return 'md';
  return 'sm';
}

/**
 * Calculates optimal grid dimensions based on widget count and viewport
 * 
 * @param widgetCount - Number of widgets to display
 * @param viewportWidth - Current viewport width
 * @param minColumnWidth - Minimum column width in pixels
 * @returns Optimal grid configuration
 */
export function calculateOptimalGrid(
  widgetCount: number,
  viewportWidth: number,
  minColumnWidth: number = 300
): { columns: number; rows: number } {
  const maxColumns = Math.floor(viewportWidth / minColumnWidth);
  const optimalColumns = Math.min(maxColumns, widgetCount);
  const rows = Math.ceil(widgetCount / optimalColumns);
  
  return {
    columns: Math.max(1, optimalColumns),
    rows: Math.max(1, rows)
  };
}

/**
 * Validates layout configuration for common issues
 * 
 * @param config - Layout configuration to validate
 * @param totalColumns - Total columns in grid
 * @returns Validation result with errors and warnings
 */
export function validateLayoutConfig(
  config: LayoutConfig,
  totalColumns: number = 12
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate position
  const { position, renderConfig } = config;
  
  if (position.x < 0 || position.x >= totalColumns) {
    errors.push(`Invalid x position: ${position.x}. Must be between 0 and ${totalColumns - 1}`);
  }
  
  if (position.w <= 0 || position.w > totalColumns) {
    errors.push(`Invalid width: ${position.w}. Must be between 1 and ${totalColumns}`);
  }
  
  if (position.x + position.w > totalColumns) {
    errors.push(`Widget extends beyond grid: x(${position.x}) + w(${position.w}) > ${totalColumns}`);
  }
  
  if (position.h <= 0) {
    errors.push(`Invalid height: ${position.h}. Must be greater than 0`);
  }
  
  // Validate render config
  if (renderConfig.minHeight && renderConfig.maxHeight && 
      renderConfig.minHeight > renderConfig.maxHeight) {
    errors.push(`minHeight (${renderConfig.minHeight}) cannot be greater than maxHeight (${renderConfig.maxHeight})`);
  }
  
  if (renderConfig.fixedHeight && renderConfig.autoHeight) {
    warnings.push('Both fixedHeight and autoHeight are set. fixedHeight will take precedence.');
  }
  
  if (renderConfig.aspectRatio && renderConfig.aspectRatio <= 0) {
    errors.push(`Invalid aspectRatio: ${renderConfig.aspectRatio}. Must be greater than 0`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// Re-export commonly used utilities
export {
  // Grid utilities
  getGridColumnClass,
  getGridRowClass,
  getResponsiveGridClasses,
  getGridItemClasses,
  getResponsiveColumnSpan,
  validateGridPosition,
  getGridCustomProperties,
  DEFAULT_GRID_CONFIG,
  
  // Size constraint utilities
  getSizeConstraints,
  getAspectRatioClass,
  getHeightConstraintClasses,
  getOverflowClasses,
  getPaddingClasses,
  getContainerQueryClasses,
  getFlexibleGridColumns,
  getChartTypeConstraints,
  CHART_TYPE_CONSTRAINTS,
  
  // Types
  type GridPosition,
  type ResponsiveGridConfig,
  type WidgetRenderConfig,
  type SizeConstraints
};