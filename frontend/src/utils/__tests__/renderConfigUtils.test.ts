import { 
  getRenderConfigClasses, 
  getDefaultRenderConfig, 
  mergeRenderConfig, 
  validateRenderConfig,
  getRenderConfigCustomProperties
} from '../renderConfigUtils';
import { WidgetRenderConfig } from '../../types/dashboard';

describe('Render Configuration Utilities', () => {
  describe('getRenderConfigClasses', () => {
    it('should handle empty config', () => {
      const result = getRenderConfigClasses();
      
      expect(result.classes).toContain('overflow-hidden');
      expect(result.classes).toContain('h-auto');
      expect(result.customProperties).toBeDefined();
    });

    it('should generate correct classes for aspect ratio 1.0 (square)', () => {
      const config: WidgetRenderConfig = { aspectRatio: 1.0 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-square');
      expect(result.customProperties['--aspect-ratio']).toBe('1');
    });

    it('should generate correct classes for aspect ratio 16:9 (video)', () => {
      const config: WidgetRenderConfig = { aspectRatio: 16/9 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-video');
      expect(result.customProperties['--aspect-ratio']).toBe(String(16/9));
    });

    it('should generate correct classes for aspect ratio 4:3', () => {
      const config: WidgetRenderConfig = { aspectRatio: 4/3 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-[4/3]');
    });

    it('should generate correct classes for aspect ratio 3:2', () => {
      const config: WidgetRenderConfig = { aspectRatio: 3/2 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-[3/2]');
    });

    it('should generate correct classes for aspect ratio 5:4', () => {
      const config: WidgetRenderConfig = { aspectRatio: 5/4 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-[5/4]');
    });

    it('should generate correct classes for aspect ratio 2:1', () => {
      const config: WidgetRenderConfig = { aspectRatio: 2/1 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-[2/1]');
    });

    it('should generate correct classes for aspect ratio 5:3', () => {
      const config: WidgetRenderConfig = { aspectRatio: 5/3 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-[5/3]');
    });

    it('should generate custom aspect ratio for non-standard values', () => {
      const config: WidgetRenderConfig = { aspectRatio: 1.25 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-[5/4]'); // 1.25 is close to 5/4 (1.25)
      expect(result.customProperties['--aspect-ratio']).toBe('1.25');
    });

    it('should generate correct classes for fixed height with Tailwind values', () => {
      const config: WidgetRenderConfig = { fixedHeight: 200 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('h-50'); // 200px / 4 = 50
      expect(result.customProperties['--fixed-height']).toBe('200px');
    });

    it('should generate correct classes for fixed height with arbitrary values', () => {
      const config: WidgetRenderConfig = { fixedHeight: 250 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('h-[250px]');
      expect(result.customProperties['--fixed-height']).toBe('250px');
    });

    it('should generate correct classes for min height with Tailwind values', () => {
      const config: WidgetRenderConfig = { minHeight: 100 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('min-h-25'); // 100px / 4 = 25
      expect(result.customProperties['--min-height']).toBe('100px');
    });

    it('should generate correct classes for min height with arbitrary values', () => {
      const config: WidgetRenderConfig = { minHeight: 150 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('min-h-[150px]');
      expect(result.customProperties['--min-height']).toBe('150px');
    });

    it('should generate correct classes for max height with Tailwind values', () => {
      const config: WidgetRenderConfig = { maxHeight: 300 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('max-h-75'); // 300px / 4 = 75
      expect(result.customProperties['--max-height']).toBe('300px');
    });

    it('should generate correct classes for max height with arbitrary values', () => {
      const config: WidgetRenderConfig = { maxHeight: 350 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('max-h-[350px]');
      expect(result.customProperties['--max-height']).toBe('350px');
    });

    it('should handle auto height', () => {
      const config: WidgetRenderConfig = { autoHeight: true };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('h-auto');
      expect(result.customProperties['--auto-height']).toBe('true');
    });

    it('should handle auto height with constraints', () => {
      const config: WidgetRenderConfig = { 
        autoHeight: true, 
        minHeight: 150,
        maxHeight: 400
      };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('h-auto');
      expect(result.classes).toContain('min-h-[150px]');
      expect(result.classes).toContain('max-h-[400px]');
    });

    it('should handle overflow hidden', () => {
      const config: WidgetRenderConfig = { overflow: 'hidden' };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('overflow-hidden');
    });

    it('should handle overflow scroll', () => {
      const config: WidgetRenderConfig = { overflow: 'scroll' };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('overflow-auto');
    });

    it('should handle overflow visible', () => {
      const config: WidgetRenderConfig = { overflow: 'visible' };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('overflow-visible');
    });

    it('should default to overflow hidden for invalid values', () => {
      const config: WidgetRenderConfig = { overflow: 'invalid' as any };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('overflow-hidden');
    });

    it('should handle padding with Tailwind values', () => {
      const config: WidgetRenderConfig = { padding: 16 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('p-4'); // 16px / 4 = 4
      expect(result.customProperties['--padding']).toBe('16px');
    });

    it('should handle padding with arbitrary values', () => {
      const config: WidgetRenderConfig = { padding: 20 };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('p-5'); // 20px / 4 = 5
      expect(result.customProperties['--padding']).toBe('20px');
    });

    it('should combine multiple configuration options', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 1.0,
        minHeight: 200,
        maxHeight: 400,
        overflow: 'hidden',
        padding: 16
      };
      const result = getRenderConfigClasses(config);
      
      expect(result.classes).toContain('aspect-square');
      expect(result.classes).toContain('min-h-50'); // 200px / 4 = 50
      expect(result.classes).toContain('max-h-[400px]');
      expect(result.classes).toContain('overflow-hidden');
      expect(result.classes).toContain('p-4');
      
      expect(result.customProperties['--aspect-ratio']).toBe('1');
      expect(result.customProperties['--min-height']).toBe('200px');
      expect(result.customProperties['--max-height']).toBe('400px');
      expect(result.customProperties['--padding']).toBe('16px');
    });
  });

  describe('getDefaultRenderConfig', () => {
    it('should return correct defaults for radar chart', () => {
      const config = getDefaultRenderConfig('radar');
      
      expect(config.aspectRatio).toBe(1.0);
      expect(config.minHeight).toBe(220);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for pie chart', () => {
      const config = getDefaultRenderConfig('pie');
      
      expect(config.aspectRatio).toBe(1.0);
      expect(config.minHeight).toBe(200);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for donut chart', () => {
      const config = getDefaultRenderConfig('donut');
      
      expect(config.aspectRatio).toBe(1.0);
      expect(config.minHeight).toBe(200);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for bar chart', () => {
      const config = getDefaultRenderConfig('bar');
      
      expect(config.aspectRatio).toBe(0.6);
      expect(config.minHeight).toBe(180);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for line chart', () => {
      const config = getDefaultRenderConfig('line');
      
      expect(config.aspectRatio).toBe(0.5);
      expect(config.minHeight).toBe(160);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for area chart', () => {
      const config = getDefaultRenderConfig('area');
      
      expect(config.aspectRatio).toBe(0.5);
      expect(config.minHeight).toBe(160);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for column chart', () => {
      const config = getDefaultRenderConfig('column');
      
      expect(config.aspectRatio).toBe(0.6);
      expect(config.minHeight).toBe(180);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for gauge chart', () => {
      const config = getDefaultRenderConfig('gauge');
      
      expect(config.aspectRatio).toBe(1.0);
      expect(config.minHeight).toBe(180);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for heatmap chart', () => {
      const config = getDefaultRenderConfig('heatmap');
      
      expect(config.aspectRatio).toBe(0.8);
      expect(config.minHeight).toBe(200);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for scatter chart', () => {
      const config = getDefaultRenderConfig('scatter');
      
      expect(config.aspectRatio).toBe(0.7);
      expect(config.minHeight).toBe(180);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for bubble chart', () => {
      const config = getDefaultRenderConfig('bubble');
      
      expect(config.aspectRatio).toBe(0.7);
      expect(config.minHeight).toBe(180);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for treemap chart', () => {
      const config = getDefaultRenderConfig('treemap');
      
      expect(config.aspectRatio).toBe(0.8);
      expect(config.minHeight).toBe(200);
      expect(config.autoHeight).toBe(false);
    });

    it('should return correct defaults for boxplot chart', () => {
      const config = getDefaultRenderConfig('boxplot');
      
      expect(config.aspectRatio).toBe(0.6);
      expect(config.minHeight).toBe(180);
      expect(config.autoHeight).toBe(false);
    });

    it('should return fallback defaults for unknown chart type', () => {
      const config = getDefaultRenderConfig('unknown');
      
      expect(config.aspectRatio).toBe(0.7);
      expect(config.minHeight).toBe(200);
      expect(config.autoHeight).toBe(false);
      expect(config.overflow).toBe('hidden');
    });

    it('should return fallback defaults for empty string', () => {
      const config = getDefaultRenderConfig('');
      
      expect(config.aspectRatio).toBe(0.7);
      expect(config.minHeight).toBe(200);
      expect(config.autoHeight).toBe(false);
      expect(config.overflow).toBe('hidden');
    });
  });

  describe('mergeRenderConfig', () => {
    it('should merge widget config with defaults', () => {
      const widgetConfig: WidgetRenderConfig = { 
        aspectRatio: 2.0,
        fixedHeight: 250 
      };
      
      const merged = mergeRenderConfig('pie', widgetConfig);
      
      expect(merged.aspectRatio).toBe(2.0); // Widget override
      expect(merged.fixedHeight).toBe(250); // Widget override
      expect(merged.minHeight).toBe(200); // Default value
      expect(merged.autoHeight).toBe(false); // Default value
    });

    it('should use defaults when widget config is empty', () => {
      const merged = mergeRenderConfig('bar', {});
      
      expect(merged.aspectRatio).toBe(0.6);
      expect(merged.minHeight).toBe(180);
      expect(merged.autoHeight).toBe(false);
    });

    it('should handle undefined widget config', () => {
      const merged = mergeRenderConfig('line');
      
      expect(merged.aspectRatio).toBe(0.5);
      expect(merged.minHeight).toBe(160);
      expect(merged.autoHeight).toBe(false);
    });

    it('should preserve all widget config properties', () => {
      const widgetConfig: WidgetRenderConfig = {
        aspectRatio: 1.5,
        minHeight: 250,
        maxHeight: 500,
        fixedHeight: 300,
        autoHeight: true,
        overflow: 'scroll',
        padding: 20
      };
      
      const merged = mergeRenderConfig('gauge', widgetConfig);
      
      expect(merged).toEqual(widgetConfig);
    });
  });

  describe('validateRenderConfig', () => {
    it('should return unchanged config when valid', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 1.0,
        minHeight: 200,
        maxHeight: 400,
        padding: 16,
        overflow: 'hidden'
      };
      
      const validated = validateRenderConfig(config);
      
      expect(validated).toEqual(config);
    });

    it('should clamp aspect ratio to minimum', () => {
      const config: WidgetRenderConfig = { aspectRatio: 0.05 };
      const validated = validateRenderConfig(config);
      
      expect(validated.aspectRatio).toBe(0.1);
    });

    it('should clamp aspect ratio to maximum', () => {
      const config: WidgetRenderConfig = { aspectRatio: 15.0 };
      const validated = validateRenderConfig(config);
      
      expect(validated.aspectRatio).toBe(10);
    });

    it('should clamp min height to minimum', () => {
      const config: WidgetRenderConfig = { minHeight: 30 };
      const validated = validateRenderConfig(config);
      
      expect(validated.minHeight).toBe(50);
    });

    it('should clamp min height to maximum', () => {
      const config: WidgetRenderConfig = { minHeight: 1500 };
      const validated = validateRenderConfig(config);
      
      expect(validated.minHeight).toBe(1000);
    });

    it('should clamp max height to minimum', () => {
      const config: WidgetRenderConfig = { maxHeight: 50 };
      const validated = validateRenderConfig(config);
      
      expect(validated.maxHeight).toBe(100);
    });

    it('should clamp max height to maximum', () => {
      const config: WidgetRenderConfig = { maxHeight: 3000 };
      const validated = validateRenderConfig(config);
      
      expect(validated.maxHeight).toBe(2000);
    });

    it('should clamp fixed height to minimum', () => {
      const config: WidgetRenderConfig = { fixedHeight: 30 };
      const validated = validateRenderConfig(config);
      
      expect(validated.fixedHeight).toBe(50);
    });

    it('should clamp fixed height to maximum', () => {
      const config: WidgetRenderConfig = { fixedHeight: 3000 };
      const validated = validateRenderConfig(config);
      
      expect(validated.fixedHeight).toBe(2000);
    });

    it('should ensure min height is less than max height', () => {
      const config: WidgetRenderConfig = { 
        minHeight: 300, 
        maxHeight: 200 
      };
      const validated = validateRenderConfig(config);
      
      expect(validated.minHeight).toBe(300);
      expect(validated.maxHeight).toBe(350); // minHeight + 50
    });

    it('should clamp padding to minimum', () => {
      const config: WidgetRenderConfig = { padding: -5 };
      const validated = validateRenderConfig(config);
      
      expect(validated.padding).toBe(0);
    });

    it('should clamp padding to maximum', () => {
      const config: WidgetRenderConfig = { padding: 100 };
      const validated = validateRenderConfig(config);
      
      expect(validated.padding).toBe(64);
    });

    it('should validate overflow values', () => {
      const config: WidgetRenderConfig = { 
        overflow: 'invalid' as any 
      };
      const validated = validateRenderConfig(config);
      
      expect(validated.overflow).toBe('hidden');
    });

    it('should preserve valid overflow values', () => {
      const validOverflows: Array<'hidden' | 'scroll' | 'visible'> = ['hidden', 'scroll', 'visible'];
      
      validOverflows.forEach(overflow => {
        const config: WidgetRenderConfig = { overflow };
        const validated = validateRenderConfig(config);
        
        expect(validated.overflow).toBe(overflow);
      });
    });

    it('should handle undefined values gracefully', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: undefined,
        minHeight: undefined,
        maxHeight: undefined,
        fixedHeight: undefined,
        padding: undefined,
        overflow: undefined
      };
      
      const validated = validateRenderConfig(config);
      
      expect(validated.aspectRatio).toBeUndefined();
      expect(validated.minHeight).toBeUndefined();
      expect(validated.maxHeight).toBeUndefined();
      expect(validated.fixedHeight).toBeUndefined();
      expect(validated.padding).toBeUndefined();
      expect(validated.overflow).toBeUndefined();
    });
  });

  describe('getRenderConfigCustomProperties', () => {
    it('should generate custom properties for all config values', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 1.5,
        minHeight: 200,
        maxHeight: 400,
        fixedHeight: 300,
        autoHeight: true,
        overflow: 'scroll',
        padding: 16
      };
      
      const properties = getRenderConfigCustomProperties(config);
      
      expect(properties).toEqual({
        '--widget-aspect-ratio': '1.5',
        '--widget-min-height': '200px',
        '--widget-max-height': '400px',
        '--widget-fixed-height': '300px',
        '--widget-padding': '16px',
        '--widget-auto-height': 'true',
        '--widget-overflow': 'scroll'
      });
    });

    it('should handle partial config', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 1.0,
        minHeight: 150
      };
      
      const properties = getRenderConfigCustomProperties(config);
      
      expect(properties).toEqual({
        '--widget-aspect-ratio': '1',
        '--widget-min-height': '150px',
        '--widget-auto-height': 'false',
        '--widget-overflow': 'hidden'
      });
    });

    it('should handle empty config', () => {
      const properties = getRenderConfigCustomProperties({});
      
      expect(properties).toEqual({
        '--widget-auto-height': 'false',
        '--widget-overflow': 'hidden'
      });
    });

    it('should handle autoHeight false', () => {
      const config: WidgetRenderConfig = { autoHeight: false };
      const properties = getRenderConfigCustomProperties(config);
      
      expect(properties['--widget-auto-height']).toBe('false');
    });

    it('should default overflow to hidden when not specified', () => {
      const config: WidgetRenderConfig = { aspectRatio: 1.0 };
      const properties = getRenderConfigCustomProperties(config);
      
      expect(properties['--widget-overflow']).toBe('hidden');
    });
  });
});