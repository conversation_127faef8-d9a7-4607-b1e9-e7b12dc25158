/**
 * Grid Utils Tests
 * 
 * Comprehensive tests for Tailwind CSS Grid utility functions
 */

import {
  GridPosition,
  ResponsiveGridConfig,
  DEFAULT_GRID_CONFIG,
  getGridColumnClass,
  getGridRowClass,
  getResponsiveGridClasses,
  getGridItemClasses,
  getResponsiveColumnSpan,
  getMobileFirstColumnClasses,
  getMobileFirstPaddingClasses,
  getMobileFirstContainerClasses,
  validateGridPosition,
  getGridCustomProperties
} from '../gridUtils';

describe('Grid Utils', () => {
  describe('getGridColumnClass', () => {
    it('should generate correct column span for position at origin', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: 2 };
      const result = getGridColumnClass(position, 12);
      expect(result).toBe('col-span-4');
    });

    it('should generate column start and span classes for offset position', () => {
      const position: GridPosition = { x: 2, y: 0, w: 4, h: 2 };
      const result = getGridColumnClass(position, 12);
      expect(result).toBe('col-start-3 col-span-4');
    });

    it('should handle edge case at grid boundary', () => {
      const position: GridPosition = { x: 11, y: 0, w: 2, h: 1 };
      const result = getGridColumnClass(position, 12);
      expect(result).toBe('col-start-12 col-span-1');
    });

    it('should clamp width to available space', () => {
      const position: GridPosition = { x: 10, y: 0, w: 5, h: 1 };
      const result = getGridColumnClass(position, 12);
      expect(result).toBe('col-start-11 col-span-2');
    });

    it('should handle negative x position', () => {
      const position: GridPosition = { x: -1, y: 0, w: 4, h: 1 };
      const result = getGridColumnClass(position, 12);
      expect(result).toBe('col-span-4');
    });

    it('should handle zero width', () => {
      const position: GridPosition = { x: 0, y: 0, w: 0, h: 1 };
      const result = getGridColumnClass(position, 12);
      expect(result).toBe('col-span-1');
    });

    it('should work with different total column counts', () => {
      const position: GridPosition = { x: 0, y: 0, w: 3, h: 1 };
      const result = getGridColumnClass(position, 6);
      expect(result).toBe('col-span-3');
    });
  });

  describe('getGridRowClass', () => {
    it('should generate row span class for multi-row components', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: 3 };
      const result = getGridRowClass(position);
      expect(result).toBe('row-span-3');
    });

    it('should return empty string for single row', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: 1 };
      const result = getGridRowClass(position);
      expect(result).toBe('');
    });

    it('should limit row span to maximum', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: 10 };
      const result = getGridRowClass(position, 6);
      expect(result).toBe('row-span-6');
    });

    it('should handle zero height', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: 0 };
      const result = getGridRowClass(position);
      expect(result).toBe('');
    });

    it('should handle negative height', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: -1 };
      const result = getGridRowClass(position);
      expect(result).toBe('');
    });
  });

  describe('getResponsiveGridClasses', () => {
    it('should generate responsive grid classes with default config', () => {
      const result = getResponsiveGridClasses();
      
      expect(result).toContain('grid');
      expect(result).toContain('w-full');
      expect(result).toContain('max-w-none');
      expect(result).toContain('overflow-hidden');
      expect(result).toContain('min-h-0');
      expect(result).toContain('gap-2');
      expect(result).toContain('sm:gap-3');
      expect(result).toContain('lg:gap-4');
      expect(result).toContain('px-4');
      expect(result).toContain('sm:px-6');
      expect(result).toContain('lg:px-8');
      expect(result).toContain('transition-all');
      expect(result).toContain('duration-300');
      expect(result).toContain('ease-in-out');
      expect(result).toContain('grid-cols-1');
      expect(result).toContain('sm:grid-cols-1');
      expect(result).toContain('md:grid-cols-2');
      expect(result).toContain('lg:grid-cols-3');
      expect(result).toContain('xl:grid-cols-4');
    });

    it('should generate responsive grid classes with custom config', () => {
      const customConfig: ResponsiveGridConfig = {
        sm: { columns: 1, minWidth: 640 },
        md: { columns: 3, minWidth: 768 },
        lg: { columns: 5, minWidth: 1024 },
        xl: { columns: 6, minWidth: 1280 }
      };
      
      const result = getResponsiveGridClasses(customConfig);
      
      expect(result).toContain('md:grid-cols-3');
      expect(result).toContain('lg:grid-cols-5');
      expect(result).toContain('xl:grid-cols-6');
    });

    it('should include all required base classes', () => {
      const result = getResponsiveGridClasses();
      
      const requiredClasses = [
        'grid',
        'w-full',
        'max-w-none',
        'overflow-hidden',
        'min-h-0',
        'transition-all',
        'duration-300',
        'ease-in-out'
      ];
      
      requiredClasses.forEach(className => {
        expect(result).toContain(className);
      });
    });
  });

  describe('getGridItemClasses', () => {
    it('should combine position and base classes', () => {
      const position: GridPosition = { x: 2, y: 1, w: 4, h: 2 };
      const result = getGridItemClasses(position, 12, true);
      
      expect(result).toContain('min-h-0');
      expect(result).toContain('overflow-hidden');
      expect(result).toContain('col-start-3');
      expect(result).toContain('col-span-4');
      expect(result).toContain('row-span-2');
    });

    it('should exclude row span when requested', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: 3 };
      const result = getGridItemClasses(position, 12, false);
      
      expect(result).toContain('col-span-4');
      expect(result).not.toContain('row-span-3');
    });

    it('should handle single row without row span', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: 1 };
      const result = getGridItemClasses(position, 12, true);
      
      expect(result).toContain('col-span-4');
      expect(result).not.toContain('row-span');
    });
  });

  describe('getResponsiveColumnSpan', () => {
    it('should scale column span for different breakpoints', () => {
      expect(getResponsiveColumnSpan(12, 'sm', 12)).toBe(12);
      expect(getResponsiveColumnSpan(12, 'md', 12)).toBe(10); // 12 * 0.8 = 9.6, ceil = 10
      expect(getResponsiveColumnSpan(12, 'lg', 12)).toBe(9);  // 12 * 0.7 = 8.4, ceil = 9
      expect(getResponsiveColumnSpan(12, 'xl', 12)).toBe(8);  // 12 * 0.6 = 7.2, ceil = 8
    });

    it('should respect maximum columns constraint', () => {
      expect(getResponsiveColumnSpan(10, 'sm', 6)).toBe(6);
      expect(getResponsiveColumnSpan(10, 'md', 4)).toBe(4);
    });

    it('should ensure minimum span of 1', () => {
      expect(getResponsiveColumnSpan(1, 'xl', 12)).toBe(1);
      expect(getResponsiveColumnSpan(0, 'lg', 12)).toBe(1);
    });
  });

  describe('getMobileFirstColumnClasses', () => {
    it('should generate mobile-first responsive column classes', () => {
      const result = getMobileFirstColumnClasses(6);
      
      expect(result).toBe('col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3');
    });

    it('should handle large column spans', () => {
      const result = getMobileFirstColumnClasses(12);
      
      expect(result).toBe('col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3');
    });

    it('should handle small column spans', () => {
      const result = getMobileFirstColumnClasses(2);
      
      expect(result).toBe('col-span-12 md:col-span-2 lg:col-span-2 xl:col-span-2');
    });

    it('should use default span when not provided', () => {
      const result = getMobileFirstColumnClasses();
      
      expect(result).toBe('col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3');
    });
  });

  describe('getMobileFirstPaddingClasses', () => {
    it('should return correct responsive padding classes', () => {
      const result = getMobileFirstPaddingClasses();
      
      expect(result).toBe('p-2 sm:p-3 lg:p-4');
    });
  });

  describe('getMobileFirstContainerClasses', () => {
    it('should return correct container classes', () => {
      const result = getMobileFirstContainerClasses();
      
      expect(result).toBe('w-full max-w-none overflow-hidden min-h-0');
    });
  });

  describe('validateGridPosition', () => {
    it('should return valid position unchanged', () => {
      const position: GridPosition = { x: 2, y: 1, w: 4, h: 2 };
      const result = validateGridPosition(position, 12);
      
      expect(result).toEqual(position);
    });

    it('should clamp x position to grid boundaries', () => {
      const position: GridPosition = { x: -1, y: 0, w: 4, h: 2 };
      const result = validateGridPosition(position, 12);
      
      expect(result.x).toBe(0);
      expect(result.w).toBe(4);
    });

    it('should clamp x position to maximum valid value', () => {
      const position: GridPosition = { x: 15, y: 0, w: 4, h: 2 };
      const result = validateGridPosition(position, 12);
      
      expect(result.x).toBe(11);
      expect(result.w).toBe(1);
    });

    it('should adjust width to fit within grid', () => {
      const position: GridPosition = { x: 10, y: 0, w: 5, h: 2 };
      const result = validateGridPosition(position, 12);
      
      expect(result.x).toBe(10);
      expect(result.w).toBe(2);
    });

    it('should ensure minimum width of 1', () => {
      const position: GridPosition = { x: 0, y: 0, w: 0, h: 2 };
      const result = validateGridPosition(position, 12);
      
      expect(result.w).toBe(1);
    });

    it('should validate row constraints when totalRows provided', () => {
      const position: GridPosition = { x: 0, y: 5, w: 4, h: 3 };
      const result = validateGridPosition(position, 12, 6);
      
      expect(result.y).toBe(5);
      expect(result.h).toBe(1);
    });

    it('should clamp y position when totalRows provided', () => {
      const position: GridPosition = { x: 0, y: -1, w: 4, h: 2 };
      const result = validateGridPosition(position, 12, 6);
      
      expect(result.y).toBe(0);
      expect(result.h).toBe(2);
    });

    it('should ensure minimum height of 1', () => {
      const position: GridPosition = { x: 0, y: 0, w: 4, h: 0 };
      const result = validateGridPosition(position, 12, 6);
      
      expect(result.h).toBe(1);
    });
  });

  describe('getGridCustomProperties', () => {
    it('should generate correct CSS custom properties', () => {
      const position: GridPosition = { x: 2, y: 1, w: 4, h: 2 };
      const result = getGridCustomProperties(position, 12);
      
      expect(result).toEqual({
        '--grid-column-start': '3',
        '--grid-column-end': '7',
        '--grid-row-start': '2',
        '--grid-row-end': '4',
        '--grid-total-columns': '12'
      });
    });

    it('should handle position at origin', () => {
      const position: GridPosition = { x: 0, y: 0, w: 1, h: 1 };
      const result = getGridCustomProperties(position, 12);
      
      expect(result).toEqual({
        '--grid-column-start': '1',
        '--grid-column-end': '2',
        '--grid-row-start': '1',
        '--grid-row-end': '2',
        '--grid-total-columns': '12'
      });
    });

    it('should work with different total columns', () => {
      const position: GridPosition = { x: 0, y: 0, w: 3, h: 1 };
      const result = getGridCustomProperties(position, 6);
      
      expect(result['--grid-total-columns']).toBe('6');
      expect(result['--grid-column-end']).toBe('4');
    });
  });

  describe('DEFAULT_GRID_CONFIG', () => {
    it('should have correct default breakpoint configuration', () => {
      expect(DEFAULT_GRID_CONFIG).toEqual({
        sm: { columns: 1, minWidth: 640 },
        md: { columns: 2, minWidth: 768 },
        lg: { columns: 3, minWidth: 1024 },
        xl: { columns: 4, minWidth: 1280 }
      });
    });

    it('should have all required breakpoints', () => {
      const requiredBreakpoints = ['sm', 'md', 'lg', 'xl'];
      
      requiredBreakpoints.forEach(bp => {
        expect(DEFAULT_GRID_CONFIG).toHaveProperty(bp);
        expect(DEFAULT_GRID_CONFIG[bp as keyof ResponsiveGridConfig]).toHaveProperty('columns');
        expect(DEFAULT_GRID_CONFIG[bp as keyof ResponsiveGridConfig]).toHaveProperty('minWidth');
      });
    });
  });
});