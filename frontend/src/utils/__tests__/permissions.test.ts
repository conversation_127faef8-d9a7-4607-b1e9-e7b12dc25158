import {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  hasRole,
  isAdmin,
  isSuperAdmin,
  getRoleDisplayName,
  getPermissionDisplayName,
  getRolePermissions,
  canAccessOrganization,
} from '../permissions';
import { User } from '../../types/auth';

const mockUsers: Record<string, User> = {
  regular: {
    id: '1',
    email: '<EMAIL>',
    role: 'regular',
    organization: 'test-org',
    created_at: '2023-01-01T00:00:00Z',
  },
  orgAdmin: {
    id: '2',
    email: '<EMAIL>',
    role: 'org_admin',
    organization: 'test-org',
    created_at: '2023-01-01T00:00:00Z',
  },
  superAdmin: {
    id: '3',
    email: '<EMAIL>',
    role: 'super_admin',
    created_at: '2023-01-01T00:00:00Z',
  },
};

describe('Permission Utilities', () => {
  describe('hasPermission', () => {
    it('should return true when user has the required permission', () => {
      expect(hasPermission(mockUsers.regular, 'view_dashboard')).toBe(true);
      expect(hasPermission(mockUsers.orgAdmin, 'manage_users')).toBe(true);
      expect(hasPermission(mockUsers.superAdmin, 'manage_system')).toBe(true);
    });

    it('should return false when user does not have the required permission', () => {
      expect(hasPermission(mockUsers.regular, 'manage_users')).toBe(false);
      expect(hasPermission(mockUsers.orgAdmin, 'manage_system')).toBe(false);
    });

    it('should return false for null user', () => {
      expect(hasPermission(null, 'view_dashboard')).toBe(false);
    });
  });

  describe('hasAnyPermission', () => {
    it('should return true when user has at least one of the permissions', () => {
      expect(hasAnyPermission(mockUsers.orgAdmin, ['manage_users', 'manage_system'])).toBe(true);
      expect(hasAnyPermission(mockUsers.regular, ['view_dashboard', 'manage_users'])).toBe(true);
    });

    it('should return false when user has none of the permissions', () => {
      expect(hasAnyPermission(mockUsers.regular, ['manage_users', 'manage_system'])).toBe(false);
    });

    it('should return false for null user', () => {
      expect(hasAnyPermission(null, ['view_dashboard'])).toBe(false);
    });
  });

  describe('hasAllPermissions', () => {
    it('should return true when user has all required permissions', () => {
      expect(hasAllPermissions(mockUsers.superAdmin, ['view_dashboard', 'manage_system'])).toBe(true);
      expect(hasAllPermissions(mockUsers.orgAdmin, ['view_dashboard', 'manage_users'])).toBe(true);
    });

    it('should return false when user is missing some permissions', () => {
      expect(hasAllPermissions(mockUsers.regular, ['view_dashboard', 'manage_users'])).toBe(false);
      expect(hasAllPermissions(mockUsers.orgAdmin, ['manage_users', 'manage_system'])).toBe(false);
    });

    it('should return false for null user', () => {
      expect(hasAllPermissions(null, ['view_dashboard'])).toBe(false);
    });
  });

  describe('hasRole', () => {
    it('should return true when user has the exact role', () => {
      expect(hasRole(mockUsers.regular, 'regular')).toBe(true);
      expect(hasRole(mockUsers.orgAdmin, 'org_admin')).toBe(true);
      expect(hasRole(mockUsers.superAdmin, 'super_admin')).toBe(true);
    });

    it('should return true when user has higher role', () => {
      expect(hasRole(mockUsers.orgAdmin, 'regular')).toBe(true);
      expect(hasRole(mockUsers.superAdmin, 'regular')).toBe(true);
      expect(hasRole(mockUsers.superAdmin, 'org_admin')).toBe(true);
    });

    it('should return false when user has lower role', () => {
      expect(hasRole(mockUsers.regular, 'org_admin')).toBe(false);
      expect(hasRole(mockUsers.regular, 'super_admin')).toBe(false);
      expect(hasRole(mockUsers.orgAdmin, 'super_admin')).toBe(false);
    });

    it('should return false for null user', () => {
      expect(hasRole(null, 'regular')).toBe(false);
    });
  });

  describe('isAdmin', () => {
    it('should return true for admin users', () => {
      expect(isAdmin(mockUsers.orgAdmin)).toBe(true);
      expect(isAdmin(mockUsers.superAdmin)).toBe(true);
    });

    it('should return false for regular users', () => {
      expect(isAdmin(mockUsers.regular)).toBe(false);
    });

    it('should return false for null user', () => {
      expect(isAdmin(null)).toBe(false);
    });
  });

  describe('isSuperAdmin', () => {
    it('should return true only for super admin', () => {
      expect(isSuperAdmin(mockUsers.superAdmin)).toBe(true);
    });

    it('should return false for other roles', () => {
      expect(isSuperAdmin(mockUsers.regular)).toBe(false);
      expect(isSuperAdmin(mockUsers.orgAdmin)).toBe(false);
    });

    it('should return false for null user', () => {
      expect(isSuperAdmin(null)).toBe(false);
    });
  });

  describe('getRoleDisplayName', () => {
    it('should return correct display names for roles', () => {
      expect(getRoleDisplayName('regular')).toBe('일반 사용자');
      expect(getRoleDisplayName('org_admin')).toBe('조직 관리자');
      expect(getRoleDisplayName('super_admin')).toBe('운영 관리자');
    });
  });

  describe('getPermissionDisplayName', () => {
    it('should return correct display names for permissions', () => {
      expect(getPermissionDisplayName('view_dashboard')).toBe('대시보드 조회');
      expect(getPermissionDisplayName('manage_users')).toBe('사용자 관리');
      expect(getPermissionDisplayName('manage_system')).toBe('시스템 관리');
    });
  });

  describe('getRolePermissions', () => {
    it('should return correct permissions for each role', () => {
      const regularPermissions = getRolePermissions('regular');
      expect(regularPermissions).toContain('view_dashboard');
      expect(regularPermissions).not.toContain('manage_users');

      const orgAdminPermissions = getRolePermissions('org_admin');
      expect(orgAdminPermissions).toContain('view_dashboard');
      expect(orgAdminPermissions).toContain('manage_users');
      expect(orgAdminPermissions).not.toContain('manage_system');

      const superAdminPermissions = getRolePermissions('super_admin');
      expect(superAdminPermissions).toContain('view_dashboard');
      expect(superAdminPermissions).toContain('manage_users');
      expect(superAdminPermissions).toContain('manage_system');
    });

    it('should return empty array for invalid role', () => {
      expect(getRolePermissions('invalid' as any)).toEqual([]);
    });
  });

  describe('canAccessOrganization', () => {
    it('should return true for super admin accessing any organization', () => {
      expect(canAccessOrganization(mockUsers.superAdmin, 'any-org')).toBe(true);
      expect(canAccessOrganization(mockUsers.superAdmin, 'test-org')).toBe(true);
    });

    it('should return true when user organization matches', () => {
      expect(canAccessOrganization(mockUsers.regular, 'test-org')).toBe(true);
      expect(canAccessOrganization(mockUsers.orgAdmin, 'test-org')).toBe(true);
    });

    it('should return false when user organization does not match', () => {
      expect(canAccessOrganization(mockUsers.regular, 'different-org')).toBe(false);
      expect(canAccessOrganization(mockUsers.orgAdmin, 'different-org')).toBe(false);
    });

    it('should return false for null user', () => {
      expect(canAccessOrganization(null, 'any-org')).toBe(false);
    });

    it('should handle undefined organization', () => {
      expect(canAccessOrganization(mockUsers.superAdmin, undefined)).toBe(true);
      expect(canAccessOrganization(mockUsers.regular, undefined)).toBe(false);
    });
  });
});