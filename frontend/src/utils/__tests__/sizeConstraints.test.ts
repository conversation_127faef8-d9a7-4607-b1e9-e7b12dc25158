/**
 * Size Constraints Tests
 * 
 * Comprehensive tests for CSS-based size constraint utilities
 */

import {
  WidgetRenderConfig,
  SizeConstraints,
  getAspectRatioClass,
  getHeightConstraintClasses,
  getOverflowClasses,
  getPaddingClasses,
  getSizeConstraints,
  getSizeCustomProperties,
  getContainerQueryClasses,
  getMinMaxGridSize,
  getFlexibleGridColumns,
  getChartTypeConstraints,
  CHART_TYPE_CONSTRAINTS
} from '../sizeConstraints';

describe('Size Constraints Utils', () => {
  describe('getAspectRatioClass', () => {
    it('should return aspect-auto for undefined ratio', () => {
      expect(getAspectRatioClass()).toBe('aspect-auto');
      expect(getAspectRatioClass(undefined)).toBe('aspect-auto');
    });

    it('should return correct predefined aspect ratio classes', () => {
      expect(getAspectRatioClass(1.0)).toBe('aspect-square');
      expect(getAspectRatioClass(1.77)).toBe('aspect-video');
      expect(getAspectRatioClass(0.75)).toBe('aspect-[4/3]');
      expect(getAspectRatioClass(0.5)).toBe('aspect-[2/1]');
      expect(getAspectRatioClass(0.6)).toBe('aspect-[5/3]');
      expect(getAspectRatioClass(0.8)).toBe('aspect-[5/4]');
    });

    it('should use predefined class for close matches', () => {
      expect(getAspectRatioClass(1.02)).toBe('aspect-square'); // Close to 1.0
      expect(getAspectRatioClass(1.75)).toBe('aspect-video'); // Close to 1.77
      expect(getAspectRatioClass(0.52)).toBe('aspect-[2/1]'); // Close to 0.5
    });

    it('should generate custom aspect ratio for non-standard values', () => {
      expect(getAspectRatioClass(1.25)).toBe('aspect-[100/125]');
      expect(getAspectRatioClass(0.33)).toBe('aspect-[100/33]');
      expect(getAspectRatioClass(2.5)).toBe('aspect-[100/250]');
    });

    it('should handle very small aspect ratios', () => {
      expect(getAspectRatioClass(0.1)).toBe('aspect-[100/10]');
    });

    it('should handle very large aspect ratios', () => {
      expect(getAspectRatioClass(5.0)).toBe('aspect-[100/500]');
    });

    it('should round aspect ratios appropriately', () => {
      expect(getAspectRatioClass(1.234567)).toBe('aspect-[100/123]');
    });
  });

  describe('getHeightConstraintClasses', () => {
    it('should handle empty config', () => {
      const result = getHeightConstraintClasses({});
      expect(result).toBe('');
    });

    it('should handle fixed height', () => {
      const config: WidgetRenderConfig = { fixedHeight: 300 };
      const result = getHeightConstraintClasses(config);
      expect(result).toBe('h-[300px]');
    });

    it('should handle auto height', () => {
      const config: WidgetRenderConfig = { autoHeight: true };
      const result = getHeightConstraintClasses(config);
      expect(result).toBe('h-auto');
    });

    it('should handle min height only', () => {
      const config: WidgetRenderConfig = { minHeight: 200 };
      const result = getHeightConstraintClasses(config);
      expect(result).toBe('min-h-[200px]');
    });

    it('should handle max height only', () => {
      const config: WidgetRenderConfig = { maxHeight: 400 };
      const result = getHeightConstraintClasses(config);
      expect(result).toBe('max-h-[400px]');
    });

    it('should handle min and max height together', () => {
      const config: WidgetRenderConfig = { 
        minHeight: 200, 
        maxHeight: 400 
      };
      const result = getHeightConstraintClasses(config);
      expect(result).toBe('min-h-[200px] max-h-[400px]');
    });

    it('should handle auto height with constraints', () => {
      const config: WidgetRenderConfig = { 
        autoHeight: true,
        minHeight: 150,
        maxHeight: 500
      };
      const result = getHeightConstraintClasses(config);
      expect(result).toBe('h-auto min-h-[150px] max-h-[500px]');
    });

    it('should prioritize fixed height over auto height', () => {
      const config: WidgetRenderConfig = { 
        fixedHeight: 300,
        autoHeight: true,
        minHeight: 200,
        maxHeight: 400
      };
      const result = getHeightConstraintClasses(config);
      expect(result).toBe('h-[300px]');
    });

    it('should handle zero values', () => {
      const config: WidgetRenderConfig = { 
        fixedHeight: 0,
        minHeight: 0,
        maxHeight: 0
      };
      const result = getHeightConstraintClasses(config);
      expect(result).toBe(''); // Zero values are falsy and get filtered out
    });
  });

  describe('getOverflowClasses', () => {
    it('should default to overflow-hidden', () => {
      expect(getOverflowClasses()).toBe('overflow-hidden');
      expect(getOverflowClasses('')).toBe('overflow-hidden');
      expect(getOverflowClasses('invalid')).toBe('overflow-hidden');
    });

    it('should handle valid overflow values', () => {
      expect(getOverflowClasses('hidden')).toBe('overflow-hidden');
      expect(getOverflowClasses('scroll')).toBe('overflow-auto');
      expect(getOverflowClasses('visible')).toBe('overflow-visible');
    });

    it('should be case sensitive', () => {
      expect(getOverflowClasses('HIDDEN')).toBe('overflow-hidden');
      expect(getOverflowClasses('Hidden')).toBe('overflow-hidden');
    });
  });

  describe('getPaddingClasses', () => {
    it('should return empty string for no padding', () => {
      expect(getPaddingClasses()).toBe('');
      expect(getPaddingClasses(0)).toBe('');
      expect(getPaddingClasses(undefined)).toBe('');
    });

    it('should return Tailwind classes for common padding values', () => {
      expect(getPaddingClasses(4)).toBe('p-1');
      expect(getPaddingClasses(8)).toBe('p-2');
      expect(getPaddingClasses(12)).toBe('p-3');
      expect(getPaddingClasses(16)).toBe('p-4');
      expect(getPaddingClasses(20)).toBe('p-5');
      expect(getPaddingClasses(24)).toBe('p-6');
      expect(getPaddingClasses(32)).toBe('p-8');
    });

    it('should return arbitrary values for non-standard padding', () => {
      expect(getPaddingClasses(10)).toBe('p-[10px]');
      expect(getPaddingClasses(18)).toBe('p-[18px]');
      expect(getPaddingClasses(40)).toBe('p-[40px]');
    });

    it('should handle negative padding', () => {
      expect(getPaddingClasses(-8)).toBe('p-[-8px]');
    });
  });

  describe('getSizeConstraints', () => {
    it('should combine all size constraints', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 1.0,
        minHeight: 200,
        maxHeight: 400,
        overflow: 'hidden',
        padding: 16
      };
      
      const result = getSizeConstraints(config);
      
      expect(result.aspectRatioClass).toBe('aspect-square');
      expect(result.heightClasses).toBe('min-h-[200px] max-h-[400px]');
      expect(result.overflowClasses).toBe('overflow-hidden');
      expect(result.paddingClasses).toBe('p-4');
      expect(result.customProperties).toBeDefined();
      expect(result.customProperties['--aspect-ratio']).toBe('1');
      expect(result.customProperties['--min-height']).toBe('200px');
      expect(result.customProperties['--max-height']).toBe('400px');
      expect(result.customProperties['--padding']).toBe('16px');
    });

    it('should handle empty config', () => {
      const result = getSizeConstraints({});
      
      expect(result.aspectRatioClass).toBe('aspect-auto');
      expect(result.heightClasses).toBe('');
      expect(result.overflowClasses).toBe('overflow-hidden');
      expect(result.paddingClasses).toBe('');
      expect(result.customProperties).toEqual({});
    });

    it('should handle partial config', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 0.6,
        overflow: 'scroll'
      };
      
      const result = getSizeConstraints(config);
      
      expect(result.aspectRatioClass).toBe('aspect-[5/3]');
      expect(result.heightClasses).toBe('');
      expect(result.overflowClasses).toBe('overflow-auto');
      expect(result.paddingClasses).toBe('');
      expect(result.customProperties['--aspect-ratio']).toBe('0.6');
    });
  });

  describe('getSizeCustomProperties', () => {
    it('should generate custom properties for all config values', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 1.5,
        minHeight: 200,
        maxHeight: 400,
        fixedHeight: 300,
        padding: 16
      };
      
      const result = getSizeCustomProperties(config);
      
      expect(result).toEqual({
        '--aspect-ratio': '1.5',
        '--min-height': '200px',
        '--max-height': '400px',
        '--fixed-height': '300px',
        '--padding': '16px'
      });
    });

    it('should handle empty config', () => {
      const result = getSizeCustomProperties({});
      expect(result).toEqual({});
    });

    it('should handle partial config', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 1.0,
        minHeight: 150
      };
      
      const result = getSizeCustomProperties(config);
      
      expect(result).toEqual({
        '--aspect-ratio': '1',
        '--min-height': '150px'
      });
    });

    it('should handle zero values', () => {
      const config: WidgetRenderConfig = {
        aspectRatio: 0,
        minHeight: 0,
        padding: 0
      };
      
      const result = getSizeCustomProperties(config);
      
      expect(result).toEqual({}); // Zero values are falsy and get filtered out
    });
  });

  describe('getContainerQueryClasses', () => {
    it('should generate container query classes with defaults', () => {
      const result = getContainerQueryClasses();
      
      expect(result).toContain('@container');
      expect(result).toContain('@[320px]:text-sm');
      expect(result).toContain('@[480px]:text-base');
      expect(result).toContain('@[640px]:text-lg');
    });

    it('should generate container query classes with custom breakpoints', () => {
      const breakpoints = {
        sm: 300,
        md: 500,
        lg: 700
      };
      
      const result = getContainerQueryClasses(breakpoints);
      
      expect(result).toContain('@container');
      expect(result).toContain('@[300px]:text-sm');
      expect(result).toContain('@[500px]:text-base');
      expect(result).toContain('@[700px]:text-lg');
    });

    it('should merge custom breakpoints with defaults', () => {
      const breakpoints = {
        md: 450
      };
      
      const result = getContainerQueryClasses(breakpoints);
      
      expect(result).toContain('@[320px]:text-sm'); // Default sm
      expect(result).toContain('@[450px]:text-base'); // Custom md
      expect(result).toContain('@[640px]:text-lg'); // Default lg
    });
  });

  describe('getMinMaxGridSize', () => {
    it('should generate minmax function with default max', () => {
      const result = getMinMaxGridSize('200px');
      expect(result).toBe('minmax(200px, 1fr)');
    });

    it('should generate minmax function with custom max', () => {
      const result = getMinMaxGridSize('200px', '400px');
      expect(result).toBe('minmax(200px, 400px)');
    });

    it('should handle fractional units', () => {
      const result = getMinMaxGridSize('1fr', '2fr');
      expect(result).toBe('minmax(1fr, 2fr)');
    });

    it('should handle percentage units', () => {
      const result = getMinMaxGridSize('20%', '50%');
      expect(result).toBe('minmax(20%, 50%)');
    });
  });

  describe('getFlexibleGridColumns', () => {
    it('should generate grid columns with defaults', () => {
      const result = getFlexibleGridColumns(4);
      expect(result).toBe('repeat(4, minmax(200px, 1fr))');
    });

    it('should generate grid columns with custom sizes', () => {
      const result = getFlexibleGridColumns(3, '250px', '400px');
      expect(result).toBe('repeat(3, minmax(250px, 400px))');
    });

    it('should handle single column', () => {
      const result = getFlexibleGridColumns(1, '300px');
      expect(result).toBe('repeat(1, minmax(300px, 1fr))');
    });

    it('should handle many columns', () => {
      const result = getFlexibleGridColumns(12, '100px');
      expect(result).toBe('repeat(12, minmax(100px, 1fr))');
    });
  });

  describe('CHART_TYPE_CONSTRAINTS', () => {
    it('should have constraints for all common chart types', () => {
      const expectedChartTypes = [
        'radar', 'pie', 'donut', 'bar', 'line', 'area', 
        'column', 'gauge', 'heatmap', 'scatter'
      ];
      
      expectedChartTypes.forEach(chartType => {
        expect(CHART_TYPE_CONSTRAINTS).toHaveProperty(chartType);
        expect(CHART_TYPE_CONSTRAINTS[chartType]).toHaveProperty('aspectRatio');
        expect(CHART_TYPE_CONSTRAINTS[chartType]).toHaveProperty('minHeight');
        expect(CHART_TYPE_CONSTRAINTS[chartType]).toHaveProperty('overflow');
      });
    });

    it('should have correct constraints for pie charts', () => {
      const pieConstraints = CHART_TYPE_CONSTRAINTS.pie;
      expect(pieConstraints.aspectRatio).toBe(1.0);
      expect(pieConstraints.minHeight).toBe(200);
      expect(pieConstraints.overflow).toBe('hidden');
    });

    it('should have correct constraints for bar charts', () => {
      const barConstraints = CHART_TYPE_CONSTRAINTS.bar;
      expect(barConstraints.aspectRatio).toBe(0.6);
      expect(barConstraints.minHeight).toBe(180);
      expect(barConstraints.overflow).toBe('hidden');
    });

    it('should have correct constraints for line charts', () => {
      const lineConstraints = CHART_TYPE_CONSTRAINTS.line;
      expect(lineConstraints.aspectRatio).toBe(0.5);
      expect(lineConstraints.minHeight).toBe(160);
      expect(lineConstraints.overflow).toBe('hidden');
    });

    it('should have correct constraints for heatmap charts', () => {
      const heatmapConstraints = CHART_TYPE_CONSTRAINTS.heatmap;
      expect(heatmapConstraints.aspectRatio).toBe(0.8);
      expect(heatmapConstraints.minHeight).toBe(200);
      expect(heatmapConstraints.overflow).toBe('scroll');
    });
  });

  describe('getChartTypeConstraints', () => {
    it('should return constraints for known chart types', () => {
      const pieConstraints = getChartTypeConstraints('pie');
      expect(pieConstraints.aspectRatio).toBe(1.0);
      expect(pieConstraints.minHeight).toBe(200);
      expect(pieConstraints.overflow).toBe('hidden');
    });

    it('should return default constraints for unknown chart types', () => {
      const unknownConstraints = getChartTypeConstraints('unknown-chart');
      expect(unknownConstraints.aspectRatio).toBe(0.7);
      expect(unknownConstraints.minHeight).toBe(200);
      expect(unknownConstraints.overflow).toBe('hidden');
    });

    it('should merge custom config with chart type defaults', () => {
      const customConfig = {
        minHeight: 300,
        padding: 16
      };
      
      const constraints = getChartTypeConstraints('pie', customConfig);
      
      expect(constraints.aspectRatio).toBe(1.0); // From chart type
      expect(constraints.minHeight).toBe(300); // From custom config
      expect(constraints.overflow).toBe('hidden'); // From chart type
      expect(constraints.padding).toBe(16); // From custom config
    });

    it('should allow custom config to override chart type defaults', () => {
      const customConfig = {
        aspectRatio: 2.0,
        overflow: 'scroll' as const
      };
      
      const constraints = getChartTypeConstraints('pie', customConfig);
      
      expect(constraints.aspectRatio).toBe(2.0); // Overridden
      expect(constraints.minHeight).toBe(200); // From chart type
      expect(constraints.overflow).toBe('scroll'); // Overridden
    });

    it('should handle empty custom config', () => {
      const constraints = getChartTypeConstraints('bar', {});
      
      expect(constraints.aspectRatio).toBe(0.6);
      expect(constraints.minHeight).toBe(180);
      expect(constraints.overflow).toBe('hidden');
    });

    it('should handle undefined custom config', () => {
      const constraints = getChartTypeConstraints('line');
      
      expect(constraints.aspectRatio).toBe(0.5);
      expect(constraints.minHeight).toBe(160);
      expect(constraints.overflow).toBe('hidden');
    });

    it('should work with all predefined chart types', () => {
      Object.keys(CHART_TYPE_CONSTRAINTS).forEach(chartType => {
        const constraints = getChartTypeConstraints(chartType);
        expect(constraints).toBeDefined();
        expect(typeof constraints.aspectRatio).toBe('number');
        expect(typeof constraints.minHeight).toBe('number');
        expect(typeof constraints.overflow).toBe('string');
      });
    });
  });
});