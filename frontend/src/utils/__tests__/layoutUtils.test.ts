/**
 * Layout Utils Tests
 * 
 * Comprehensive tests for the CSS Grid layout and size constraint utilities
 */

import {
  computeLayoutClasses,
  computeDashboardLayout,
  getAutoFitGridClasses,
  getAutoFitGridProperties,
  validateLayoutConfig,
  getCurrentBreakpoint,
  calculateOptimalGrid,
  DEFAULT_GRID_CONFIG,
  type LayoutConfig,
  type ComputedLayoutClasses
} from '../layoutUtils';

import {
  getGridColumnClass,
  getGridRowClass,
  getResponsiveGridClasses,
  getMobileFirstColumnClasses,
  getMobileFirstPaddingClasses,
  getMobileFirstContainerClasses,
  validateGridPosition,
  getGridCustomProperties
} from '../gridUtils';

import {
  getSizeConstraints,
  getAspectRatioClass,
  getHeightConstraintClasses,
  getChartTypeConstraints,
  CHART_TYPE_CONSTRAINTS
} from '../sizeConstraints';

describe('Layout Utils', () => {
  const mockPosition = { x: 0, y: 0, w: 6, h: 2 };
  const mockRenderConfig = {
    aspectRatio: 0.6,
    minHeight: 200,
    overflow: 'hidden' as const
  };

  describe('computeLayoutClasses', () => {
    it('should compute complete layout classes with default options', () => {
      const config: LayoutConfig = {
        position: mockPosition,
        renderConfig: mockRenderConfig
      };
      
      const result = computeLayoutClasses(config);
      
      expect(result.containerClasses).toContain('grid');
      expect(result.containerClasses).toContain('grid-cols-1');
      expect(result.containerClasses).toContain('md:grid-cols-2');
      expect(result.containerClasses).toContain('lg:grid-cols-3');
      expect(result.containerClasses).toContain('xl:grid-cols-4');
      expect(result.containerClasses).toContain('grid-transition');
      
      expect(result.itemClasses).toContain('col-span-6');
      expect(result.itemClasses).toContain('row-span-2');
      expect(result.itemClasses).toContain('min-h-0');
      expect(result.itemClasses).toContain('overflow-hidden');
      expect(result.itemClasses).toContain('grid-item-transition');
      expect(result.itemClasses).toContain('chart-container');
      
      expect(result.contentClasses).toContain('chart-content');
      expect(result.contentClasses).toContain('flex-1');
      expect(result.contentClasses).toContain('min-h-0');
      expect(result.contentClasses).toContain('overflow-hidden');
      
      expect(result.customProperties).toBeDefined();
      expect(result.customProperties['--grid-column-start']).toBe('1');
      expect(result.customProperties['--grid-column-end']).toBe('7');
    });

    it('should apply chart type optimizations', () => {
      const config: LayoutConfig = {
        position: mockPosition,
        renderConfig: mockRenderConfig,
        chartType: 'pie'
      };
      
      const result = computeLayoutClasses(config);
      
      // Should apply pie chart optimizations - the aspect ratio from renderConfig (0.6) takes precedence
      expect(result.itemClasses).toContain('aspect-[5/3]'); // 0.6 aspect ratio
    });

    it('should handle responsive disabled', () => {
      const config: LayoutConfig = {
        position: mockPosition,
        renderConfig: mockRenderConfig,
        responsive: false
      };
      
      const result = computeLayoutClasses(config);
      
      expect(result.containerClasses).toContain('grid-cols-12');
      expect(result.containerClasses).not.toContain('md:grid-cols-2');
    });

    it('should include debug classes when enabled', () => {
      const config: LayoutConfig = {
        position: mockPosition,
        renderConfig: mockRenderConfig,
        debug: true
      };
      
      const result = computeLayoutClasses(config);
      
      expect(result.containerClasses).toContain('grid-debug');
    });

    it('should validate and correct invalid positions', () => {
      const config: LayoutConfig = {
        position: { x: -1, y: 0, w: 15, h: 2 },
        renderConfig: mockRenderConfig
      };
      
      const result = computeLayoutClasses(config);
      
      // Position should be corrected by validateGridPosition
      expect(result.customProperties['--grid-column-start']).toBe('1');
      expect(result.customProperties['--grid-column-end']).toBe('13'); // x=0, w=12 (clamped)
    });

    it('should work with custom grid configuration', () => {
      const customGridConfig = {
        sm: { columns: 1, minWidth: 640 },
        md: { columns: 3, minWidth: 768 },
        lg: { columns: 5, minWidth: 1024 },
        xl: { columns: 6, minWidth: 1280 }
      };
      
      const config: LayoutConfig = {
        position: mockPosition,
        renderConfig: mockRenderConfig
      };
      
      const result = computeLayoutClasses(config, 12, customGridConfig);
      
      expect(result.containerClasses).toContain('md:grid-cols-3');
      expect(result.containerClasses).toContain('lg:grid-cols-5');
      expect(result.containerClasses).toContain('xl:grid-cols-6');
    });
  });

  describe('computeDashboardLayout', () => {
    it('should compute layout for multiple widgets', () => {
      const widgets = [
        {
          id: 'widget1',
          position: { x: 0, y: 0, w: 6, h: 2 },
          renderConfig: { aspectRatio: 1.0 },
          chartType: 'pie'
        },
        {
          id: 'widget2',
          position: { x: 6, y: 0, w: 6, h: 2 },
          renderConfig: { aspectRatio: 0.6 },
          chartType: 'bar'
        }
      ];
      
      const result = computeDashboardLayout(widgets);
      
      expect(result).toHaveProperty('widget1');
      expect(result).toHaveProperty('widget2');
      
      expect(result.widget1.itemClasses).toContain('col-span-6');
      expect(result.widget1.itemClasses).toContain('aspect-square');
      
      expect(result.widget2.itemClasses).toContain('col-start-7');
      expect(result.widget2.itemClasses).toContain('col-span-6');
    });

    it('should handle empty widgets array', () => {
      const result = computeDashboardLayout([]);
      
      expect(result).toEqual({});
    });

    it('should apply custom grid configuration to all widgets', () => {
      const widgets = [
        {
          id: 'widget1',
          position: { x: 0, y: 0, w: 4, h: 1 },
          renderConfig: {}
        }
      ];
      
      const customGridConfig = {
        sm: { columns: 1, minWidth: 640 },
        md: { columns: 2, minWidth: 768 },
        lg: { columns: 4, minWidth: 1024 },
        xl: { columns: 8, minWidth: 1280 }
      };
      
      const result = computeDashboardLayout(widgets, 12, customGridConfig);
      
      expect(result.widget1.containerClasses).toContain('xl:grid-cols-8');
    });
  });

  describe('getAutoFitGridClasses', () => {
    it('should generate auto-fit grid classes with defaults', () => {
      const result = getAutoFitGridClasses();
      
      expect(result).toContain('auto-fit-grid');
      expect(result).toContain('grid');
      expect(result).toContain('gap-[1rem]');
    });

    it('should generate auto-fit grid classes with custom values', () => {
      const result = getAutoFitGridClasses('250px', '0.5rem', '180px');
      
      expect(result).toContain('auto-fit-grid');
      expect(result).toContain('grid');
      expect(result).toContain('gap-[0.5rem]');
    });
  });

  describe('getAutoFitGridProperties', () => {
    it('should generate auto-fit grid properties with defaults', () => {
      const result = getAutoFitGridProperties();
      
      expect(result).toEqual({
        '--min-column-width': '300px',
        '--min-row-height': '200px'
      });
    });

    it('should generate auto-fit grid properties with custom values', () => {
      const result = getAutoFitGridProperties('250px', '180px');
      
      expect(result).toEqual({
        '--min-column-width': '250px',
        '--min-row-height': '180px'
      });
    });
  });

  describe('validateLayoutConfig', () => {
    it('should validate correct configuration', () => {
      const config: LayoutConfig = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {
          minHeight: 200,
          maxHeight: 400
        }
      };
      
      const result = validateLayoutConfig(config);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid position', () => {
      const config: LayoutConfig = {
        position: { x: -1, y: 0, w: 6, h: 2 },
        renderConfig: {}
      };
      
      const result = validateLayoutConfig(config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should detect conflicting height constraints', () => {
      const config: LayoutConfig = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {
          minHeight: 400,
          maxHeight: 200
        }
      };
      
      const result = validateLayoutConfig(config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('minHeight'))).toBe(true);
    });

    it('should detect position extending beyond grid', () => {
      const config: LayoutConfig = {
        position: { x: 10, y: 0, w: 5, h: 2 },
        renderConfig: {}
      };
      
      const result = validateLayoutConfig(config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('extends beyond grid'))).toBe(true);
    });

    it('should detect invalid aspect ratio', () => {
      const config: LayoutConfig = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {
          aspectRatio: -1
        }
      };
      
      const result = validateLayoutConfig(config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('aspectRatio'))).toBe(true);
    });

    it('should warn about conflicting height settings', () => {
      const config: LayoutConfig = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {
          fixedHeight: 300,
          autoHeight: true
        }
      };
      
      const result = validateLayoutConfig(config);
      
      expect(result.warnings.some(warning => warning.includes('fixedHeight') && warning.includes('autoHeight'))).toBe(true);
    });
  });

  describe('getCurrentBreakpoint', () => {
    it('should return correct breakpoint for different widths', () => {
      expect(getCurrentBreakpoint(500)).toBe('sm');
      expect(getCurrentBreakpoint(800)).toBe('md');
      expect(getCurrentBreakpoint(1100)).toBe('lg');
      expect(getCurrentBreakpoint(1400)).toBe('xl');
    });

    it('should work with custom grid configuration', () => {
      const customConfig = {
        sm: { columns: 1, minWidth: 480 },
        md: { columns: 2, minWidth: 720 },
        lg: { columns: 3, minWidth: 960 },
        xl: { columns: 4, minWidth: 1200 }
      };
      
      expect(getCurrentBreakpoint(500, customConfig)).toBe('sm');
      expect(getCurrentBreakpoint(800, customConfig)).toBe('md');
      expect(getCurrentBreakpoint(1000, customConfig)).toBe('lg');
      expect(getCurrentBreakpoint(1300, customConfig)).toBe('xl');
    });

    it('should handle edge cases at breakpoint boundaries', () => {
      expect(getCurrentBreakpoint(640)).toBe('sm'); // Exactly at sm boundary
      expect(getCurrentBreakpoint(768)).toBe('md'); // Exactly at md boundary
      expect(getCurrentBreakpoint(1024)).toBe('lg'); // Exactly at lg boundary
      expect(getCurrentBreakpoint(1280)).toBe('xl'); // Exactly at xl boundary
    });
  });

  describe('calculateOptimalGrid', () => {
    it('should calculate optimal grid dimensions', () => {
      const result = calculateOptimalGrid(8, 1200, 300);
      expect(result.columns).toBe(4);
      expect(result.rows).toBe(2);
    });

    it('should handle small viewports', () => {
      const result = calculateOptimalGrid(6, 400, 300);
      expect(result.columns).toBe(1);
      expect(result.rows).toBe(6);
    });

    it('should handle more widgets than can fit in one row', () => {
      const result = calculateOptimalGrid(10, 1200, 300);
      expect(result.columns).toBe(4);
      expect(result.rows).toBe(3); // ceil(10/4) = 3
    });

    it('should handle exact fit scenarios', () => {
      const result = calculateOptimalGrid(6, 1800, 300);
      expect(result.columns).toBe(6);
      expect(result.rows).toBe(1);
    });

    it('should ensure minimum of 1 column for normal viewport', () => {
      const result = calculateOptimalGrid(1, 1200, 300); // Large enough viewport
      expect(result.columns).toBe(1);
      expect(result.rows).toBe(1);
    });

    it('should handle small viewport edge case', () => {
      const result = calculateOptimalGrid(1, 100, 300);
      expect(result.columns).toBe(1); // Math.max(1, 0) = 1
      // Math.ceil(1/0) = Infinity, Math.max(1, Infinity) = Infinity
      expect(result.rows).toBe(Infinity);
    });

    it('should handle zero widgets edge case', () => {
      const result = calculateOptimalGrid(0, 100, 300);
      expect(result.columns).toBe(1);
      // Math.ceil(0/0) = NaN, Math.max(1, NaN) = NaN
      expect(result.rows).toBeNaN();
    });

    it('should respect widget count limit', () => {
      const result = calculateOptimalGrid(2, 1800, 300);
      expect(result.columns).toBe(2);
      expect(result.rows).toBe(1);
    });
  });

  describe('DEFAULT_GRID_CONFIG', () => {
    it('should have correct default breakpoint configuration', () => {
      expect(DEFAULT_GRID_CONFIG).toEqual({
        sm: { columns: 1, minWidth: 640 },
        md: { columns: 2, minWidth: 768 },
        lg: { columns: 3, minWidth: 1024 },
        xl: { columns: 4, minWidth: 1280 }
      });
    });

    it('should have all required breakpoints', () => {
      const requiredBreakpoints = ['sm', 'md', 'lg', 'xl'];
      
      requiredBreakpoints.forEach(bp => {
        expect(DEFAULT_GRID_CONFIG).toHaveProperty(bp);
        expect(DEFAULT_GRID_CONFIG[bp as keyof typeof DEFAULT_GRID_CONFIG]).toHaveProperty('columns');
        expect(DEFAULT_GRID_CONFIG[bp as keyof typeof DEFAULT_GRID_CONFIG]).toHaveProperty('minWidth');
      });
    });
  });

  describe('Integration with Grid Utils', () => {
    it('should work with getGridColumnClass', () => {
      const position = { x: 2, y: 0, w: 4, h: 2 };
      const result = getGridColumnClass(position, 12);
      expect(result).toBe('col-start-3 col-span-4');
    });

    it('should work with getGridRowClass', () => {
      const position = { x: 0, y: 0, w: 4, h: 3 };
      const result = getGridRowClass(position);
      expect(result).toBe('row-span-3');
    });

    it('should work with getResponsiveGridClasses', () => {
      const result = getResponsiveGridClasses();
      expect(result).toContain('grid');
      expect(result).toContain('grid-cols-1');
      expect(result).toContain('md:grid-cols-2');
    });

    it('should work with validateGridPosition', () => {
      const position = { x: -1, y: 0, w: 15, h: 2 };
      const result = validateGridPosition(position, 12);
      expect(result.x).toBe(0);
      expect(result.w).toBe(12);
    });

    it('should work with getGridCustomProperties', () => {
      const position = { x: 2, y: 1, w: 4, h: 2 };
      const result = getGridCustomProperties(position, 12);
      expect(result['--grid-column-start']).toBe('3');
      expect(result['--grid-column-end']).toBe('7');
    });
  });

  describe('Integration with Size Constraints', () => {
    it('should work with getSizeConstraints', () => {
      const config = {
        aspectRatio: 1.0,
        minHeight: 200,
        overflow: 'hidden' as const
      };
      
      const result = getSizeConstraints(config);
      expect(result.aspectRatioClass).toBe('aspect-square');
      expect(result.heightClasses).toContain('min-h-[200px]');
    });

    it('should work with getChartTypeConstraints', () => {
      const baseConfig = { minHeight: 150 };
      const result = getChartTypeConstraints('pie', baseConfig);
      expect(result.aspectRatio).toBe(1.0);
      expect(result.minHeight).toBe(150); // Custom config takes precedence
    });

    it('should work with CHART_TYPE_CONSTRAINTS', () => {
      expect(CHART_TYPE_CONSTRAINTS).toHaveProperty('pie');
      expect(CHART_TYPE_CONSTRAINTS).toHaveProperty('bar');
      expect(CHART_TYPE_CONSTRAINTS).toHaveProperty('line');
      expect(CHART_TYPE_CONSTRAINTS.pie.aspectRatio).toBe(1.0);
    });
  });

  describe('Mobile-First Responsive Integration', () => {
    it('should work with getMobileFirstColumnClasses', () => {
      const result = getMobileFirstColumnClasses(6);
      expect(result).toContain('col-span-12');
      expect(result).toContain('md:col-span-6');
    });

    it('should work with getMobileFirstPaddingClasses', () => {
      const result = getMobileFirstPaddingClasses();
      expect(result).toBe('p-2 sm:p-3 lg:p-4');
    });

    it('should work with getMobileFirstContainerClasses', () => {
      const result = getMobileFirstContainerClasses();
      expect(result).toBe('w-full max-w-none overflow-hidden min-h-0');
    });
  });
});