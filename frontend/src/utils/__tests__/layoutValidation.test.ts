import {
  validateLayoutConfig,
  validateGridConfig,
  validateWidgets,
  validateRows,
  validateWidgetReferences,
  formatValidationResults
} from '../layoutValidation';
import { DashboardLayout } from '../../types/dashboard';

describe('Layout Validation', () => {
  const validLayout: DashboardLayout = {
    grid: {
      mode: 'strict-grid',
      gap: 4,
      breakpoints: {
        sm: { minWidth: 640, columns: 1, rowUnit: 80 },
        md: { minWidth: 768, columns: 2, rowUnit: 80 },
        lg: { minWidth: 1024, columns: 3, rowUnit: 80 },
        xl: { minWidth: 1280, columns: 4, rowUnit: 80 }
      }
    },
    widgets: [
      {
        id: 'widget1',
        type: 'bar',
        title: 'Test Widget',
        dataSource: { componentId: 1 },
        render: { autoHeight: true, minHeight: 200 }
      }
    ],
    rows: [
      {
        id: 'row1',
        rowHeight: 'auto',
        items: [
          { widgetRef: 'widget1', col: 1, span: 2, h: 1 }
        ]
      }
    ]
  };

  describe('validateLayoutConfig', () => {
    it('should validate a correct layout', () => {
      const result = validateLayoutConfig(validLayout);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject null/undefined layout', () => {
      const result = validateLayoutConfig(null);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'layout',
          message: 'Layout configuration must be an object'
        })
      );
    });

    it('should reject layout missing required properties', () => {
      const result = validateLayoutConfig({});
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'grid',
          message: 'Missing required property: grid'
        })
      );
    });
  });

  describe('validateGridConfig', () => {
    it('should validate correct grid config', () => {
      const result = validateGridConfig(validLayout.grid);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid grid mode', () => {
      const invalidGrid = { ...validLayout.grid, mode: 'invalid-mode' };
      const result = validateGridConfig(invalidGrid);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'grid.mode',
          message: 'Invalid grid mode: invalid-mode'
        })
      );
    });

    it('should reject negative gap', () => {
      const invalidGrid = { ...validLayout.grid, gap: -5 };
      const result = validateGridConfig(invalidGrid);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'grid.gap',
          message: 'Grid gap cannot be negative'
        })
      );
    });

    it('should warn about large gap values', () => {
      const largeGapGrid = { ...validLayout.grid, gap: 50 };
      const result = validateGridConfig(largeGapGrid);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContainEqual(
        expect.objectContaining({
          field: 'grid.gap',
          message: 'Large gap values may cause layout issues'
        })
      );
    });
  });

  describe('validateWidgets', () => {
    it('should validate correct widgets array', () => {
      const result = validateWidgets(validLayout.widgets);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject non-array widgets', () => {
      const result = validateWidgets('not-an-array');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'widgets',
          message: 'Widgets must be an array'
        })
      );
    });

    it('should detect duplicate widget IDs', () => {
      const duplicateWidgets = [
        validLayout.widgets[0],
        { ...validLayout.widgets[0] } // Same ID
      ];
      const result = validateWidgets(duplicateWidgets);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'widgets[1].id',
          message: 'Duplicate widget ID: widget1'
        })
      );
    });

    it('should reject widget with missing required properties', () => {
      const invalidWidget = { id: 'test' }; // Missing other required props
      const result = validateWidgets([invalidWidget]);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('validateRows', () => {
    it('should validate correct rows array', () => {
      const result = validateRows(validLayout.rows);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject non-array rows', () => {
      const result = validateRows('not-an-array');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'rows',
          message: 'Rows must be an array'
        })
      );
    });

    it('should detect duplicate row IDs', () => {
      const duplicateRows = [
        validLayout.rows[0],
        { ...validLayout.rows[0] } // Same ID
      ];
      const result = validateRows(duplicateRows);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'rows[1].id',
          message: 'Duplicate row ID: row1'
        })
      );
    });

    it('should validate row items', () => {
      const invalidRow = {
        id: 'test-row',
        rowHeight: 'auto',
        items: [
          { widgetRef: 'widget1', col: 0, span: 1, h: 1 } // col should be >= 1
        ]
      };
      const result = validateRows([invalidRow]);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'rows[0].items[0].col',
          message: 'col must be positive'
        })
      );
    });
  });

  describe('validateWidgetReferences', () => {
    it('should validate correct widget references', () => {
      const result = validateWidgetReferences(validLayout.widgets, validLayout.rows);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing widget references', () => {
      const rowsWithMissingRef = [
        {
          id: 'row1',
          rowHeight: 'auto' as const,
          items: [
            { widgetRef: 'missing-widget', col: 1, span: 2, h: 1 }
          ]
        }
      ];
      const result = validateWidgetReferences(validLayout.widgets, rowsWithMissingRef);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'rows[0].items[0].widgetRef',
          message: "Widget reference 'missing-widget' not found"
        })
      );
    });

    it('should detect orphaned widgets', () => {
      const orphanedWidgets = [
        ...validLayout.widgets,
        {
          id: 'orphaned-widget',
          type: 'line',
          title: 'Orphaned Widget',
          dataSource: { componentId: 2 },
          render: {}
        }
      ];
      const result = validateWidgetReferences(orphanedWidgets, validLayout.rows);
      expect(result.isValid).toBe(true); // Orphaned widgets are warnings, not errors
      expect(result.warnings).toContainEqual(
        expect.objectContaining({
          field: 'widgets',
          message: expect.stringContaining('orphaned-widget')
        })
      );
    });
  });

  describe('formatValidationResults', () => {
    it('should format valid results', () => {
      const result = { isValid: true, errors: [], warnings: [], info: [] };
      const formatted = formatValidationResults(result);
      expect(formatted).toContain('✅ Layout configuration is valid');
    });

    it('should format invalid results with errors', () => {
      const result = {
        isValid: false,
        errors: [
          {
            field: 'test.field',
            message: 'Test error',
            severity: 'error' as const,
            suggestions: ['Fix this issue']
          }
        ],
        warnings: [],
        info: []
      };
      const formatted = formatValidationResults(result);
      expect(formatted).toContain('❌ Layout configuration has errors');
      expect(formatted).toContain('🚨 Errors:');
      expect(formatted).toContain('test.field: Test error');
      expect(formatted).toContain('💡 Fix this issue');
    });

    it('should format warnings and info', () => {
      const result = {
        isValid: true,
        errors: [],
        warnings: [
          {
            field: 'test.warning',
            message: 'Test warning',
            severity: 'warning' as const
          }
        ],
        info: [
          {
            field: 'test.info',
            message: 'Test info',
            severity: 'info' as const
          }
        ]
      };
      const formatted = formatValidationResults(result);
      expect(formatted).toContain('⚠️ Warnings:');
      expect(formatted).toContain('ℹ️ Information:');
    });
  });
});