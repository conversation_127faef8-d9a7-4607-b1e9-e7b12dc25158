import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, AVAILABLE_THEMES, ThemeKey } from '../contexts/ThemeContext';
import Dashboard from '../components/Dashboard';
import ThemeSelector from '../components/ThemeSelector';

// Mock API client and auth context for components that need them
jest.mock('../services/api', () => ({
  apiClient: {
    updateUserTheme: jest.fn().mockResolvedValue({ user: {} }),
  },
}));

jest.mock('../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: '1', email: '<EMAIL>', role: 'regular' },
    updateUser: jest.fn(),
  }),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock console methods to avoid noise in tests
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

// Utility function to calculate color contrast ratio
const getLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};

const getContrastRatio = (color1: [number, number, number], color2: [number, number, number]): number => {
  const lum1 = getLuminance(...color1);
  const lum2 = getLuminance(...color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
};

// Parse RGB color string to RGB values
const parseRgb = (rgbString: string): [number, number, number] | null => {
  const match = rgbString.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (!match) return null;
  return [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
};

// Test component that renders common UI elements
const AccessibilityTestComponent: React.FC = () => {
  return (
    <div className="min-h-screen bg-base-100">
      {/* Main content area */}
      <div className="bg-base-100 text-base-content p-8">
        <h1 className="text-3xl font-bold text-base-content mb-4">
          Main Heading
        </h1>
        <p className="text-base-content/70 mb-4">
          Secondary text content that should have sufficient contrast
        </p>
        
        {/* Card component */}
        <div className="card bg-base-200 text-base-content p-6 mb-4">
          <h2 className="text-xl font-semibold text-base-content mb-2">
            Card Title
          </h2>
          <p className="text-base-content/70">
            Card content with secondary text
          </p>
        </div>

        {/* Button components */}
        <div className="flex gap-4 mb-4">
          <button className="btn btn-primary">Primary Button</button>
          <button className="btn btn-secondary">Secondary Button</button>
          <button className="btn btn-ghost">Ghost Button</button>
        </div>

        {/* Form elements */}
        <div className="form-control mb-4">
          <label className="label">
            <span className="label-text text-base-content">Form Label</span>
          </label>
          <input 
            type="text" 
            className="input input-bordered bg-base-100 text-base-content" 
            placeholder="Input placeholder"
          />
        </div>

        {/* Alert/notification styles */}
        <div className="alert alert-info mb-4">
          <span className="text-info-content">Info alert message</span>
        </div>
        
        <div className="alert alert-success mb-4">
          <span className="text-success-content">Success alert message</span>
        </div>
        
        <div className="alert alert-warning mb-4">
          <span className="text-warning-content">Warning alert message</span>
        </div>
        
        <div className="alert alert-error mb-4">
          <span className="text-error-content">Error alert message</span>
        </div>
      </div>
    </div>
  );
};

describe('Accessibility - Color Contrast', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    document.documentElement.removeAttribute('data-theme');
  });

  // Test each theme for color contrast compliance
  AVAILABLE_THEMES.forEach(theme => {
    describe(`Theme: ${theme.name} (${theme.key})`, () => {
      beforeEach(() => {
        // Apply theme to document
        document.documentElement.setAttribute('data-theme', theme.key);
      });

      test('main content has sufficient contrast ratio', async () => {
        render(
          <ThemeProvider defaultTheme={theme.key}>
            <AccessibilityTestComponent />
          </ThemeProvider>
        );

        // Wait for theme to be applied
        await new Promise(resolve => setTimeout(resolve, 100));

        const mainHeading = screen.getByText('Main Heading');
        const mainContent = mainHeading.closest('div');
        
        if (mainContent) {
          const styles = window.getComputedStyle(mainContent);
          const bgColor = parseRgb(styles.backgroundColor);
          const textColor = parseRgb(styles.color);
          
          if (bgColor && textColor) {
            const contrastRatio = getContrastRatio(bgColor, textColor);
            
            // WCAG AA requires 4.5:1 for normal text, 3:1 for large text
            expect(contrastRatio).toBeGreaterThanOrEqual(4.5);
          }
        }
      });

      test('card components have sufficient contrast', async () => {
        render(
          <ThemeProvider defaultTheme={theme.key}>
            <AccessibilityTestComponent />
          </ThemeProvider>
        );

        await new Promise(resolve => setTimeout(resolve, 100));

        const cardTitle = screen.getByText('Card Title');
        const card = cardTitle.closest('.card');
        
        if (card) {
          const styles = window.getComputedStyle(card);
          const bgColor = parseRgb(styles.backgroundColor);
          const textColor = parseRgb(styles.color);
          
          if (bgColor && textColor) {
            const contrastRatio = getContrastRatio(bgColor, textColor);
            expect(contrastRatio).toBeGreaterThanOrEqual(4.5);
          }
        }
      });

      test('button components have sufficient contrast', async () => {
        render(
          <ThemeProvider defaultTheme={theme.key}>
            <AccessibilityTestComponent />
          </ThemeProvider>
        );

        await new Promise(resolve => setTimeout(resolve, 100));

        const primaryButton = screen.getByText('Primary Button');
        const buttonStyles = window.getComputedStyle(primaryButton);
        const bgColor = parseRgb(buttonStyles.backgroundColor);
        const textColor = parseRgb(buttonStyles.color);
        
        if (bgColor && textColor) {
          const contrastRatio = getContrastRatio(bgColor, textColor);
          expect(contrastRatio).toBeGreaterThanOrEqual(4.5);
        }
      });

      test('form elements have sufficient contrast', async () => {
        render(
          <ThemeProvider defaultTheme={theme.key}>
            <AccessibilityTestComponent />
          </ThemeProvider>
        );

        await new Promise(resolve => setTimeout(resolve, 100));

        const formLabel = screen.getByText('Form Label');
        const labelStyles = window.getComputedStyle(formLabel);
        const labelBgColor = parseRgb(labelStyles.backgroundColor || 'rgb(255, 255, 255)');
        const labelTextColor = parseRgb(labelStyles.color);
        
        if (labelBgColor && labelTextColor) {
          const contrastRatio = getContrastRatio(labelBgColor, labelTextColor);
          expect(contrastRatio).toBeGreaterThanOrEqual(4.5);
        }
      });

      test('alert components have sufficient contrast', async () => {
        render(
          <ThemeProvider defaultTheme={theme.key}>
            <AccessibilityTestComponent />
          </ThemeProvider>
        );

        await new Promise(resolve => setTimeout(resolve, 100));

        const alertTypes = ['Info alert message', 'Success alert message', 'Warning alert message', 'Error alert message'];
        
        alertTypes.forEach(alertText => {
          const alert = screen.getByText(alertText);
          const alertStyles = window.getComputedStyle(alert);
          const bgColor = parseRgb(alertStyles.backgroundColor);
          const textColor = parseRgb(alertStyles.color);
          
          if (bgColor && textColor) {
            const contrastRatio = getContrastRatio(bgColor, textColor);
            expect(contrastRatio).toBeGreaterThanOrEqual(4.5);
          }
        });
      });

      test('secondary text has adequate contrast', async () => {
        render(
          <ThemeProvider defaultTheme={theme.key}>
            <AccessibilityTestComponent />
          </ThemeProvider>
        );

        await new Promise(resolve => setTimeout(resolve, 100));

        const secondaryText = screen.getByText('Secondary text content that should have sufficient contrast');
        const textStyles = window.getComputedStyle(secondaryText);
        const parentStyles = window.getComputedStyle(secondaryText.parentElement!);
        
        const bgColor = parseRgb(parentStyles.backgroundColor);
        const textColor = parseRgb(textStyles.color);
        
        if (bgColor && textColor) {
          const contrastRatio = getContrastRatio(bgColor, textColor);
          // Secondary text should have at least 3:1 ratio (WCAG AA for large text)
          expect(contrastRatio).toBeGreaterThanOrEqual(3.0);
        }
      });
    });
  });

  describe('Theme Selector Accessibility', () => {
    test('theme selector modal has proper contrast in all themes', async () => {
      for (const theme of AVAILABLE_THEMES) {
        document.documentElement.setAttribute('data-theme', theme.key);
        
        const { unmount } = render(
          <ThemeProvider defaultTheme={theme.key}>
            <ThemeSelector isOpen={true} onClose={() => {}} />
          </ThemeProvider>
        );

        await new Promise(resolve => setTimeout(resolve, 50));

        const modalTitle = screen.getByText('테마 선택');
        const modal = modalTitle.closest('.modal-box');
        
        if (modal) {
          const styles = window.getComputedStyle(modal);
          const bgColor = parseRgb(styles.backgroundColor);
          const textColor = parseRgb(styles.color);
          
          if (bgColor && textColor) {
            const contrastRatio = getContrastRatio(bgColor, textColor);
            expect(contrastRatio).toBeGreaterThanOrEqual(4.5);
          }
        }

        unmount();
      }
    });
  });

  describe('Focus Indicators', () => {
    test('interactive elements have visible focus indicators', async () => {
      render(
        <ThemeProvider>
          <AccessibilityTestComponent />
        </ThemeProvider>
      );

      const primaryButton = screen.getByText('Primary Button');
      primaryButton.focus();

      // Check that focus styles are applied
      const focusedStyles = window.getComputedStyle(primaryButton, ':focus');
      
      // DaisyUI buttons should have focus outline or ring
      expect(
        focusedStyles.outline !== 'none' || 
        focusedStyles.boxShadow.includes('ring') ||
        focusedStyles.borderColor !== primaryButton.style.borderColor
      ).toBe(true);
    });

    test('form inputs have visible focus indicators', async () => {
      render(
        <ThemeProvider>
          <AccessibilityTestComponent />
        </ThemeProvider>
      );

      const input = screen.getByPlaceholderText('Input placeholder');
      input.focus();

      const focusedStyles = window.getComputedStyle(input, ':focus');
      
      // Input should have focus outline or border change
      expect(
        focusedStyles.outline !== 'none' || 
        focusedStyles.boxShadow.includes('ring') ||
        focusedStyles.borderColor !== input.style.borderColor
      ).toBe(true);
    });
  });

  describe('Color-only Information', () => {
    test('important information is not conveyed by color alone', () => {
      render(
        <ThemeProvider>
          <AccessibilityTestComponent />
        </ThemeProvider>
      );

      // Alert messages should have text content, not just color
      const alerts = [
        screen.getByText('Info alert message'),
        screen.getByText('Success alert message'),
        screen.getByText('Warning alert message'),
        screen.getByText('Error alert message'),
      ];

      alerts.forEach(alert => {
        // Each alert should have meaningful text content
        expect(alert.textContent).toBeTruthy();
        expect(alert.textContent!.length).toBeGreaterThan(0);
      });
    });
  });
});