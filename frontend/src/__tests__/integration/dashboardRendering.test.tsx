/**
 * Dashboard Rendering Integration Tests
 * 
 * Tests for the complete dashboard layout rendering pipeline,
 * including responsive behavior, error handling, and layout migration.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

import { DashboardGrid } from '../../components/DashboardGrid';
import { PageContent } from '../../components/PageContent';
import { LayoutErrorBoundary } from '../../components/LayoutErrorBoundary';
import { validateLayoutConfig } from '../../utils/layoutValidation';
import { computeLayoutClasses, computeDashboardLayout } from '../../utils/layoutUtils';
import { DashboardLayout, Page, Widget } from '../../types/dashboard';

// Mock data for testing
const mockLegacyPage: Page = {
  id: 1,
  title: 'Legacy Dashboard',
  layout_config: {
    // Legacy grid_position format
    components: [
      {
        id: 'widget1',
        grid_position: { x: 0, y: 0, w: 6, h: 2 },
        type: 'bar',
        title: 'Sales Chart'
      },
      {
        id: 'widget2', 
        grid_position: { x: 6, y: 0, w: 6, h: 2 },
        type: 'pie',
        title: 'Revenue Chart'
      }
    ]
  },
  accessible_organizations: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockNewSchemaPage: Page = {
  id: 2,
  title: 'Modern Dashboard',
  layout_config: {
    grid: {
      mode: 'strict-grid',
      gap: 4,
      breakpoints: {
        sm: { minWidth: 640, columns: 1, rowUnit: 80 },
        md: { minWidth: 768, columns: 2, rowUnit: 80 },
        lg: { minWidth: 1024, columns: 3, rowUnit: 80 },
        xl: { minWidth: 1280, columns: 4, rowUnit: 80 }
      }
    },
    widgets: [
      {
        id: 'widget1',
        type: 'bar',
        title: 'Sales Chart',
        dataSource: { componentId: 1 },
        render: {
          aspectRatio: 0.6,
          minHeight: 200,
          overflow: 'hidden'
        }
      },
      {
        id: 'widget2',
        type: 'pie', 
        title: 'Revenue Chart',
        dataSource: { componentId: 2 },
        render: {
          aspectRatio: 1.0,
          minHeight: 200,
          overflow: 'hidden'
        }
      }
    ],
    rows: [
      {
        id: 'row1',
        rowHeight: 'auto',
        items: [
          { widgetRef: 'widget1', col: 1, span: 6, h: 2 },
          { widgetRef: 'widget2', col: 7, span: 6, h: 2 }
        ]
      }
    ]
  } as DashboardLayout,
  accessible_organizations: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockInvalidPage: Page = {
  id: 3,
  title: 'Invalid Dashboard',
  layout_config: {
    // Missing required properties
    widgets: [],
    rows: []
  } as any,
  accessible_organizations: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

// Mock components
jest.mock('../../components/ChartComponent', () => ({
  ChartComponent: ({ title, type, renderConfig }: any) => (
    <div 
      data-testid={`chart-${title.replace(/\s+/g, '-').toLowerCase()}`}
      data-chart-type={type}
      data-render-config={JSON.stringify(renderConfig)}
      className="mock-chart"
    >
      {title} ({type})
    </div>
  )
}));

describe('Dashboard Rendering Integration', () => {
  beforeEach(() => {
    // Reset viewport size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 768,
    });
  });

  describe('Complete Dashboard Layout Rendering Pipeline', () => {
    it('should render dashboard with new schema layout', async () => {
      const { container } = render(
        <PageContent page={mockNewSchemaPage} />
      );

      // Should render the dashboard grid
      expect(container.querySelector('.dashboard-grid')).toBeInTheDocument();
      
      // Should render both widgets
      expect(screen.getByTestId('chart-sales-chart')).toBeInTheDocument();
      expect(screen.getByTestId('chart-revenue-chart')).toBeInTheDocument();
      
      // Should apply correct chart types
      expect(screen.getByTestId('chart-sales-chart')).toHaveAttribute('data-chart-type', 'bar');
      expect(screen.getByTestId('chart-revenue-chart')).toHaveAttribute('data-chart-type', 'pie');
      
      // Should apply render configurations
      const salesChart = screen.getByTestId('chart-sales-chart');
      const salesRenderConfig = JSON.parse(salesChart.getAttribute('data-render-config') || '{}');
      expect(salesRenderConfig.aspectRatio).toBe(0.6);
      expect(salesRenderConfig.minHeight).toBe(200);
    });

    it('should handle legacy layout format with fallback', async () => {
      const { container } = render(
        <PageContent page={mockLegacyPage} />
      );

      // Should render with legacy component grid
      expect(container.querySelector('.component-grid')).toBeInTheDocument();
      
      // Should still render widgets
      expect(screen.getByText('Sales Chart')).toBeInTheDocument();
      expect(screen.getByText('Revenue Chart')).toBeInTheDocument();
    });

    it('should apply CSS classes from layout computation', async () => {
      const widgets = [
        {
          id: 'widget1',
          position: { x: 0, y: 0, w: 6, h: 2 },
          renderConfig: { aspectRatio: 0.6, minHeight: 200 },
          chartType: 'bar'
        }
      ];

      const layoutClasses = computeDashboardLayout(widgets);
      
      expect(layoutClasses.widget1.containerClasses).toContain('grid');
      expect(layoutClasses.widget1.containerClasses).toContain('grid-cols-1');
      expect(layoutClasses.widget1.containerClasses).toContain('md:grid-cols-2');
      
      expect(layoutClasses.widget1.itemClasses).toContain('col-span-6');
      expect(layoutClasses.widget1.itemClasses).toContain('row-span-2');
      expect(layoutClasses.widget1.itemClasses).toContain('aspect-[5/3]');
      
      expect(layoutClasses.widget1.customProperties['--grid-column-start']).toBe('1');
      expect(layoutClasses.widget1.customProperties['--grid-column-end']).toBe('7');
    });

    it('should validate layout configuration during rendering', async () => {
      const validationResult = validateLayoutConfig(mockNewSchemaPage.layout_config);
      
      expect(validationResult.isValid).toBe(true);
      expect(validationResult.errors).toHaveLength(0);
    });

    it('should handle invalid layout configuration gracefully', async () => {
      const validationResult = validateLayoutConfig(mockInvalidPage.layout_config);
      
      expect(validationResult.isValid).toBe(false);
      expect(validationResult.errors.length).toBeGreaterThan(0);
      
      // Should render error boundary
      render(
        <LayoutErrorBoundary>
          <PageContent page={mockInvalidPage} />
        </LayoutErrorBoundary>
      );
      
      // Should show fallback UI
      expect(screen.getByText(/layout error/i)).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior Across Breakpoints', () => {
    const breakpoints = [
      { width: 640, name: 'sm', expectedCols: 1 },
      { width: 768, name: 'md', expectedCols: 2 },
      { width: 1024, name: 'lg', expectedCols: 3 },
      { width: 1280, name: 'xl', expectedCols: 4 }
    ];

    breakpoints.forEach(({ width, name, expectedCols }) => {
      it(`should adapt layout for ${name} breakpoint (${width}px)`, async () => {
        // Set viewport width
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          configurable: true,
          value: width,
        });

        const { container } = render(
          <DashboardGrid 
            widgets={mockNewSchemaPage.layout_config.widgets}
            rows={mockNewSchemaPage.layout_config.rows}
            gridConfig={mockNewSchemaPage.layout_config.grid}
          />
        );

        // Trigger resize event
        act(() => {
          fireEvent(window, new Event('resize'));
        });

        await waitFor(() => {
          const gridElement = container.querySelector('.dashboard-grid');
          expect(gridElement).toHaveClass(`${name}:grid-cols-${expectedCols}`);
        });
      });
    });

    it('should handle smooth transitions between breakpoints', async () => {
      const { container } = render(
        <DashboardGrid 
          widgets={mockNewSchemaPage.layout_config.widgets}
          rows={mockNewSchemaPage.layout_config.rows}
          gridConfig={mockNewSchemaPage.layout_config.grid}
        />
      );

      const gridElement = container.querySelector('.dashboard-grid');
      
      // Should have transition classes
      expect(gridElement).toHaveClass('transition-all');
      expect(gridElement).toHaveClass('duration-300');
      expect(gridElement).toHaveClass('ease-in-out');
    });

    it('should stack components properly on mobile', async () => {
      // Set mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { container } = render(
        <DashboardGrid 
          widgets={mockNewSchemaPage.layout_config.widgets}
          rows={mockNewSchemaPage.layout_config.rows}
          gridConfig={mockNewSchemaPage.layout_config.grid}
        />
      );

      act(() => {
        fireEvent(window, new Event('resize'));
      });

      await waitFor(() => {
        const gridElement = container.querySelector('.dashboard-grid');
        expect(gridElement).toHaveClass('grid-cols-1');
        
        // All widgets should span full width on mobile
        const widgets = container.querySelectorAll('.grid-item');
        widgets.forEach(widget => {
          expect(widget).toHaveClass('col-span-12');
        });
      });
    });
  });

  describe('Error Handling and Fallback Scenarios', () => {
    it('should handle missing widget references gracefully', async () => {
      const pageWithMissingWidget: Page = {
        ...mockNewSchemaPage,
        layout_config: {
          ...mockNewSchemaPage.layout_config,
          rows: [
            {
              id: 'row1',
              rowHeight: 'auto',
              items: [
                { widgetRef: 'missing-widget', col: 1, span: 6, h: 2 }
              ]
            }
          ]
        } as DashboardLayout
      };

      render(
        <LayoutErrorBoundary>
          <PageContent page={pageWithMissingWidget} />
        </LayoutErrorBoundary>
      );

      // Should not crash and show error message
      expect(screen.getByText(/widget reference.*not found/i)).toBeInTheDocument();
    });

    it('should handle malformed layout configuration', async () => {
      const pageWithMalformedLayout: Page = {
        ...mockNewSchemaPage,
        layout_config: {
          // Malformed - missing required properties
          widgets: 'invalid' as any,
          rows: null as any
        }
      };

      render(
        <LayoutErrorBoundary>
          <PageContent page={pageWithMalformedLayout} />
        </LayoutErrorBoundary>
      );

      // Should show error boundary
      expect(screen.getByText(/layout error/i)).toBeInTheDocument();
    });

    it('should fallback to legacy grid when new schema fails', async () => {
      const pageWithPartialNewSchema: Page = {
        ...mockNewSchemaPage,
        layout_config: {
          // Has some new schema properties but is incomplete
          grid: mockNewSchemaPage.layout_config.grid,
          widgets: [], // Empty widgets array
          rows: []
        } as DashboardLayout
      };

      const { container } = render(
        <PageContent page={pageWithPartialNewSchema} />
      );

      // Should fallback to legacy component grid
      expect(container.querySelector('.component-grid')).toBeInTheDocument();
    });

    it('should handle component rendering errors', async () => {
      // Mock a component that throws an error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      
      const ErrorComponent = () => {
        throw new Error('Component rendering failed');
      };

      render(
        <LayoutErrorBoundary>
          <ErrorComponent />
        </LayoutErrorBoundary>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      jest.restoreAllMocks();
    });
  });

  describe('Layout Migration from Legacy to New Schema', () => {
    it('should detect legacy layout format correctly', async () => {
      const hasLegacyFormat = !mockLegacyPage.layout_config.grid && 
                             !mockLegacyPage.layout_config.widgets &&
                             mockLegacyPage.layout_config.components;
      
      expect(hasLegacyFormat).toBe(true);
    });

    it('should detect new schema format correctly', async () => {
      const hasNewSchema = mockNewSchemaPage.layout_config.grid && 
                          mockNewSchemaPage.layout_config.widgets &&
                          mockNewSchemaPage.layout_config.rows;
      
      expect(hasNewSchema).toBe(true);
    });

    it('should migrate legacy grid_position to new schema', async () => {
      const legacyComponent = {
        id: 'test-widget',
        grid_position: { x: 2, y: 1, w: 4, h: 2 },
        type: 'bar',
        title: 'Test Chart'
      };

      // Simulate migration logic
      const migratedWidget = {
        id: legacyComponent.id,
        type: legacyComponent.type,
        title: legacyComponent.title,
        dataSource: { componentId: 1 },
        render: {
          aspectRatio: 0.6, // Default for bar charts
          minHeight: 180,
          overflow: 'hidden'
        }
      };

      const migratedRow = {
        id: 'migrated-row-1',
        rowHeight: 'auto' as const,
        items: [
          {
            widgetRef: legacyComponent.id,
            col: legacyComponent.grid_position.x + 1, // Convert 0-based to 1-based
            span: legacyComponent.grid_position.w,
            h: legacyComponent.grid_position.h
          }
        ]
      };

      expect(migratedWidget.type).toBe('bar');
      expect(migratedWidget.render.aspectRatio).toBe(0.6);
      expect(migratedRow.items[0].col).toBe(3); // x=2 becomes col=3
      expect(migratedRow.items[0].span).toBe(4);
    });

    it('should preserve widget functionality during migration', async () => {
      // Test that migrated widgets still render correctly
      const migratedPage: Page = {
        ...mockLegacyPage,
        layout_config: {
          grid: {
            mode: 'strict-grid',
            gap: 4,
            breakpoints: {
              sm: { minWidth: 640, columns: 1, rowUnit: 80 },
              md: { minWidth: 768, columns: 2, rowUnit: 80 },
              lg: { minWidth: 1024, columns: 3, rowUnit: 80 },
              xl: { minWidth: 1280, columns: 4, rowUnit: 80 }
            }
          },
          widgets: [
            {
              id: 'widget1',
              type: 'bar',
              title: 'Sales Chart',
              dataSource: { componentId: 1 },
              render: { aspectRatio: 0.6, minHeight: 180 }
            }
          ],
          rows: [
            {
              id: 'row1',
              rowHeight: 'auto',
              items: [
                { widgetRef: 'widget1', col: 1, span: 6, h: 2 }
              ]
            }
          ]
        } as DashboardLayout
      };

      render(<PageContent page={migratedPage} />);
      
      expect(screen.getByText('Sales Chart')).toBeInTheDocument();
      expect(screen.getByTestId('chart-sales-chart')).toHaveAttribute('data-chart-type', 'bar');
    });
  });

  describe('Performance and Optimization', () => {
    it('should not re-render unnecessarily when props do not change', async () => {
      const renderSpy = jest.fn();
      
      const TestComponent = ({ page }: { page: Page }) => {
        renderSpy();
        return <PageContent page={page} />;
      };

      const { rerender } = render(<TestComponent page={mockNewSchemaPage} />);
      
      expect(renderSpy).toHaveBeenCalledTimes(1);
      
      // Re-render with same props
      rerender(<TestComponent page={mockNewSchemaPage} />);
      
      // Should not trigger unnecessary re-renders (this depends on memoization)
      expect(renderSpy).toHaveBeenCalledTimes(2); // React will re-render, but components should be memoized
    });

    it('should handle large numbers of widgets efficiently', async () => {
      const manyWidgets = Array.from({ length: 50 }, (_, i) => ({
        id: `widget-${i}`,
        type: 'bar',
        title: `Chart ${i}`,
        dataSource: { componentId: i },
        render: { aspectRatio: 0.6, minHeight: 180 }
      }));

      const manyRows = Array.from({ length: 10 }, (_, i) => ({
        id: `row-${i}`,
        rowHeight: 'auto' as const,
        items: Array.from({ length: 5 }, (_, j) => ({
          widgetRef: `widget-${i * 5 + j}`,
          col: j * 2 + 1,
          span: 2,
          h: 2
        }))
      }));

      const largePage: Page = {
        ...mockNewSchemaPage,
        layout_config: {
          ...mockNewSchemaPage.layout_config,
          widgets: manyWidgets,
          rows: manyRows
        } as DashboardLayout
      };

      const startTime = performance.now();
      
      render(<PageContent page={largePage} />);
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render within reasonable time (less than 1 second)
      expect(renderTime).toBeLessThan(1000);
      
      // Should render all widgets
      expect(screen.getAllByText(/Chart \d+/)).toHaveLength(50);
    });

    it('should cleanup event listeners on unmount', async () => {
      const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');
      
      const { unmount } = render(
        <DashboardGrid 
          widgets={mockNewSchemaPage.layout_config.widgets}
          rows={mockNewSchemaPage.layout_config.rows}
          gridConfig={mockNewSchemaPage.layout_config.grid}
        />
      );

      unmount();

      // Should cleanup resize listeners
      expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function));
      
      removeEventListenerSpy.mockRestore();
    });
  });

  describe('Accessibility and User Experience', () => {
    it('should provide proper ARIA labels for dashboard components', async () => {
      render(<PageContent page={mockNewSchemaPage} />);
      
      const dashboard = screen.getByRole('main');
      expect(dashboard).toHaveAttribute('aria-label', expect.stringContaining('dashboard'));
      
      const charts = screen.getAllByRole('img');
      charts.forEach(chart => {
        expect(chart).toHaveAttribute('aria-label');
      });
    });

    it('should support keyboard navigation', async () => {
      render(<PageContent page={mockNewSchemaPage} />);
      
      const firstChart = screen.getByTestId('chart-sales-chart');
      
      // Should be focusable
      expect(firstChart).toHaveAttribute('tabIndex', '0');
      
      // Should handle keyboard events
      fireEvent.keyDown(firstChart, { key: 'Enter' });
      fireEvent.keyDown(firstChart, { key: ' ' });
      
      // Should not throw errors
      expect(firstChart).toBeInTheDocument();
    });

    it('should provide loading states during data fetching', async () => {
      const LoadingComponent = () => (
        <div data-testid="loading-spinner">Loading...</div>
      );

      render(
        <React.Suspense fallback={<LoadingComponent />}>
          <PageContent page={mockNewSchemaPage} />
        </React.Suspense>
      );

      // Should show loading state initially
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('should handle high contrast mode', async () => {
      // Mock high contrast media query
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-contrast: high)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      const { container } = render(<PageContent page={mockNewSchemaPage} />);
      
      // Should apply high contrast styles
      const dashboard = container.querySelector('.dashboard-grid');
      expect(dashboard).toHaveClass('high-contrast');
    });
  });
});