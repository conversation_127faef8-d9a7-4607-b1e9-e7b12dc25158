/**
 * Responsive Layout Integration Tests
 * 
 * Tests for responsive behavior, breakpoint transitions, and mobile layout handling
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

import { DashboardGrid } from '../../components/DashboardGrid';
import { getCurrentBreakpoint, calculateOptimalGrid } from '../../utils/layoutUtils';
import { getResponsiveGridClasses, getMobileFirstColumnClasses } from '../../utils/gridUtils';
import { ResponsiveGridConfig } from '../../types/dashboard';

// Mock viewport utilities
const mockViewport = (width: number, height: number = 768) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
};

const mockMatchMedia = (matches: boolean = false) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
};

// Test data
const mockGridConfig: ResponsiveGridConfig = {
  sm: { columns: 1, minWidth: 640 },
  md: { columns: 2, minWidth: 768 },
  lg: { columns: 3, minWidth: 1024 },
  xl: { columns: 4, minWidth: 1280 }
};

const mockWidgets = [
  {
    id: 'widget1',
    type: 'bar',
    title: 'Sales Chart',
    dataSource: { componentId: 1 },
    render: { aspectRatio: 0.6, minHeight: 200 }
  },
  {
    id: 'widget2',
    type: 'pie',
    title: 'Revenue Chart', 
    dataSource: { componentId: 2 },
    render: { aspectRatio: 1.0, minHeight: 200 }
  },
  {
    id: 'widget3',
    type: 'line',
    title: 'Growth Chart',
    dataSource: { componentId: 3 },
    render: { aspectRatio: 0.5, minHeight: 160 }
  },
  {
    id: 'widget4',
    type: 'area',
    title: 'Trend Chart',
    dataSource: { componentId: 4 },
    render: { aspectRatio: 0.5, minHeight: 160 }
  }
];

const mockRows = [
  {
    id: 'row1',
    rowHeight: 'auto' as const,
    items: [
      { widgetRef: 'widget1', col: 1, span: 6, h: 2 },
      { widgetRef: 'widget2', col: 7, span: 6, h: 2 }
    ]
  },
  {
    id: 'row2', 
    rowHeight: 'auto' as const,
    items: [
      { widgetRef: 'widget3', col: 1, span: 6, h: 2 },
      { widgetRef: 'widget4', col: 7, span: 6, h: 2 }
    ]
  }
];

// Mock chart component
jest.mock('../../components/ChartComponent', () => ({
  ChartComponent: ({ title, type, renderConfig }: any) => (
    <div 
      data-testid={`chart-${title.replace(/\s+/g, '-').toLowerCase()}`}
      data-chart-type={type}
      className="mock-chart"
      style={{
        aspectRatio: renderConfig?.aspectRatio,
        minHeight: renderConfig?.minHeight
      }}
    >
      {title}
    </div>
  )
}));

describe('Responsive Layout Integration', () => {
  beforeEach(() => {
    mockViewport(1024, 768);
    mockMatchMedia(false);
  });

  describe('Breakpoint Detection and Transitions', () => {
    const breakpointTests = [
      { width: 320, expected: 'sm', description: 'mobile portrait' },
      { width: 640, expected: 'sm', description: 'mobile landscape' },
      { width: 768, expected: 'md', description: 'tablet portrait' },
      { width: 1024, expected: 'lg', description: 'tablet landscape' },
      { width: 1280, expected: 'xl', description: 'desktop' },
      { width: 1920, expected: 'xl', description: 'large desktop' }
    ];

    breakpointTests.forEach(({ width, expected, description }) => {
      it(`should detect ${expected} breakpoint for ${description} (${width}px)`, () => {
        const breakpoint = getCurrentBreakpoint(width, mockGridConfig);
        expect(breakpoint).toBe(expected);
      });
    });

    it('should handle custom breakpoint configuration', () => {
      const customConfig: ResponsiveGridConfig = {
        sm: { columns: 1, minWidth: 480 },
        md: { columns: 2, minWidth: 720 },
        lg: { columns: 4, minWidth: 960 },
        xl: { columns: 6, minWidth: 1200 }
      };

      expect(getCurrentBreakpoint(500, customConfig)).toBe('sm');
      expect(getCurrentBreakpoint(800, customConfig)).toBe('md');
      expect(getCurrentBreakpoint(1000, customConfig)).toBe('lg');
      expect(getCurrentBreakpoint(1400, customConfig)).toBe('xl');
    });

    it('should handle edge cases at breakpoint boundaries', () => {
      expect(getCurrentBreakpoint(639, mockGridConfig)).toBe('sm');
      expect(getCurrentBreakpoint(640, mockGridConfig)).toBe('sm');
      expect(getCurrentBreakpoint(767, mockGridConfig)).toBe('sm');
      expect(getCurrentBreakpoint(768, mockGridConfig)).toBe('md');
    });
  });

  describe('Grid Layout Adaptation', () => {
    it('should adapt grid columns based on viewport width', async () => {
      const { container, rerender } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      // Test different viewport sizes
      const viewportTests = [
        { width: 640, expectedClass: 'sm:grid-cols-1' },
        { width: 768, expectedClass: 'md:grid-cols-2' },
        { width: 1024, expectedClass: 'lg:grid-cols-3' },
        { width: 1280, expectedClass: 'xl:grid-cols-4' }
      ];

      for (const { width, expectedClass } of viewportTests) {
        mockViewport(width);
        
        rerender(
          <DashboardGrid 
            widgets={mockWidgets}
            rows={mockRows}
            gridConfig={mockGridConfig}
          />
        );

        act(() => {
          fireEvent(window, new Event('resize'));
        });

        await waitFor(() => {
          const gridElement = container.querySelector('.dashboard-grid');
          expect(gridElement).toHaveClass(expectedClass);
        });
      }
    });

    it('should generate responsive grid classes correctly', () => {
      const responsiveClasses = getResponsiveGridClasses(mockGridConfig);
      
      expect(responsiveClasses).toContain('grid');
      expect(responsiveClasses).toContain('grid-cols-1');
      expect(responsiveClasses).toContain('sm:grid-cols-1');
      expect(responsiveClasses).toContain('md:grid-cols-2');
      expect(responsiveClasses).toContain('lg:grid-cols-3');
      expect(responsiveClasses).toContain('xl:grid-cols-4');
      expect(responsiveClasses).toContain('transition-all');
      expect(responsiveClasses).toContain('duration-300');
    });

    it('should calculate optimal grid dimensions for different viewports', () => {
      const testCases = [
        { widgets: 4, viewport: 320, minWidth: 300, expected: { columns: 1, rows: 4 } },
        { widgets: 4, viewport: 768, minWidth: 300, expected: { columns: 2, rows: 2 } },
        { widgets: 4, viewport: 1024, minWidth: 300, expected: { columns: 3, rows: 2 } },
        { widgets: 4, viewport: 1280, minWidth: 300, expected: { columns: 4, rows: 1 } },
        { widgets: 6, viewport: 1200, minWidth: 300, expected: { columns: 4, rows: 2 } }
      ];

      testCases.forEach(({ widgets, viewport, minWidth, expected }) => {
        const result = calculateOptimalGrid(widgets, viewport, minWidth);
        expect(result).toEqual(expected);
      });
    });
  });

  describe('Mobile-First Responsive Design', () => {
    it('should apply mobile-first column classes', () => {
      const testCases = [
        { span: 3, expected: 'col-span-12 md:col-span-3 lg:col-span-3 xl:col-span-3' },
        { span: 6, expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3' },
        { span: 12, expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3' }
      ];

      testCases.forEach(({ span, expected }) => {
        const result = getMobileFirstColumnClasses(span);
        expect(result).toBe(expected);
      });
    });

    it('should stack widgets vertically on mobile', async () => {
      mockViewport(375); // iPhone width

      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      act(() => {
        fireEvent(window, new Event('resize'));
      });

      await waitFor(() => {
        const gridElement = container.querySelector('.dashboard-grid');
        expect(gridElement).toHaveClass('grid-cols-1');
        
        // All widgets should span full width
        const widgetElements = container.querySelectorAll('.grid-item');
        widgetElements.forEach(widget => {
          expect(widget).toHaveClass('col-span-12');
        });
      });
    });

    it('should apply responsive padding and spacing', async () => {
      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      const gridElement = container.querySelector('.dashboard-grid');
      
      // Should have responsive padding
      expect(gridElement).toHaveClass('px-4');
      expect(gridElement).toHaveClass('sm:px-6');
      expect(gridElement).toHaveClass('lg:px-8');
      
      // Should have responsive gap
      expect(gridElement).toHaveClass('gap-2');
      expect(gridElement).toHaveClass('sm:gap-3');
      expect(gridElement).toHaveClass('lg:gap-4');
    });
  });

  describe('Smooth Transitions and Animations', () => {
    it('should apply transition classes for smooth layout changes', async () => {
      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      const gridElement = container.querySelector('.dashboard-grid');
      
      expect(gridElement).toHaveClass('transition-all');
      expect(gridElement).toHaveClass('duration-300');
      expect(gridElement).toHaveClass('ease-in-out');
    });

    it('should handle rapid viewport changes smoothly', async () => {
      const { container, rerender } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      // Simulate rapid viewport changes
      const viewportSizes = [320, 768, 1024, 640, 1280, 375];
      
      for (const width of viewportSizes) {
        mockViewport(width);
        
        rerender(
          <DashboardGrid 
            widgets={mockWidgets}
            rows={mockRows}
            gridConfig={mockGridConfig}
          />
        );

        act(() => {
          fireEvent(window, new Event('resize'));
        });

        // Should not crash or cause layout thrashing
        const gridElement = container.querySelector('.dashboard-grid');
        expect(gridElement).toBeInTheDocument();
        expect(gridElement).toHaveClass('transition-all');
      }
    });

    it('should debounce resize events to prevent excessive re-renders', async () => {
      const resizeHandler = jest.fn();
      
      const TestComponent = () => {
        React.useEffect(() => {
          const debouncedHandler = debounce(resizeHandler, 100);
          window.addEventListener('resize', debouncedHandler);
          return () => window.removeEventListener('resize', debouncedHandler);
        }, []);

        return (
          <DashboardGrid 
            widgets={mockWidgets}
            rows={mockRows}
            gridConfig={mockGridConfig}
          />
        );
      };

      render(<TestComponent />);

      // Fire multiple resize events rapidly
      for (let i = 0; i < 10; i++) {
        fireEvent(window, new Event('resize'));
      }

      // Wait for debounce
      await waitFor(() => {
        expect(resizeHandler).toHaveBeenCalledTimes(1);
      }, { timeout: 200 });
    });
  });

  describe('Container Queries and Modern CSS Features', () => {
    it('should support container queries for component-level responsiveness', async () => {
      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      const widgetContainers = container.querySelectorAll('.chart-container');
      
      widgetContainers.forEach(widget => {
        expect(widget).toHaveClass('@container');
      });
    });

    it('should apply aspect ratio constraints correctly', async () => {
      render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      const barChart = screen.getByTestId('chart-sales-chart');
      const pieChart = screen.getByTestId('chart-revenue-chart');
      
      // Check computed styles
      expect(barChart).toHaveStyle({ aspectRatio: '0.6' });
      expect(pieChart).toHaveStyle({ aspectRatio: '1' });
    });

    it('should handle CSS Grid minmax() for flexible sizing', () => {
      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      const gridElement = container.querySelector('.dashboard-grid');
      const computedStyle = window.getComputedStyle(gridElement!);
      
      // Should use CSS Grid
      expect(computedStyle.display).toBe('grid');
    });
  });

  describe('Accessibility in Responsive Layouts', () => {
    it('should maintain focus order across breakpoints', async () => {
      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      const focusableElements = container.querySelectorAll('[tabindex="0"]');
      
      // Test focus order on desktop
      mockViewport(1280);
      act(() => {
        fireEvent(window, new Event('resize'));
      });

      // Test focus order on mobile
      mockViewport(375);
      act(() => {
        fireEvent(window, new Event('resize'));
      });

      // Focus order should remain logical
      focusableElements.forEach((element, index) => {
        expect(element).toHaveAttribute('tabindex', '0');
      });
    });

    it('should provide appropriate ARIA labels for responsive states', async () => {
      mockViewport(375);

      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      act(() => {
        fireEvent(window, new Event('resize'));
      });

      const gridElement = container.querySelector('.dashboard-grid');
      expect(gridElement).toHaveAttribute('aria-label', expect.stringContaining('mobile'));
    });

    it('should handle reduced motion preferences', async () => {
      // Mock prefers-reduced-motion
      mockMatchMedia(true);

      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      const gridElement = container.querySelector('.dashboard-grid');
      
      // Should disable transitions when reduced motion is preferred
      expect(gridElement).toHaveClass('motion-reduce:transition-none');
    });
  });

  describe('Performance Optimization', () => {
    it('should use CSS transforms for better performance', async () => {
      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      // Trigger layout change
      mockViewport(768);
      act(() => {
        fireEvent(window, new Event('resize'));
      });

      const widgetElements = container.querySelectorAll('.grid-item');
      
      widgetElements.forEach(widget => {
        const computedStyle = window.getComputedStyle(widget);
        // Should use hardware acceleration
        expect(computedStyle.willChange).toBe('transform');
      });
    });

    it('should minimize layout thrashing during transitions', async () => {
      const layoutSpy = jest.spyOn(HTMLElement.prototype, 'getBoundingClientRect');
      
      const { container } = render(
        <DashboardGrid 
          widgets={mockWidgets}
          rows={mockRows}
          gridConfig={mockGridConfig}
        />
      );

      // Trigger multiple rapid changes
      const sizes = [320, 768, 1024, 1280];
      
      for (const size of sizes) {
        mockViewport(size);
        act(() => {
          fireEvent(window, new Event('resize'));
        });
      }

      // Should not cause excessive layout calculations
      expect(layoutSpy).toHaveBeenCalledTimes(0);
      
      layoutSpy.mockRestore();
    });
  });
});

// Utility function for debouncing (would normally be imported)
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}