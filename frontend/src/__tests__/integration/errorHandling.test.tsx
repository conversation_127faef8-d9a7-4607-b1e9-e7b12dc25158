/**
 * Error Handling and Fallback Integration Tests
 * 
 * Tests for error boundaries, graceful degradation, and fallback scenarios
 * in the dashboard layout system.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';

import { LayoutErrorBoundary } from '../../components/LayoutErrorBoundary';
import { PageContent } from '../../components/PageContent';
import { DashboardGrid } from '../../components/DashboardGrid';
import { validateLayoutConfig, formatValidationResults } from '../../utils/layoutValidation';
import { computeLayoutClasses } from '../../utils/layoutUtils';
import { DashboardLayout, Page } from '../../types/dashboard';

// Mock console methods to avoid noise in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Test data with various error scenarios
const validPage: Page = {
  id: 1,
  title: 'Valid Dashboard',
  layout_config: {
    grid: {
      mode: 'strict-grid',
      gap: 4,
      breakpoints: {
        sm: { minWidth: 640, columns: 1, rowUnit: 80 },
        md: { minWidth: 768, columns: 2, rowUnit: 80 },
        lg: { minWidth: 1024, columns: 3, rowUnit: 80 },
        xl: { minWidth: 1280, columns: 4, rowUnit: 80 }
      }
    },
    widgets: [
      {
        id: 'widget1',
        type: 'bar',
        title: 'Sales Chart',
        dataSource: { componentId: 1 },
        render: { aspectRatio: 0.6, minHeight: 200 }
      }
    ],
    rows: [
      {
        id: 'row1',
        rowHeight: 'auto',
        items: [
          { widgetRef: 'widget1', col: 1, span: 6, h: 2 }
        ]
      }
    ]
  } as DashboardLayout,
  accessible_organizations: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const pageWithMissingWidgetRefs: Page = {
  ...validPage,
  title: 'Missing Widget References',
  layout_config: {
    ...validPage.layout_config,
    rows: [
      {
        id: 'row1',
        rowHeight: 'auto',
        items: [
          { widgetRef: 'missing-widget', col: 1, span: 6, h: 2 },
          { widgetRef: 'another-missing-widget', col: 7, span: 6, h: 2 }
        ]
      }
    ]
  } as DashboardLayout
};

const pageWithInvalidGrid: Page = {
  ...validPage,
  title: 'Invalid Grid Configuration',
  layout_config: {
    ...validPage.layout_config,
    grid: {
      mode: 'invalid-mode' as any,
      gap: -5, // Invalid negative gap
      breakpoints: {
        sm: { minWidth: -100, columns: 0, rowUnit: 0 }, // Invalid values
        md: { minWidth: 768, columns: 2, rowUnit: 80 }
        // Missing lg and xl breakpoints
      }
    }
  } as DashboardLayout
};

const pageWithMalformedLayout: Page = {
  ...validPage,
  title: 'Malformed Layout',
  layout_config: {
    // Missing required properties
    widgets: null as any,
    rows: 'invalid' as any
  }
};

const pageWithInvalidRenderConfig: Page = {
  ...validPage,
  title: 'Invalid Render Config',
  layout_config: {
    ...validPage.layout_config,
    widgets: [
      {
        id: 'widget1',
        type: 'bar',
        title: 'Invalid Widget',
        dataSource: { componentId: 1 },
        render: {
          aspectRatio: -1, // Invalid negative aspect ratio
          minHeight: -100, // Invalid negative height
          maxHeight: 50, // Max less than min
          overflow: 'invalid-overflow' as any
        }
      }
    ]
  } as DashboardLayout
};

const pageWithCircularReferences: Page = {
  ...validPage,
  title: 'Circular References',
  layout_config: {
    ...validPage.layout_config,
    widgets: [
      {
        id: 'widget1',
        type: 'bar',
        title: 'Widget 1',
        dataSource: { componentId: 1 },
        render: {}
      },
      {
        id: 'widget2',
        type: 'pie',
        title: 'Widget 2', 
        dataSource: { componentId: 2 },
        render: {}
      }
    ],
    rows: [
      {
        id: 'row1',
        rowHeight: 'auto',
        items: [
          { widgetRef: 'widget1', col: 1, span: 6, h: 2 },
          { widgetRef: 'widget1', col: 7, span: 6, h: 2 } // Duplicate reference
        ]
      }
    ]
  } as DashboardLayout
};

// Mock components that can throw errors
const ErrorThrowingChart = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Chart rendering failed');
  }
  return <div data-testid="working-chart">Working Chart</div>;
};

const AsyncErrorChart = () => {
  React.useEffect(() => {
    // Simulate async error
    setTimeout(() => {
      throw new Error('Async chart error');
    }, 100);
  }, []);
  
  return <div data-testid="async-chart">Async Chart</div>;
};

describe('Error Handling and Fallback Integration', () => {
  describe('Layout Validation Errors', () => {
    it('should detect and report missing widget references', () => {
      const result = validateLayoutConfig(pageWithMissingWidgetRefs.layout_config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: expect.stringContaining('widgetRef'),
          message: expect.stringContaining('not found')
        })
      );
    });

    it('should detect and report invalid grid configuration', () => {
      const result = validateLayoutConfig(pageWithInvalidGrid.layout_config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'grid.mode',
          message: expect.stringContaining('Invalid grid mode')
        })
      );
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'grid.gap',
          message: expect.stringContaining('cannot be negative')
        })
      );
    });

    it('should detect malformed layout structure', () => {
      const result = validateLayoutConfig(pageWithMalformedLayout.layout_config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: 'widgets',
          message: expect.stringContaining('must be an array')
        })
      );
    });

    it('should detect invalid render configurations', () => {
      const result = validateLayoutConfig(pageWithInvalidRenderConfig.layout_config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContainEqual(
        expect.objectContaining({
          field: expect.stringContaining('aspectRatio'),
          message: expect.stringContaining('greater than 0')
        })
      );
    });

    it('should format validation results for user display', () => {
      const result = validateLayoutConfig(pageWithInvalidGrid.layout_config);
      const formatted = formatValidationResults(result);
      
      expect(formatted).toContain('❌ Layout configuration has errors');
      expect(formatted).toContain('🚨 Errors:');
      expect(formatted).toContain('💡'); // Should contain suggestions
    });

    it('should provide helpful suggestions for common errors', () => {
      const result = validateLayoutConfig(pageWithMissingWidgetRefs.layout_config);
      
      const errorWithSuggestions = result.errors.find(error => 
        error.field.includes('widgetRef') && error.suggestions
      );
      
      expect(errorWithSuggestions?.suggestions).toContain(
        expect.stringContaining('Check if the widget ID is correct')
      );
    });
  });

  describe('Error Boundary Behavior', () => {
    it('should catch and display component rendering errors', () => {
      render(
        <LayoutErrorBoundary>
          <ErrorThrowingChart shouldThrow={true} />
        </LayoutErrorBoundary>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText(/try refreshing/i)).toBeInTheDocument();
    });

    it('should allow error recovery through retry mechanism', async () => {
      let shouldThrow = true;
      
      const { rerender } = render(
        <LayoutErrorBoundary>
          <ErrorThrowingChart shouldThrow={shouldThrow} />
        </LayoutErrorBoundary>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      // Simulate retry
      const retryButton = screen.getByText(/try again/i);
      
      shouldThrow = false;
      fireEvent.click(retryButton);
      
      rerender(
        <LayoutErrorBoundary>
          <ErrorThrowingChart shouldThrow={shouldThrow} />
        </LayoutErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByTestId('working-chart')).toBeInTheDocument();
      });
    });

    it('should log errors for debugging while showing user-friendly messages', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      render(
        <LayoutErrorBoundary>
          <ErrorThrowingChart shouldThrow={true} />
        </LayoutErrorBoundary>
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Layout Error:'),
        expect.any(Error)
      );
      
      // User should see friendly message, not technical error
      expect(screen.queryByText('Chart rendering failed')).not.toBeInTheDocument();
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });

    it('should handle nested error boundaries correctly', () => {
      render(
        <LayoutErrorBoundary>
          <div>
            <LayoutErrorBoundary>
              <ErrorThrowingChart shouldThrow={true} />
            </LayoutErrorBoundary>
            <div data-testid="sibling-content">Sibling Content</div>
          </div>
        </LayoutErrorBoundary>
      );

      // Inner error boundary should catch the error
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      // Sibling content should still render
      expect(screen.getByTestId('sibling-content')).toBeInTheDocument();
    });

    it('should handle async errors in components', async () => {
      // Mock error event handler
      const errorHandler = jest.fn();
      window.addEventListener('error', errorHandler);

      render(
        <LayoutErrorBoundary>
          <AsyncErrorChart />
        </LayoutErrorBoundary>
      );

      // Component should render initially
      expect(screen.getByTestId('async-chart')).toBeInTheDocument();

      // Wait for async error
      await waitFor(() => {
        expect(errorHandler).toHaveBeenCalled();
      }, { timeout: 200 });

      window.removeEventListener('error', errorHandler);
    });
  });

  describe('Graceful Degradation', () => {
    it('should fallback to legacy layout when new schema fails', () => {
      const pageWithPartialSchema: Page = {
        ...validPage,
        layout_config: {
          // Incomplete new schema
          grid: validPage.layout_config.grid,
          widgets: [], // Empty widgets
          rows: []
        } as DashboardLayout
      };

      const { container } = render(
        <LayoutErrorBoundary>
          <PageContent page={pageWithPartialSchema} />
        </LayoutErrorBoundary>
      );

      // Should fallback to legacy component grid
      expect(container.querySelector('.component-grid')).toBeInTheDocument();
    });

    it('should render partial layouts when some widgets fail', () => {
      const pageWithMixedWidgets: Page = {
        ...validPage,
        layout_config: {
          ...validPage.layout_config,
          widgets: [
            {
              id: 'working-widget',
              type: 'bar',
              title: 'Working Widget',
              dataSource: { componentId: 1 },
              render: { aspectRatio: 0.6 }
            },
            {
              id: 'broken-widget',
              type: 'invalid-type' as any,
              title: 'Broken Widget',
              dataSource: { componentId: 2 },
              render: { aspectRatio: -1 } // Invalid config
            }
          ],
          rows: [
            {
              id: 'row1',
              rowHeight: 'auto',
              items: [
                { widgetRef: 'working-widget', col: 1, span: 6, h: 2 },
                { widgetRef: 'broken-widget', col: 7, span: 6, h: 2 }
              ]
            }
          ]
        } as DashboardLayout
      };

      render(
        <LayoutErrorBoundary>
          <PageContent page={pageWithMixedWidgets} />
        </LayoutErrorBoundary>
      );

      // Working widget should render
      expect(screen.getByText('Working Widget')).toBeInTheDocument();
      
      // Broken widget should show error placeholder
      expect(screen.getByText(/widget error/i)).toBeInTheDocument();
    });

    it('should provide default render configurations for missing configs', () => {
      const config = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {}, // Empty render config
        chartType: 'bar'
      };

      const result = computeLayoutClasses(config);
      
      // Should apply default configurations
      expect(result.itemClasses).toContain('aspect-[5/3]'); // Default bar chart aspect ratio
      expect(result.itemClasses).toContain('min-h-[180px]'); // Default bar chart min height
    });

    it('should handle missing chart types gracefully', () => {
      const config = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {},
        chartType: 'unknown-chart-type'
      };

      const result = computeLayoutClasses(config);
      
      // Should apply fallback configurations
      expect(result.itemClasses).toContain('aspect-[7/10]'); // Fallback aspect ratio
      expect(result.itemClasses).toContain('min-h-[200px]'); // Fallback min height
    });
  });

  describe('Network and Data Loading Errors', () => {
    it('should handle API failures gracefully', async () => {
      // Mock failed API call
      const mockFetch = jest.spyOn(global, 'fetch').mockRejectedValue(
        new Error('Network error')
      );

      const LoadingComponent = () => {
        const [error, setError] = React.useState<string | null>(null);
        
        React.useEffect(() => {
          fetch('/api/dashboard/1')
            .catch(err => setError(err.message));
        }, []);

        if (error) {
          return <div data-testid="network-error">Failed to load dashboard: {error}</div>;
        }

        return <div data-testid="loading">Loading...</div>;
      };

      render(
        <LayoutErrorBoundary>
          <LoadingComponent />
        </LayoutErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByTestId('network-error')).toBeInTheDocument();
        expect(screen.getByText(/failed to load dashboard/i)).toBeInTheDocument();
      });

      mockFetch.mockRestore();
    });

    it('should show loading states during data fetching', async () => {
      const SlowLoadingComponent = () => {
        const [loading, setLoading] = React.useState(true);
        
        React.useEffect(() => {
          setTimeout(() => setLoading(false), 100);
        }, []);

        if (loading) {
          return <div data-testid="loading-spinner">Loading dashboard...</div>;
        }

        return <PageContent page={validPage} />;
      };

      render(<SlowLoadingComponent />);

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByText('Sales Chart')).toBeInTheDocument();
      });
    });

    it('should handle timeout scenarios', async () => {
      const TimeoutComponent = () => {
        const [timedOut, setTimedOut] = React.useState(false);
        
        React.useEffect(() => {
          const timeout = setTimeout(() => setTimedOut(true), 50);
          return () => clearTimeout(timeout);
        }, []);

        if (timedOut) {
          return <div data-testid="timeout-error">Request timed out</div>;
        }

        return <div data-testid="loading">Loading...</div>;
      };

      render(<TimeoutComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('timeout-error')).toBeInTheDocument();
      }, { timeout: 100 });
    });
  });

  describe('Memory and Performance Error Handling', () => {
    it('should handle large dataset rendering gracefully', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `widget-${i}`,
        type: 'bar',
        title: `Chart ${i}`,
        dataSource: { componentId: i },
        render: { aspectRatio: 0.6, minHeight: 200 }
      }));

      const LargeDatasetComponent = () => {
        const [error, setError] = React.useState<string | null>(null);
        
        try {
          return (
            <DashboardGrid 
              widgets={largeDataset}
              rows={[]}
              gridConfig={validPage.layout_config.grid}
            />
          );
        } catch (err) {
          setError((err as Error).message);
          return <div data-testid="memory-error">Memory error: {error}</div>;
        }
      };

      render(
        <LayoutErrorBoundary>
          <LargeDatasetComponent />
        </LayoutErrorBoundary>
      );

      // Should either render successfully or show appropriate error
      await waitFor(() => {
        const dashboard = screen.queryByRole('main');
        const error = screen.queryByTestId('memory-error');
        expect(dashboard || error).toBeInTheDocument();
      });
    });

    it('should cleanup resources on component unmount', () => {
      const cleanupSpy = jest.fn();
      
      const ComponentWithCleanup = () => {
        React.useEffect(() => {
          return cleanupSpy;
        }, []);

        return <PageContent page={validPage} />;
      };

      const { unmount } = render(
        <LayoutErrorBoundary>
          <ComponentWithCleanup />
        </LayoutErrorBoundary>
      );

      unmount();

      expect(cleanupSpy).toHaveBeenCalled();
    });
  });

  describe('User Experience During Errors', () => {
    it('should provide actionable error messages', () => {
      render(
        <LayoutErrorBoundary>
          <ErrorThrowingChart shouldThrow={true} />
        </LayoutErrorBoundary>
      );

      // Should provide clear actions user can take
      expect(screen.getByText(/try refreshing/i)).toBeInTheDocument();
      expect(screen.getByText(/try again/i)).toBeInTheDocument();
    });

    it('should maintain application state during partial failures', () => {
      const StatefulComponent = () => {
        const [count, setCount] = React.useState(0);
        
        return (
          <div>
            <button onClick={() => setCount(c => c + 1)}>
              Count: {count}
            </button>
            <LayoutErrorBoundary>
              <ErrorThrowingChart shouldThrow={count > 2} />
            </LayoutErrorBoundary>
          </div>
        );
      };

      render(<StatefulComponent />);

      const button = screen.getByText(/count: 0/i);
      
      // Increment counter
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button); // This should trigger error

      // State should be preserved
      expect(screen.getByText(/count: 3/i)).toBeInTheDocument();
      // Error should be shown
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
    });

    it('should provide error reporting mechanism', () => {
      const reportErrorSpy = jest.fn();
      
      const ErrorReportingBoundary = ({ children }: { children: React.ReactNode }) => {
        return (
          <LayoutErrorBoundary onError={reportErrorSpy}>
            {children}
          </LayoutErrorBoundary>
        );
      };

      render(
        <ErrorReportingBoundary>
          <ErrorThrowingChart shouldThrow={true} />
        </ErrorReportingBoundary>
      );

      expect(reportErrorSpy).toHaveBeenCalledWith(
        expect.any(Error),
        expect.any(Object)
      );
    });
  });
});