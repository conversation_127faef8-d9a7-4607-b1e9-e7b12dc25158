# Manual Theme Testing Checklist

This checklist provides comprehensive manual testing procedures for validating theme functionality across the Dynamic BI Dashboard application.

## Pre-Testing Setup

### Environment Preparation
- [ ] Clear browser cache and localStorage
- [ ] Ensure application is running in development mode
- [ ] Open browser developer tools (F12)
- [ ] Verify DaisyUI CSS is loaded (check Network tab)
- [ ] Test in multiple browsers: Chrome, Firefox, Safari, Edge

### Test Data Setup
- [ ] Create test user account with different roles (regular, org_admin, super_admin)
- [ ] Ensure test dashboard pages and components are available
- [ ] Verify BigQuery connection is working (if testing with real data)

## Theme Switching Functionality

### Basic Theme Selection
- [ ] **Open Theme Selector**
  - Navigate to dashboard
  - Click theme selector button/icon
  - Verify modal opens with all available themes

- [ ] **Theme Options Display**
  - Verify all 5 themes are shown: Corporate, Business, Dark, Retro, Cyberpunk
  - Check theme names are in Korean as expected
  - Verify theme descriptions are displayed
  - Confirm theme color previews are visible

- [ ] **Current Theme Indication**
  - Verify current theme is highlighted/marked
  - Check "현재 테마" badge is displayed on active theme
  - Confirm visual distinction from other themes

### Theme Application
- [ ] **Corporate Theme**
  - Select corporate theme
  - Verify immediate visual change
  - Check navbar, sidebar, main content areas
  - Confirm professional, clean appearance

- [ ] **Business Theme**
  - Switch to business theme
  - Verify modern business interface styling
  - Check color scheme consistency
  - Confirm all UI elements update

- [ ] **Dark Theme**
  - Switch to dark theme
  - Verify dark backgrounds throughout app
  - Check text remains readable (white/light text)
  - Confirm no light "flashes" or artifacts

- [ ] **Retro Theme**
  - Switch to retro theme
  - Verify vintage/classic styling
  - Check color scheme matches retro aesthetic
  - Confirm readability maintained

- [ ] **Cyberpunk Theme**
  - Switch to cyberpunk theme
  - Verify futuristic neon color scheme
  - Check high contrast elements
  - Confirm theme maintains usability

### Theme Persistence
- [ ] **Page Reload**
  - Select a theme (e.g., Dark)
  - Refresh the page (F5)
  - Verify theme persists after reload
  - Check localStorage contains correct theme

- [ ] **Browser Session**
  - Select a theme
  - Close browser tab
  - Open new tab and navigate to app
  - Verify theme is maintained

- [ ] **User Account Integration**
  - Login as authenticated user
  - Change theme
  - Logout and login again
  - Verify theme preference is saved to user account

## Component-Specific Testing

### Dashboard Layout
- [ ] **Main Dashboard Area**
  - Verify background uses theme colors (bg-base-100)
  - Check text uses theme-appropriate colors
  - Confirm cards use bg-base-200
  - Test user info section styling

- [ ] **Navigation Elements**
  - Check navbar theme integration
  - Verify page tabs use theme colors
  - Test hover states on navigation items
  - Confirm active page indication

- [ ] **Content Cards**
  - Verify card backgrounds (bg-base-200)
  - Check card text colors (text-base-content)
  - Test card borders and shadows
  - Confirm hover effects work

### Theme Selector Modal
- [ ] **Modal Appearance**
  - Verify modal uses theme colors (bg-base-100)
  - Check modal text color (text-base-content)
  - Test modal backdrop/overlay
  - Confirm modal positioning

- [ ] **Theme Cards**
  - Check theme option cards styling
  - Verify hover effects on theme cards
  - Test selected theme highlighting
  - Confirm color preview circles

- [ ] **Interactive Elements**
  - Test cancel button styling
  - Verify loading states during theme change
  - Check button hover/focus states
  - Confirm keyboard navigation works

### Form Elements
- [ ] **Input Fields**
  - Check input background colors
  - Verify input text colors
  - Test input border colors
  - Confirm focus states

- [ ] **Buttons**
  - Test primary button styling (btn-primary)
  - Check secondary button styling (btn-secondary)
  - Verify ghost button styling (btn-ghost)
  - Test button hover/focus states

- [ ] **Labels and Text**
  - Check form label colors
  - Verify help text colors
  - Test error message styling
  - Confirm placeholder text visibility

### Charts and Data Visualization
- [ ] **Chart Components**
  - Verify chart backgrounds match theme
  - Check chart text/label colors
  - Test chart grid line colors
  - Confirm chart legend styling

- [ ] **Data Tables**
  - Check table header styling
  - Verify table row backgrounds
  - Test alternating row colors
  - Confirm table border colors

## Accessibility Testing

### Color Contrast
- [ ] **WCAG AA Compliance**
  - Use browser accessibility tools
  - Check contrast ratios for all text
  - Verify minimum 4.5:1 for normal text
  - Confirm 3:1 for large text (18pt+)

- [ ] **Text Readability**
  - Test all themes in bright lighting
  - Test all themes in dim lighting
  - Verify readability on different screen types
  - Check secondary text (70% opacity) is readable

### Keyboard Navigation
- [ ] **Theme Selector**
  - Tab through theme options
  - Use Enter/Space to select themes
  - Test Escape to close modal
  - Verify focus indicators are visible

- [ ] **Focus Management**
  - Check focus outlines on all interactive elements
  - Verify focus doesn't get trapped
  - Test tab order is logical
  - Confirm focus is visible in all themes

### Screen Reader Compatibility
- [ ] **ARIA Labels**
  - Test with screen reader (NVDA, JAWS, VoiceOver)
  - Verify theme names are announced
  - Check modal is properly announced
  - Confirm button purposes are clear

## Responsive Design Testing

### Mobile Devices (320px - 768px)
- [ ] **Theme Selector on Mobile**
  - Test theme selector modal on small screens
  - Verify theme cards are properly sized
  - Check touch targets are adequate (44px min)
  - Test modal scrolling if needed

- [ ] **Dashboard on Mobile**
  - Verify theme colors on mobile layout
  - Check responsive grid behavior
  - Test navigation menu theming
  - Confirm readability on small screens

### Tablet Devices (768px - 1024px)
- [ ] **Medium Screen Layout**
  - Test theme consistency on tablet
  - Verify grid layouts work properly
  - Check navigation behavior
  - Test orientation changes

### Desktop (1024px+)
- [ ] **Large Screen Layout**
  - Test theme on wide screens
  - Verify sidebar theming
  - Check multi-column layouts
  - Test hover states on desktop

## Performance Testing

### Theme Switching Performance
- [ ] **Switch Speed**
  - Time theme switches (should be < 200ms)
  - Check for visual lag or flicker
  - Test rapid theme switching
  - Verify no memory leaks

- [ ] **CSS Loading**
  - Monitor network requests during theme change
  - Verify no additional CSS downloads
  - Check for CSS custom property updates
  - Test with slow network connections

### Browser Performance
- [ ] **Memory Usage**
  - Monitor memory usage during theme switches
  - Check for memory leaks over time
  - Test with multiple theme changes
  - Verify garbage collection works

## Error Handling Testing

### Network Issues
- [ ] **Offline Theme Changes**
  - Disconnect network
  - Try changing themes
  - Verify local changes still work
  - Check error handling for server updates

### Invalid States
- [ ] **Corrupted localStorage**
  - Manually corrupt theme data in localStorage
  - Reload application
  - Verify fallback to default theme
  - Check error recovery

- [ ] **Invalid Theme Data**
  - Set invalid theme in localStorage
  - Reload application
  - Verify fallback behavior
  - Check console for appropriate warnings

## Cross-Browser Testing

### Chrome
- [ ] Test all themes in Chrome
- [ ] Verify CSS custom properties work
- [ ] Check developer tools show correct theme
- [ ] Test Chrome-specific features

### Firefox
- [ ] Test all themes in Firefox
- [ ] Verify CSS compatibility
- [ ] Check Firefox developer tools
- [ ] Test Firefox-specific behaviors

### Safari
- [ ] Test all themes in Safari
- [ ] Verify WebKit compatibility
- [ ] Check Safari developer tools
- [ ] Test iOS Safari if possible

### Edge
- [ ] Test all themes in Edge
- [ ] Verify Chromium compatibility
- [ ] Check Edge developer tools
- [ ] Test Edge-specific features

## Integration Testing

### User Authentication
- [ ] **Login/Logout**
  - Test theme persistence through login
  - Verify theme loads from user preferences
  - Check theme updates save to server
  - Test guest vs authenticated user behavior

### API Integration
- [ ] **Theme API Calls**
  - Monitor network requests for theme updates
  - Verify API responses are handled correctly
  - Test API error scenarios
  - Check retry mechanisms

## Final Validation

### Complete User Journey
- [ ] **New User Experience**
  - Clear all data (incognito mode)
  - Navigate to application
  - Verify default theme loads
  - Test first theme change

- [ ] **Returning User Experience**
  - Login as existing user
  - Verify saved theme loads
  - Test theme changes persist
  - Check cross-device consistency

### Documentation Verification
- [ ] **Theme Names and Descriptions**
  - Verify all theme names match requirements
  - Check descriptions are accurate
  - Confirm Korean translations are correct
  - Test theme preview accuracy

## Test Results Documentation

### Pass/Fail Tracking
- [ ] Document any failing tests
- [ ] Note browser-specific issues
- [ ] Record performance measurements
- [ ] Capture screenshots of issues

### Issue Reporting
- [ ] Create bug reports for failures
- [ ] Include reproduction steps
- [ ] Attach screenshots/videos
- [ ] Specify browser and OS versions

---

## Testing Notes

**Estimated Testing Time:** 3-4 hours for complete checklist
**Recommended Frequency:** Before each release, after theme-related changes
**Required Tools:** Multiple browsers, accessibility tools, performance monitors
**Test Environment:** Development and staging environments

**Critical Issues:** Any failures in basic theme switching, accessibility violations, or data persistence should be considered blocking issues.

**Nice-to-Have Issues:** Minor visual inconsistencies, performance optimizations, or edge case handling can be addressed in future iterations.