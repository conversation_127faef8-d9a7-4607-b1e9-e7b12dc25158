import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Theme<PERSON>rovider, ThemeKey, AVAILABLE_THEMES } from '../../contexts/ThemeContext';

// Mock localStorage for tests
export const createMockLocalStorage = () => {
  const store: Record<string, string> = {};
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
    get store() {
      return { ...store };
    }
  };
};

// Mock console methods to reduce test noise
export const mockConsole = () => {
  const originalConsole = { ...console };
  
  beforeAll(() => {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  });

  afterAll(() => {
    Object.assign(console, originalConsole);
  });

  return originalConsole;
};

// Custom render function with ThemeProvider
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  theme?: ThemeKey;
  wrapper?: React.ComponentType<any>;
}

export const renderWithTheme = (
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { theme = 'corporate', wrapper: Wrapper, ...renderOptions } = options;

  const ThemeWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const content = <ThemeProvider defaultTheme={theme}>{children}</ThemeProvider>;
    
    if (Wrapper) {
      return <Wrapper>{content}</Wrapper>;
    }
    
    return content;
  };

  return render(ui, { wrapper: ThemeWrapper, ...renderOptions });
};

// Utility to test all themes
export const testAllThemes = (
  testFn: (theme: ThemeKey, themeName: string) => void | Promise<void>
) => {
  AVAILABLE_THEMES.forEach(theme => {
    describe(`Theme: ${theme.name} (${theme.key})`, () => {
      testFn(theme.key, theme.name);
    });
  });
};

// Utility to wait for theme application
export const waitForThemeApplication = async (expectedTheme: ThemeKey, timeout = 1000) => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (document.documentElement.getAttribute('data-theme') === expectedTheme) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  
  throw new Error(`Theme "${expectedTheme}" was not applied within ${timeout}ms`);
};

// Utility to capture element styles
export const captureElementStyles = (element: Element) => {
  const styles = window.getComputedStyle(element);
  return {
    backgroundColor: styles.backgroundColor,
    color: styles.color,
    borderColor: styles.borderColor,
    boxShadow: styles.boxShadow,
    fontSize: styles.fontSize,
    fontWeight: styles.fontWeight,
    padding: styles.padding,
    margin: styles.margin,
    opacity: styles.opacity,
    display: styles.display,
    visibility: styles.visibility,
  };
};

// Utility to get DaisyUI CSS custom properties
export const getThemeCustomProperties = () => {
  const documentStyles = window.getComputedStyle(document.documentElement);
  return {
    primary: documentStyles.getPropertyValue('--p').trim(),
    secondary: documentStyles.getPropertyValue('--s').trim(),
    accent: documentStyles.getPropertyValue('--a').trim(),
    neutral: documentStyles.getPropertyValue('--n').trim(),
    base100: documentStyles.getPropertyValue('--b1').trim(),
    base200: documentStyles.getPropertyValue('--b2').trim(),
    base300: documentStyles.getPropertyValue('--b3').trim(),
    baseContent: documentStyles.getPropertyValue('--bc').trim(),
    info: documentStyles.getPropertyValue('--in').trim(),
    success: documentStyles.getPropertyValue('--su').trim(),
    warning: documentStyles.getPropertyValue('--wa').trim(),
    error: documentStyles.getPropertyValue('--er').trim(),
  };
};

// Color contrast calculation utilities
export const getLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};

export const getContrastRatio = (
  color1: [number, number, number], 
  color2: [number, number, number]
): number => {
  const lum1 = getLuminance(...color1);
  const lum2 = getLuminance(...color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
};

// Parse RGB color string to RGB values
export const parseRgb = (rgbString: string): [number, number, number] | null => {
  // Handle rgb() format
  let match = rgbString.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (match) {
    return [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
  }
  
  // Handle rgba() format
  match = rgbString.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/);
  if (match) {
    return [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
  }
  
  // Handle hex format
  match = rgbString.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
  if (match) {
    return [
      parseInt(match[1], 16),
      parseInt(match[2], 16),
      parseInt(match[3], 16)
    ];
  }
  
  return null;
};

// Check if contrast ratio meets WCAG standards
export const meetsWCAGContrast = (
  color1: [number, number, number], 
  color2: [number, number, number],
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean => {
  const ratio = getContrastRatio(color1, color2);
  
  if (level === 'AAA') {
    return size === 'large' ? ratio >= 4.5 : ratio >= 7;
  } else {
    return size === 'large' ? ratio >= 3 : ratio >= 4.5;
  }
};

// Mock API responses for theme testing
export const createMockApiClient = () => ({
  updateUserTheme: jest.fn().mockResolvedValue({
    user: { id: '1', ui_theme: 'dark' }
  }),
  logout: jest.fn().mockResolvedValue(undefined),
  getAccessiblePages: jest.fn().mockResolvedValue({
    pages: [
      { id: 1, name: 'Test Dashboard', permission_level: 'regular' }
    ],
    user_role: 'regular'
  }),
  getPageComponents: jest.fn().mockResolvedValue([]),
});

// Mock auth context for theme testing
export const createMockAuthContext = (overrides = {}) => ({
  user: {
    id: '1',
    email: '<EMAIL>',
    role: 'regular',
    organization: 'Test Org',
    ui_theme: 'corporate',
    ...overrides
  },
  updateUser: jest.fn(),
  logout: jest.fn(),
  isAuthenticated: true,
});

// Utility to simulate viewport changes
export const setViewport = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
  
  // Trigger resize event
  window.dispatchEvent(new Event('resize'));
};

// Common viewport sizes for testing
export const VIEWPORT_SIZES = {
  mobile: { width: 320, height: 568 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1024, height: 768 },
  largeDesktop: { width: 1920, height: 1080 },
};

// Utility to test element visibility
export const isElementVisible = (element: Element): boolean => {
  const styles = window.getComputedStyle(element);
  return (
    styles.display !== 'none' &&
    styles.visibility !== 'hidden' &&
    styles.opacity !== '0' &&
    element.getBoundingClientRect().width > 0 &&
    element.getBoundingClientRect().height > 0
  );
};

// Utility to wait for CSS transitions to complete
export const waitForTransition = (element: Element, property?: string, timeout = 1000): Promise<void> => {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`Transition timeout after ${timeout}ms`));
    }, timeout);

    const handleTransitionEnd = (event: TransitionEvent) => {
      if (!property || event.propertyName === property) {
        clearTimeout(timeoutId);
        element.removeEventListener('transitionend', handleTransitionEnd);
        resolve();
      }
    };

    element.addEventListener('transitionend', handleTransitionEnd);
    
    // If no transition is running, resolve immediately
    const styles = window.getComputedStyle(element);
    if (styles.transitionDuration === '0s') {
      clearTimeout(timeoutId);
      element.removeEventListener('transitionend', handleTransitionEnd);
      resolve();
    }
  });
};

// Utility to create a test component with theme switching
export const createThemeSwitchTestComponent = (
  content: React.ReactNode,
  initialTheme: ThemeKey = 'corporate'
) => {
  return function ThemeSwitchTestComponent() {
    const [currentTheme, setCurrentTheme] = React.useState<ThemeKey>(initialTheme);
    
    return (
      <ThemeProvider defaultTheme={currentTheme}>
        <div data-testid="theme-container" data-theme={currentTheme}>
          {content}
          <div data-testid="theme-controls">
            {AVAILABLE_THEMES.map(theme => (
              <button
                key={theme.key}
                data-testid={`set-theme-${theme.key}`}
                onClick={() => setCurrentTheme(theme.key)}
              >
                Set {theme.name}
              </button>
            ))}
          </div>
          <div data-testid="current-theme">{currentTheme}</div>
        </div>
      </ThemeProvider>
    );
  };
};