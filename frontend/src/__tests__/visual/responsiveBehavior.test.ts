/**
 * Responsive Layout Behavior Visual Tests
 * 
 * Tests for responsive grid behavior, breakpoint transitions,
 * and mobile-first design implementation.
 */

import {
  getCurrentBreakpoint,
  calculateOptimalGrid,
  DEFAULT_GRID_CONFIG
} from '../../utils/layoutUtils';

import {
  getResponsiveGridClasses,
  getMobileFirstColumnClasses,
  getMobileFirstPaddingClasses,
  getMobileFirstContainerClasses,
  getResponsiveColumnSpan,
  type ResponsiveGridConfig
} from '../../utils/gridUtils';

describe('Visual Tests - Responsive Layout Behavior', () => {
  describe('Breakpoint Detection and Consistency', () => {
    it('should detect breakpoints consistently across viewport sizes', () => {
      const testCases = [
        // Mobile portrait
        { width: 320, expected: 'sm', description: 'mobile portrait' },
        { width: 375, expected: 'sm', description: 'iPhone portrait' },
        { width: 414, expected: 'sm', description: 'iPhone Plus portrait' },
        
        // Mobile landscape / Small tablet
        { width: 640, expected: 'sm', description: 'mobile landscape (boundary)' },
        { width: 667, expected: 'sm', description: 'iPhone landscape' },
        { width: 736, expected: 'sm', description: 'iPhone Plus landscape' },
        
        // Tablet portrait
        { width: 768, expected: 'md', description: 'tablet portrait (boundary)' },
        { width: 800, expected: 'md', description: 'small tablet' },
        { width: 834, expected: 'md', description: 'iPad portrait' },
        
        // Tablet landscape / Small desktop
        { width: 1024, expected: 'lg', description: 'tablet landscape (boundary)' },
        { width: 1112, expected: 'lg', description: 'iPad landscape' },
        { width: 1200, expected: 'lg', description: 'small desktop' },
        
        // Desktop
        { width: 1280, expected: 'xl', description: 'desktop (boundary)' },
        { width: 1440, expected: 'xl', description: 'standard desktop' },
        { width: 1920, expected: 'xl', description: 'full HD' },
        { width: 2560, expected: 'xl', description: '4K desktop' }
      ];
      
      testCases.forEach(({ width, expected, description }) => {
        const breakpoint = getCurrentBreakpoint(width, DEFAULT_GRID_CONFIG);
        expect(breakpoint).toBe(expected);
        
        // Test consistency - same width should always return same breakpoint
        const breakpoint2 = getCurrentBreakpoint(width, DEFAULT_GRID_CONFIG);
        expect(breakpoint2).toBe(breakpoint);
      });
    });

    it('should handle custom breakpoint configurations', () => {
      const customConfig: ResponsiveGridConfig = {
        sm: { columns: 1, minWidth: 480 },
        md: { columns: 3, minWidth: 720 },
        lg: { columns: 5, minWidth: 960 },
        xl: { columns: 8, minWidth: 1200 }
      };
      
      const testCases = [
        { width: 400, expected: 'sm' },
        { width: 480, expected: 'sm' },
        { width: 720, expected: 'md' },
        { width: 960, expected: 'lg' },
        { width: 1200, expected: 'xl' },
        { width: 1600, expected: 'xl' }
      ];
      
      testCases.forEach(({ width, expected }) => {
        const breakpoint = getCurrentBreakpoint(width, customConfig);
        expect(breakpoint).toBe(expected);
      });
    });

    it('should handle edge cases at exact breakpoint boundaries', () => {
      const boundaryTests = [
        { width: 639, expected: 'sm' }, // Just below md
        { width: 640, expected: 'sm' }, // Exactly at sm boundary
        { width: 767, expected: 'sm' }, // Just below md
        { width: 768, expected: 'md' }, // Exactly at md boundary
        { width: 1023, expected: 'md' }, // Just below lg
        { width: 1024, expected: 'lg' }, // Exactly at lg boundary
        { width: 1279, expected: 'lg' }, // Just below xl
        { width: 1280, expected: 'xl' } // Exactly at xl boundary
      ];
      
      boundaryTests.forEach(({ width, expected }) => {
        const breakpoint = getCurrentBreakpoint(width, DEFAULT_GRID_CONFIG);
        expect(breakpoint).toBe(expected);
      });
    });
  });

  describe('Responsive Grid Class Generation', () => {
    it('should generate consistent responsive grid classes', () => {
      const classes = getResponsiveGridClasses(DEFAULT_GRID_CONFIG);
      
      // Base grid classes
      expect(classes).toContain('grid');
      expect(classes).toContain('w-full');
      expect(classes).toContain('max-w-none');
      expect(classes).toContain('overflow-hidden');
      expect(classes).toContain('min-h-0');
      
      // Responsive gap classes
      expect(classes).toContain('gap-2');
      expect(classes).toContain('sm:gap-3');
      expect(classes).toContain('lg:gap-4');
      
      // Responsive padding classes
      expect(classes).toContain('px-4');
      expect(classes).toContain('sm:px-6');
      expect(classes).toContain('lg:px-8');
      
      // Transition classes
      expect(classes).toContain('transition-all');
      expect(classes).toContain('duration-300');
      expect(classes).toContain('ease-in-out');
      
      // Column classes
      expect(classes).toContain('grid-cols-1');
      expect(classes).toContain('sm:grid-cols-1');
      expect(classes).toContain('md:grid-cols-2');
      expect(classes).toContain('lg:grid-cols-3');
      expect(classes).toContain('xl:grid-cols-4');
    });

    it('should generate responsive classes for custom configurations', () => {
      const customConfig: ResponsiveGridConfig = {
        sm: { columns: 1, minWidth: 640 },
        md: { columns: 3, minWidth: 768 },
        lg: { columns: 6, minWidth: 1024 },
        xl: { columns: 8, minWidth: 1280 }
      };
      
      const classes = getResponsiveGridClasses(customConfig);
      
      expect(classes).toContain('md:grid-cols-3');
      expect(classes).toContain('lg:grid-cols-6');
      expect(classes).toContain('xl:grid-cols-8');
    });

    it('should maintain class order for CSS specificity', () => {
      const classes = getResponsiveGridClasses(DEFAULT_GRID_CONFIG);
      const classArray = classes.split(' ');
      
      // Base classes should come before responsive variants
      const gridColsIndex = classArray.indexOf('grid-cols-1');
      const smGridColsIndex = classArray.indexOf('sm:grid-cols-1');
      const mdGridColsIndex = classArray.indexOf('md:grid-cols-2');
      const lgGridColsIndex = classArray.indexOf('lg:grid-cols-3');
      const xlGridColsIndex = classArray.indexOf('xl:grid-cols-4');
      
      expect(gridColsIndex).toBeLessThan(smGridColsIndex);
      expect(smGridColsIndex).toBeLessThan(mdGridColsIndex);
      expect(mdGridColsIndex).toBeLessThan(lgGridColsIndex);
      expect(lgGridColsIndex).toBeLessThan(xlGridColsIndex);
    });
  });

  describe('Mobile-First Column Behavior', () => {
    it('should generate mobile-first column classes correctly', () => {
      const testCases = [
        {
          originalSpan: 1,
          expected: 'col-span-12 md:col-span-1 lg:col-span-1 xl:col-span-1'
        },
        {
          originalSpan: 3,
          expected: 'col-span-12 md:col-span-3 lg:col-span-3 xl:col-span-3'
        },
        {
          originalSpan: 6,
          expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3'
        },
        {
          originalSpan: 9,
          expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3'
        },
        {
          originalSpan: 12,
          expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3'
        }
      ];
      
      testCases.forEach(({ originalSpan, expected }) => {
        const result = getMobileFirstColumnClasses(originalSpan);
        expect(result).toBe(expected);
      });
    });

    it('should handle edge cases in mobile-first column generation', () => {
      const edgeCases = [
        { span: 0, expected: 'col-span-12 md:col-span-0 lg:col-span-0 xl:col-span-0' }, // Zero span behavior
        { span: undefined, expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3' }, // Default behavior
        { span: 24, expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3' } // Clamped to max
      ];
      
      edgeCases.forEach(({ span, expected }) => {
        const result = getMobileFirstColumnClasses(span);
        expect(result).toBe(expected);
      });
    });

    it('should generate consistent mobile-first padding classes', () => {
      const paddingClasses = getMobileFirstPaddingClasses();
      expect(paddingClasses).toBe('p-2 sm:p-3 lg:p-4');
      
      // Should be consistent across calls
      const paddingClasses2 = getMobileFirstPaddingClasses();
      expect(paddingClasses2).toBe(paddingClasses);
    });

    it('should generate consistent mobile-first container classes', () => {
      const containerClasses = getMobileFirstContainerClasses();
      expect(containerClasses).toBe('w-full max-w-none overflow-hidden min-h-0');
      
      // Should be consistent across calls
      const containerClasses2 = getMobileFirstContainerClasses();
      expect(containerClasses2).toBe(containerClasses);
    });
  });

  describe('Responsive Column Span Calculations', () => {
    it('should calculate responsive column spans correctly', () => {
      const testCases = [
        // Original span 12 (full width)
        { originalSpan: 12, breakpoint: 'sm' as const, maxCols: 12, expected: 12 },
        { originalSpan: 12, breakpoint: 'md' as const, maxCols: 12, expected: 10 }, // 12 * 0.8 = 9.6, ceil = 10
        { originalSpan: 12, breakpoint: 'lg' as const, maxCols: 12, expected: 9 },  // 12 * 0.7 = 8.4, ceil = 9
        { originalSpan: 12, breakpoint: 'xl' as const, maxCols: 12, expected: 8 },  // 12 * 0.6 = 7.2, ceil = 8
        
        // Original span 6 (half width)
        { originalSpan: 6, breakpoint: 'sm' as const, maxCols: 12, expected: 6 },
        { originalSpan: 6, breakpoint: 'md' as const, maxCols: 12, expected: 5 }, // 6 * 0.8 = 4.8, ceil = 5
        { originalSpan: 6, breakpoint: 'lg' as const, maxCols: 12, expected: 5 }, // 6 * 0.7 = 4.2, ceil = 5
        { originalSpan: 6, breakpoint: 'xl' as const, maxCols: 12, expected: 4 }, // 6 * 0.6 = 3.6, ceil = 4
        
        // Test with different max columns
        { originalSpan: 8, breakpoint: 'md' as const, maxCols: 6, expected: 6 }, // Clamped to max
        { originalSpan: 2, breakpoint: 'lg' as const, maxCols: 12, expected: 2 }, // 2 * 0.7 = 1.4, ceil = 2
      ];
      
      testCases.forEach(({ originalSpan, breakpoint, maxCols, expected }) => {
        const result = getResponsiveColumnSpan(originalSpan, breakpoint, maxCols);
        expect(result).toBe(expected);
      });
    });

    it('should ensure minimum span of 1', () => {
      const testCases = [
        { originalSpan: 1, breakpoint: 'xl' as const, maxCols: 12 },
        { originalSpan: 0, breakpoint: 'lg' as const, maxCols: 12 },
        { originalSpan: -1, breakpoint: 'md' as const, maxCols: 12 }
      ];
      
      testCases.forEach(({ originalSpan, breakpoint, maxCols }) => {
        const result = getResponsiveColumnSpan(originalSpan, breakpoint, maxCols);
        expect(result).toBeGreaterThanOrEqual(1);
      });
    });

    it('should respect maximum column constraints', () => {
      const testCases = [
        { originalSpan: 20, breakpoint: 'sm' as const, maxCols: 4, expected: 4 },
        { originalSpan: 15, breakpoint: 'md' as const, maxCols: 6, expected: 6 },
        { originalSpan: 10, breakpoint: 'lg' as const, maxCols: 3, expected: 3 }
      ];
      
      testCases.forEach(({ originalSpan, breakpoint, maxCols, expected }) => {
        const result = getResponsiveColumnSpan(originalSpan, breakpoint, maxCols);
        expect(result).toBe(expected);
      });
    });
  });

  describe('Optimal Grid Calculations for Responsive Design', () => {
    it('should calculate optimal grids for different viewport sizes', () => {
      const testCases = [
        // Mobile viewports
        { widgets: 4, viewport: 320, minWidth: 300, expected: { columns: 1, rows: 4 } },
        { widgets: 6, viewport: 375, minWidth: 300, expected: { columns: 1, rows: 6 } },
        { widgets: 8, viewport: 414, minWidth: 300, expected: { columns: 1, rows: 8 } },
        
        // Tablet viewports
        { widgets: 4, viewport: 768, minWidth: 300, expected: { columns: 2, rows: 2 } },
        { widgets: 6, viewport: 834, minWidth: 300, expected: { columns: 2, rows: 3 } },
        { widgets: 9, viewport: 1024, minWidth: 300, expected: { columns: 3, rows: 3 } },
        
        // Desktop viewports
        { widgets: 8, viewport: 1280, minWidth: 300, expected: { columns: 4, rows: 2 } },
        { widgets: 12, viewport: 1440, minWidth: 300, expected: { columns: 4, rows: 3 } },
        { widgets: 16, viewport: 1920, minWidth: 300, expected: { columns: 6, rows: 3 } }
      ];
      
      testCases.forEach(({ widgets, viewport, minWidth, expected }) => {
        const result = calculateOptimalGrid(widgets, viewport, minWidth);
        expect(result).toEqual(expected);
      });
    });

    it('should handle different minimum column widths', () => {
      const testCases = [
        // Narrow columns (more fit)
        { widgets: 6, viewport: 1200, minWidth: 200, expected: { columns: 6, rows: 1 } },
        
        // Standard columns
        { widgets: 6, viewport: 1200, minWidth: 300, expected: { columns: 4, rows: 2 } },
        
        // Wide columns (fewer fit)
        { widgets: 6, viewport: 1200, minWidth: 400, expected: { columns: 3, rows: 2 } },
        
        // Very wide columns
        { widgets: 6, viewport: 1200, minWidth: 600, expected: { columns: 2, rows: 3 } }
      ];
      
      testCases.forEach(({ widgets, viewport, minWidth, expected }) => {
        const result = calculateOptimalGrid(widgets, viewport, minWidth);
        expect(result).toEqual(expected);
      });
    });

    it('should handle edge cases in grid calculations', () => {
      const edgeCases = [
        // No widgets - this is a known edge case that returns NaN
        { widgets: 0, viewport: 1200, minWidth: 300, expectedCols: 1, expectedRows: NaN },
        
        // Single widget
        { widgets: 1, viewport: 1200, minWidth: 300, expectedCols: 1, expectedRows: 1 },
        
        // Very small viewport - results in Infinity rows due to Math.ceil(4/0)
        { widgets: 4, viewport: 200, minWidth: 300, expectedCols: 1, expectedRows: Infinity },
        
        // Very large viewport
        { widgets: 4, viewport: 3000, minWidth: 300, expectedCols: 4, expectedRows: 1 }
      ];
      
      edgeCases.forEach(({ widgets, viewport, minWidth, expectedCols, expectedRows }) => {
        const result = calculateOptimalGrid(widgets, viewport, minWidth);
        expect(result.columns).toBe(expectedCols);
        
        if (expectedRows !== undefined) {
          if (widgets === 0) {
            // Special case: 0 widgets results in NaN due to Math.ceil(0/0)
            expect(result.rows).toBeNaN();
          } else if (expectedRows === Infinity) {
            // Special case: small viewport results in Infinity rows
            expect(result.rows).toBe(Infinity);
          } else {
            expect(result.rows).toBe(expectedRows);
          }
        }
      });
    });
  });

  describe('Responsive Design Consistency', () => {
    it('should maintain consistent responsive behavior across multiple calls', () => {
      const viewport = 1024;
      const widgets = 8;
      const minWidth = 300;
      
      // Multiple calls should return identical results
      const results = Array.from({ length: 5 }, () => 
        calculateOptimalGrid(widgets, viewport, minWidth)
      );
      
      results.forEach(result => {
        expect(result).toEqual(results[0]);
      });
    });

    it('should generate consistent responsive classes across multiple calls', () => {
      const config = DEFAULT_GRID_CONFIG;
      
      // Multiple calls should return identical results
      const classResults = Array.from({ length: 5 }, () => 
        getResponsiveGridClasses(config)
      );
      
      classResults.forEach(classes => {
        expect(classes).toBe(classResults[0]);
      });
    });

    it('should maintain breakpoint consistency across different configurations', () => {
      const viewports = [320, 768, 1024, 1280, 1920];
      
      viewports.forEach(viewport => {
        // Multiple calls with same viewport should return same breakpoint
        const breakpoints = Array.from({ length: 3 }, () => 
          getCurrentBreakpoint(viewport, DEFAULT_GRID_CONFIG)
        );
        
        breakpoints.forEach(breakpoint => {
          expect(breakpoint).toBe(breakpoints[0]);
        });
      });
    });
  });

  describe('Performance and Efficiency', () => {
    it('should calculate responsive layouts efficiently', () => {
      const startTime = performance.now();
      
      // Perform many responsive calculations
      for (let i = 0; i < 1000; i++) {
        const viewport = 320 + (i % 1600); // Vary viewport from 320 to 1920
        const widgets = 1 + (i % 20); // Vary widget count from 1 to 20
        
        getCurrentBreakpoint(viewport);
        calculateOptimalGrid(widgets, viewport, 300);
        getResponsiveColumnSpan(6, 'md', 12);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time
      expect(duration).toBeLessThan(100); // 100ms for 1000 calculations
    });

    it('should generate responsive classes efficiently', () => {
      const startTime = performance.now();
      
      // Generate many responsive class sets
      for (let i = 0; i < 100; i++) {
        getResponsiveGridClasses(DEFAULT_GRID_CONFIG);
        getMobileFirstColumnClasses(6);
        getMobileFirstPaddingClasses();
        getMobileFirstContainerClasses();
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time
      expect(duration).toBeLessThan(50); // 50ms for 100 generations
    });
  });

  describe('Accessibility in Responsive Design', () => {
    it('should maintain logical tab order in responsive layouts', () => {
      // Test that responsive column classes don't break logical ordering
      const widgets = [
        { id: 'widget1', originalSpan: 6 },
        { id: 'widget2', originalSpan: 6 },
        { id: 'widget3', originalSpan: 4 },
        { id: 'widget4', originalSpan: 4 },
        { id: 'widget5', originalSpan: 4 }
      ];
      
      widgets.forEach(widget => {
        const classes = getMobileFirstColumnClasses(widget.originalSpan);
        
        // Should always start with full width on mobile for logical stacking
        expect(classes.startsWith('col-span-12')).toBe(true);
        
        // Should have responsive overrides
        expect(classes).toMatch(/md:col-span-\d+/);
        expect(classes).toMatch(/lg:col-span-\d+/);
        expect(classes).toMatch(/xl:col-span-\d+/);
      });
    });

    it('should provide appropriate spacing for touch targets on mobile', () => {
      const paddingClasses = getMobileFirstPaddingClasses();
      
      // Should start with adequate padding for touch targets
      expect(paddingClasses).toContain('p-2'); // 8px minimum
      
      // Should increase padding on larger screens
      expect(paddingClasses).toContain('sm:p-3'); // 12px on small screens
      expect(paddingClasses).toContain('lg:p-4'); // 16px on large screens
    });

    it('should ensure readable content at all breakpoints', () => {
      const containerClasses = getMobileFirstContainerClasses();
      
      // Should prevent horizontal overflow
      expect(containerClasses).toContain('overflow-hidden');
      
      // Should use full width for maximum readability
      expect(containerClasses).toContain('w-full');
      
      // Should prevent flex item shrinking issues
      expect(containerClasses).toContain('min-h-0');
    });
  });
});