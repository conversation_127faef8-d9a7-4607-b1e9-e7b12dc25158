/**
 * Visual Regression Tests for Layout Consistency
 * 
 * Tests for CSS class generation, layout calculations, and visual consistency
 * across different configurations and breakpoints.
 */

import {
  computeLayoutClasses,
  computeDashboardLayout,
  getCurrentBreakpoint,
  calculateOptimalGrid,
  DEFAULT_GRID_CONFIG
} from '../../utils/layoutUtils';

import {
  getGridColumnClass,
  getGridRowClass,
  getResponsiveGridClasses,
  getMobileFirstColumnClasses,
  validateGridPosition,
  getGridCustomProperties
} from '../../utils/gridUtils';

import {
  getSizeConstraints,
  getAspectRatioClass,
  getHeightConstraintClasses,
  getChartTypeConstraints,
  CHART_TYPE_CONSTRAINTS
} from '../../utils/sizeConstraints';

import {
  getRenderConfigClasses,
  getDefaultRenderConfig,
  mergeRenderConfig
} from '../../utils/renderConfigUtils';

import { validateLayoutConfig } from '../../utils/layoutValidation';

describe('Visual Regression Tests - Layout Consistency', () => {
  describe('CSS Class Generation Consistency', () => {
    it('should generate consistent grid classes for identical positions', () => {
      const position1 = { x: 2, y: 1, w: 4, h: 2 };
      const position2 = { x: 2, y: 1, w: 4, h: 2 };
      
      const classes1 = getGridColumnClass(position1, 12);
      const classes2 = getGridColumnClass(position2, 12);
      
      expect(classes1).toBe(classes2);
      expect(classes1).toBe('col-start-3 col-span-4');
    });

    it('should generate consistent responsive grid classes', () => {
      const classes1 = getResponsiveGridClasses(DEFAULT_GRID_CONFIG);
      const classes2 = getResponsiveGridClasses(DEFAULT_GRID_CONFIG);
      
      expect(classes1).toBe(classes2);
      
      // Should contain all expected responsive classes
      const expectedClasses = [
        'grid',
        'w-full',
        'max-w-none',
        'overflow-hidden',
        'min-h-0',
        'gap-2',
        'sm:gap-3',
        'lg:gap-4',
        'px-4',
        'sm:px-6',
        'lg:px-8',
        'transition-all',
        'duration-300',
        'ease-in-out',
        'grid-cols-1',
        'sm:grid-cols-1',
        'md:grid-cols-2',
        'lg:grid-cols-3',
        'xl:grid-cols-4'
      ];
      
      expectedClasses.forEach(className => {
        expect(classes1).toContain(className);
      });
    });

    it('should generate consistent aspect ratio classes', () => {
      const testCases = [
        { ratio: 1.0, expected: 'aspect-square' },
        { ratio: 1.77, expected: 'aspect-video' },
        { ratio: 0.75, expected: 'aspect-[4/3]' },
        { ratio: 0.6, expected: 'aspect-[5/3]' },
        { ratio: 1.25, expected: 'aspect-[100/125]' }
      ];
      
      testCases.forEach(({ ratio, expected }) => {
        const result1 = getAspectRatioClass(ratio);
        const result2 = getAspectRatioClass(ratio);
        
        expect(result1).toBe(result2);
        expect(result1).toBe(expected);
      });
    });

    it('should generate consistent height constraint classes', () => {
      const renderConfig = {
        minHeight: 200,
        maxHeight: 400,
        fixedHeight: 300
      };
      
      const classes1 = getHeightConstraintClasses(renderConfig);
      const classes2 = getHeightConstraintClasses(renderConfig);
      
      expect(classes1).toBe(classes2);
      expect(classes1).toBe('h-[300px]'); // Fixed height takes precedence
    });

    it('should generate consistent mobile-first column classes', () => {
      const testCases = [
        { span: 3, expected: 'col-span-12 md:col-span-3 lg:col-span-3 xl:col-span-3' },
        { span: 6, expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3' },
        { span: 12, expected: 'col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3' }
      ];
      
      testCases.forEach(({ span, expected }) => {
        const result1 = getMobileFirstColumnClasses(span);
        const result2 = getMobileFirstColumnClasses(span);
        
        expect(result1).toBe(result2);
        expect(result1).toBe(expected);
      });
    });
  });

  describe('Layout Calculation Consistency', () => {
    it('should produce consistent layout classes for identical configurations', () => {
      const config = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {
          aspectRatio: 0.6,
          minHeight: 200,
          overflow: 'hidden' as const
        },
        chartType: 'bar'
      };
      
      const result1 = computeLayoutClasses(config);
      const result2 = computeLayoutClasses(config);
      
      expect(result1.containerClasses).toBe(result2.containerClasses);
      expect(result1.itemClasses).toBe(result2.itemClasses);
      expect(result1.contentClasses).toBe(result2.contentClasses);
      expect(result1.customProperties).toEqual(result2.customProperties);
    });

    it('should produce consistent dashboard layouts for identical widget sets', () => {
      const widgets = [
        {
          id: 'widget1',
          position: { x: 0, y: 0, w: 6, h: 2 },
          renderConfig: { aspectRatio: 1.0 },
          chartType: 'pie'
        },
        {
          id: 'widget2',
          position: { x: 6, y: 0, w: 6, h: 2 },
          renderConfig: { aspectRatio: 0.6 },
          chartType: 'bar'
        }
      ];
      
      const layout1 = computeDashboardLayout(widgets);
      const layout2 = computeDashboardLayout(widgets);
      
      expect(layout1).toEqual(layout2);
      
      // Verify specific widget layouts
      expect(layout1.widget1.itemClasses).toContain('col-span-6');
      expect(layout1.widget1.itemClasses).toContain('aspect-square');
      expect(layout1.widget2.itemClasses).toContain('col-start-7');
      expect(layout1.widget2.itemClasses).toContain('aspect-[5/3]');
    });

    it('should produce consistent breakpoint detection', () => {
      const testCases = [
        { width: 320, expected: 'sm' },
        { width: 640, expected: 'sm' },
        { width: 768, expected: 'md' },
        { width: 1024, expected: 'lg' },
        { width: 1280, expected: 'xl' },
        { width: 1920, expected: 'xl' }
      ];
      
      testCases.forEach(({ width, expected }) => {
        const result1 = getCurrentBreakpoint(width);
        const result2 = getCurrentBreakpoint(width);
        
        expect(result1).toBe(result2);
        expect(result1).toBe(expected);
      });
    });

    it('should produce consistent optimal grid calculations', () => {
      const testCases = [
        { widgets: 4, viewport: 1200, minWidth: 300, expected: { columns: 4, rows: 1 } },
        { widgets: 6, viewport: 1200, minWidth: 300, expected: { columns: 4, rows: 2 } },
        { widgets: 8, viewport: 800, minWidth: 300, expected: { columns: 2, rows: 4 } }
      ];
      
      testCases.forEach(({ widgets, viewport, minWidth, expected }) => {
        const result1 = calculateOptimalGrid(widgets, viewport, minWidth);
        const result2 = calculateOptimalGrid(widgets, viewport, minWidth);
        
        expect(result1).toEqual(result2);
        expect(result1).toEqual(expected);
      });
    });
  });

  describe('Chart Type Constraint Consistency', () => {
    it('should produce consistent constraints for chart types', () => {
      const chartTypes = Object.keys(CHART_TYPE_CONSTRAINTS);
      
      chartTypes.forEach(chartType => {
        const constraints1 = getChartTypeConstraints(chartType);
        const constraints2 = getChartTypeConstraints(chartType);
        
        expect(constraints1).toEqual(constraints2);
        expect(constraints1).toEqual(CHART_TYPE_CONSTRAINTS[chartType]);
      });
    });

    it('should produce consistent merged render configurations', () => {
      const customConfig = {
        aspectRatio: 2.0,
        minHeight: 250,
        overflow: 'scroll' as const
      };
      
      const merged1 = mergeRenderConfig('pie', customConfig);
      const merged2 = mergeRenderConfig('pie', customConfig);
      
      expect(merged1).toEqual(merged2);
      
      // Should merge correctly
      expect(merged1.aspectRatio).toBe(2.0); // Custom override
      expect(merged1.minHeight).toBe(250); // Custom override
      expect(merged1.overflow).toBe('scroll'); // Custom override
    });

    it('should produce consistent default render configurations', () => {
      const chartTypes = ['pie', 'bar', 'line', 'area', 'radar', 'gauge'];
      
      chartTypes.forEach(chartType => {
        const config1 = getDefaultRenderConfig(chartType);
        const config2 = getDefaultRenderConfig(chartType);
        
        expect(config1).toEqual(config2);
        
        // Should have required properties
        expect(typeof config1.aspectRatio).toBe('number');
        expect(typeof config1.minHeight).toBe('number');
        expect(typeof config1.autoHeight).toBe('boolean');
      });
    });
  });

  describe('Render Configuration Class Consistency', () => {
    it('should produce consistent render config classes', () => {
      const renderConfig = {
        aspectRatio: 1.0,
        minHeight: 200,
        maxHeight: 400,
        overflow: 'hidden' as const,
        padding: 16
      };
      
      const result1 = getRenderConfigClasses(renderConfig);
      const result2 = getRenderConfigClasses(renderConfig);
      
      expect(result1.classes).toBe(result2.classes);
      expect(result1.customProperties).toEqual(result2.customProperties);
      
      // Should contain expected classes
      expect(result1.classes).toContain('aspect-square');
      expect(result1.classes).toContain('min-h-50'); // 200px / 4 = 50
      expect(result1.classes).toContain('max-h-[400px]');
      expect(result1.classes).toContain('overflow-hidden');
      expect(result1.classes).toContain('p-4'); // 16px / 4 = 4
    });

    it('should handle edge cases consistently', () => {
      const edgeCases = [
        { config: {}, expectedClasses: ['h-auto', 'overflow-hidden'] },
        { config: { aspectRatio: 0 }, expectedClasses: ['h-auto', 'overflow-hidden'] }, // aspectRatio 0 is falsy
        { config: { minHeight: 0 }, expectedClasses: ['h-auto', 'overflow-hidden'] },
        { config: { padding: 0 }, expectedClasses: ['h-auto', 'overflow-hidden'] }
      ];
      
      edgeCases.forEach(({ config, expectedClasses }) => {
        const result1 = getRenderConfigClasses(config);
        const result2 = getRenderConfigClasses(config);
        
        expect(result1.classes).toBe(result2.classes);
        
        expectedClasses.forEach(className => {
          expect(result1.classes).toContain(className);
        });
      });
    });
  });

  describe('Grid Position Validation Consistency', () => {
    it('should produce consistent validated positions', () => {
      const testCases = [
        {
          input: { x: -1, y: 0, w: 4, h: 2 },
          expected: { x: 0, y: 0, w: 4, h: 2 }
        },
        {
          input: { x: 10, y: 0, w: 5, h: 2 },
          expected: { x: 10, y: 0, w: 2, h: 2 } // w adjusted to fit
        },
        {
          input: { x: 0, y: 0, w: 0, h: 0 },
          expected: { x: 0, y: 0, w: 1, h: 0 } // h: 0 is not adjusted by validateGridPosition
        }
      ];
      
      testCases.forEach(({ input, expected }) => {
        const result1 = validateGridPosition(input, 12);
        const result2 = validateGridPosition(input, 12);
        
        expect(result1).toEqual(result2);
        expect(result1).toEqual(expected);
      });
    });

    it('should produce consistent custom properties', () => {
      const position = { x: 2, y: 1, w: 4, h: 2 };
      
      const props1 = getGridCustomProperties(position, 12);
      const props2 = getGridCustomProperties(position, 12);
      
      expect(props1).toEqual(props2);
      expect(props1).toEqual({
        '--grid-column-start': '3',
        '--grid-column-end': '7',
        '--grid-row-start': '2',
        '--grid-row-end': '4',
        '--grid-total-columns': '12'
      });
    });
  });

  describe('Layout Validation Consistency', () => {
    it('should produce consistent validation results', () => {
      const validLayout = {
        grid: {
          mode: 'strict-grid',
          gap: 4,
          breakpoints: {
            sm: { minWidth: 640, columns: 1, rowUnit: 80 },
            md: { minWidth: 768, columns: 2, rowUnit: 80 },
            lg: { minWidth: 1024, columns: 3, rowUnit: 80 },
            xl: { minWidth: 1280, columns: 4, rowUnit: 80 }
          }
        },
        widgets: [
          {
            id: 'widget1',
            type: 'bar',
            title: 'Test Widget',
            dataSource: { componentId: 1 },
            render: { aspectRatio: 0.6, minHeight: 200 }
          }
        ],
        rows: [
          {
            id: 'row1',
            rowHeight: 'auto' as const,
            items: [
              { widgetRef: 'widget1', col: 1, span: 6, h: 2 }
            ]
          }
        ]
      };
      
      const result1 = validateLayoutConfig(validLayout);
      const result2 = validateLayoutConfig(validLayout);
      
      expect(result1).toEqual(result2);
      expect(result1.isValid).toBe(true);
      expect(result1.errors).toHaveLength(0);
    });

    it('should produce consistent error detection', () => {
      const invalidLayout = {
        grid: {
          mode: 'invalid-mode' as any,
          gap: -5,
          breakpoints: {}
        },
        widgets: [],
        rows: []
      };
      
      const result1 = validateLayoutConfig(invalidLayout);
      const result2 = validateLayoutConfig(invalidLayout);
      
      expect(result1).toEqual(result2);
      expect(result1.isValid).toBe(false);
      expect(result1.errors.length).toBeGreaterThan(0);
      
      // Should detect specific errors consistently
      const modeError = result1.errors.find(e => e.field === 'grid.mode');
      const gapError = result1.errors.find(e => e.field === 'grid.gap');
      
      expect(modeError).toBeDefined();
      expect(gapError).toBeDefined();
    });
  });

  describe('Size Constraint Integration Consistency', () => {
    it('should produce consistent size constraints', () => {
      const renderConfig = {
        aspectRatio: 1.0,
        minHeight: 200,
        maxHeight: 400,
        overflow: 'hidden' as const,
        padding: 16
      };
      
      const constraints1 = getSizeConstraints(renderConfig);
      const constraints2 = getSizeConstraints(renderConfig);
      
      expect(constraints1).toEqual(constraints2);
      
      // Verify individual components
      expect(constraints1.aspectRatioClass).toBe('aspect-square');
      expect(constraints1.heightClasses).toBe('min-h-[200px] max-h-[400px]');
      expect(constraints1.overflowClasses).toBe('overflow-hidden');
      expect(constraints1.paddingClasses).toBe('p-4');
      expect(constraints1.customProperties['--aspect-ratio']).toBe('1');
    });

    it('should handle complex integration scenarios consistently', () => {
      const complexConfig = {
        position: { x: 2, y: 1, w: 6, h: 3 },
        renderConfig: {
          aspectRatio: 0.75,
          minHeight: 180,
          maxHeight: 360,
          autoHeight: true,
          overflow: 'scroll' as const,
          padding: 12
        },
        chartType: 'heatmap',
        responsive: true,
        debug: false
      };
      
      const result1 = computeLayoutClasses(complexConfig);
      const result2 = computeLayoutClasses(complexConfig);
      
      expect(result1).toEqual(result2);
      
      // Verify complex integration
      expect(result1.itemClasses).toContain('col-start-3');
      expect(result1.itemClasses).toContain('col-span-6');
      expect(result1.itemClasses).toContain('row-span-3');
      expect(result1.itemClasses).toContain('aspect-[4/3]');
      expect(result1.itemClasses).toContain('h-auto');
      expect(result1.itemClasses).toContain('min-h-[180px]'); // Chart type override (heatmap default)
      expect(result1.itemClasses).toContain('overflow-auto');
      expect(result1.itemClasses).toContain('p-3');
    });
  });

  describe('Cross-Browser Consistency', () => {
    it('should generate CSS classes that work across browsers', () => {
      const position = { x: 0, y: 0, w: 6, h: 2 };
      const classes = getGridColumnClass(position, 12);
      
      // Should use standard CSS Grid classes
      expect(classes).toBe('col-span-6');
      expect(classes).not.toContain('webkit');
      expect(classes).not.toContain('moz');
      expect(classes).not.toContain('ms');
    });

    it('should generate aspect ratio classes that degrade gracefully', () => {
      const aspectRatios = [1.0, 0.75, 0.6, 1.77];
      
      aspectRatios.forEach(ratio => {
        const className = getAspectRatioClass(ratio);
        
        // Should use modern aspect-ratio property
        expect(className).toMatch(/^aspect-/);
        
        // Should not use legacy padding-bottom hacks
        expect(className).not.toContain('pb-');
        expect(className).not.toContain('padding');
      });
    });

    it('should generate height classes that work with CSS Grid', () => {
      const renderConfig = {
        minHeight: 200,
        maxHeight: 400,
        fixedHeight: 300
      };
      
      const classes = getHeightConstraintClasses(renderConfig);
      
      // Should use modern height constraints
      expect(classes).toBe('h-[300px]');
      expect(classes).not.toContain('line-height');
      expect(classes).not.toContain('vertical-align');
    });
  });

  describe('Performance Consistency', () => {
    it('should generate classes efficiently for large datasets', () => {
      const startTime = performance.now();
      
      // Generate classes for 100 widgets
      const widgets = Array.from({ length: 100 }, (_, i) => ({
        id: `widget-${i}`,
        position: { x: i % 12, y: Math.floor(i / 12), w: 2, h: 2 },
        renderConfig: {
          aspectRatio: 0.6 + (i % 5) * 0.1,
          minHeight: 180 + (i % 3) * 20
        },
        chartType: ['bar', 'line', 'pie', 'area'][i % 4]
      }));
      
      const layout = computeDashboardLayout(widgets);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time
      expect(duration).toBeLessThan(100); // 100ms
      expect(Object.keys(layout)).toHaveLength(100);
      
      // All widgets should have consistent structure
      Object.values(layout).forEach(widgetLayout => {
        expect(widgetLayout).toHaveProperty('containerClasses');
        expect(widgetLayout).toHaveProperty('itemClasses');
        expect(widgetLayout).toHaveProperty('contentClasses');
        expect(widgetLayout).toHaveProperty('customProperties');
      });
    });

    it('should cache repeated calculations', () => {
      const config = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: { aspectRatio: 0.6, minHeight: 200 },
        chartType: 'bar'
      };
      
      // First calculation
      const start1 = performance.now();
      const result1 = computeLayoutClasses(config);
      const end1 = performance.now();
      const duration1 = end1 - start1;
      
      // Second calculation (should be faster if cached)
      const start2 = performance.now();
      const result2 = computeLayoutClasses(config);
      const end2 = performance.now();
      const duration2 = end2 - start2;
      
      // Results should be identical
      expect(result1).toEqual(result2);
      
      // Second calculation should not be significantly slower
      // (Note: This is a basic test - real caching would show more improvement)
      expect(duration2).toBeLessThanOrEqual(duration1 * 2);
    });
  });
});