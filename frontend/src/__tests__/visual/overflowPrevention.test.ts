/**
 * Overflow Prevention Visual Tests
 * 
 * Tests to ensure components are properly contained within their grid boundaries
 * and that overflow is handled correctly across different configurations.
 */

import {
  computeLayoutClasses
} from '../../utils/layoutUtils';

import {
  validateLayoutConfig
} from '../../utils/layoutValidation';

import {
  getGridColumnClass,
  getGridRowClass,
  validateGridPosition,
  getGridCustomProperties
} from '../../utils/gridUtils';

import {
  getSizeConstraints,
  getHeightConstraintClasses,
  getOverflowClasses
} from '../../utils/sizeConstraints';

import {
  getRenderConfigClasses
} from '../../utils/renderConfigUtils';

describe('Visual Tests - Overflow Prevention', () => {
  describe('Grid Boundary Enforcement', () => {
    it('should prevent components from extending beyond grid columns', () => {
      const testCases = [
        { x: 10, w: 5, totalCols: 12, expectedW: 2 }, // Should clamp width
        { x: 11, w: 3, totalCols: 12, expectedW: 1 }, // Should clamp width
        { x: 12, w: 1, totalCols: 12, expectedX: 11, expectedW: 1 }, // Should clamp position
        { x: -1, w: 4, totalCols: 12, expectedX: 0, expectedW: 4 } // Should clamp negative position
      ];
      
      testCases.forEach(({ x, w, totalCols, expectedW, expectedX }) => {
        const position = { x, y: 0, w, h: 2 };
        const validated = validateGridPosition(position, totalCols);
        
        if (expectedX !== undefined) {
          expect(validated.x).toBe(expectedX);
        }
        if (expectedW !== undefined) {
          expect(validated.w).toBe(expectedW);
        }
        
        // Ensure position + width never exceeds total columns
        expect(validated.x + validated.w).toBeLessThanOrEqual(totalCols);
      });
    });

    it('should generate correct column classes that respect grid boundaries', () => {
      const testCases = [
        { x: 0, w: 12, expected: 'col-span-12' }, // Full width
        { x: 0, w: 6, expected: 'col-span-6' }, // Half width
        { x: 6, w: 6, expected: 'col-start-7 col-span-6' }, // Second half
        { x: 11, w: 1, expected: 'col-start-12 col-span-1' } // Last column
      ];
      
      testCases.forEach(({ x, w, expected }) => {
        const position = { x, y: 0, w, h: 2 };
        const classes = getGridColumnClass(position, 12);
        expect(classes).toBe(expected);
      });
    });

    it('should enforce minimum and maximum row spans', () => {
      const testCases = [
        { h: 0, expected: '' }, // Zero height should not generate row span
        { h: 1, expected: '' }, // Single row should not generate row span
        { h: 3, expected: 'row-span-3' }, // Multi-row should generate span
        { h: 10, maxRows: 6, expected: 'row-span-6' } // Should clamp to max
      ];
      
      testCases.forEach(({ h, maxRows, expected }) => {
        const position = { x: 0, y: 0, w: 6, h };
        const classes = getGridRowClass(position, maxRows);
        expect(classes).toBe(expected);
      });
    });

    it('should generate CSS custom properties that respect boundaries', () => {
      const position = { x: 2, y: 1, w: 4, h: 2 };
      const properties = getGridCustomProperties(position, 12);
      
      expect(properties).toEqual({
        '--grid-column-start': '3', // x + 1
        '--grid-column-end': '7', // x + w + 1
        '--grid-row-start': '2', // y + 1
        '--grid-row-end': '4', // y + h + 1
        '--grid-total-columns': '12'
      });
      
      // Verify boundaries
      const colStart = parseInt(properties['--grid-column-start']);
      const colEnd = parseInt(properties['--grid-column-end']);
      const totalCols = parseInt(properties['--grid-total-columns']);
      
      expect(colStart).toBeGreaterThanOrEqual(1);
      expect(colEnd).toBeLessThanOrEqual(totalCols + 1);
      expect(colStart).toBeLessThan(colEnd);
    });
  });

  describe('Content Overflow Handling', () => {
    it('should apply correct overflow classes for different scenarios', () => {
      const testCases = [
        { overflow: 'hidden', expected: 'overflow-hidden' },
        { overflow: 'scroll', expected: 'overflow-auto' },
        { overflow: 'visible', expected: 'overflow-visible' },
        { overflow: undefined, expected: 'overflow-hidden' }, // Default
        { overflow: 'invalid', expected: 'overflow-hidden' } // Fallback
      ];
      
      testCases.forEach(({ overflow, expected }) => {
        const classes = getOverflowClasses(overflow);
        expect(classes).toBe(expected);
      });
    });

    it('should combine overflow with other container classes correctly', () => {
      const renderConfig = {
        aspectRatio: 1.0,
        minHeight: 200,
        overflow: 'hidden' as const
      };
      
      const constraints = getSizeConstraints(renderConfig);
      
      expect(constraints.overflowClasses).toBe('overflow-hidden');
      expect(constraints.aspectRatioClass).toBe('aspect-square');
      expect(constraints.heightClasses).toBe('min-h-[200px]');
      
      // Should not conflict with each other
      const allClasses = [
        constraints.aspectRatioClass,
        constraints.heightClasses,
        constraints.overflowClasses
      ].join(' ');
      
      expect(allClasses).toContain('overflow-hidden');
      expect(allClasses).toContain('aspect-square');
      expect(allClasses).toContain('min-h-[200px]');
    });

    it('should handle scrollable content with proper constraints', () => {
      const renderConfig = {
        minHeight: 200,
        maxHeight: 400,
        overflow: 'scroll' as const
      };
      
      const heightClasses = getHeightConstraintClasses(renderConfig);
      const overflowClasses = getOverflowClasses(renderConfig.overflow);
      
      expect(heightClasses).toBe('min-h-[200px] max-h-[400px]');
      expect(overflowClasses).toBe('overflow-auto');
      
      // Combined classes should create scrollable container
      const combined = `${heightClasses} ${overflowClasses}`;
      expect(combined).toBe('min-h-[200px] max-h-[400px] overflow-auto');
    });

    it('should prevent content from breaking out of fixed height containers', () => {
      const renderConfig = {
        fixedHeight: 300,
        overflow: 'hidden' as const
      };
      
      const result = getRenderConfigClasses(renderConfig);
      
      expect(result.classes).toContain('h-75'); // 300px / 4 = 75
      expect(result.classes).toContain('overflow-hidden');
      
      // Should not have conflicting height classes
      expect(result.classes).not.toContain('h-auto');
      expect(result.classes).not.toContain('min-h-');
      expect(result.classes).not.toContain('max-h-');
    });
  });

  describe('Aspect Ratio Constraint Enforcement', () => {
    it('should maintain aspect ratios within grid boundaries', () => {
      const testCases = [
        {
          position: { x: 0, y: 0, w: 6, h: 2 },
          aspectRatio: 1.0,
          expectedAspect: 'aspect-square'
        },
        {
          position: { x: 0, y: 0, w: 12, h: 1 },
          aspectRatio: 0.5,
          expectedAspect: 'aspect-[2/1]'
        },
        {
          position: { x: 0, y: 0, w: 4, h: 3 },
          aspectRatio: 0.75,
          expectedAspect: 'aspect-[4/3]'
        }
      ];
      
      testCases.forEach(({ position, aspectRatio, expectedAspect }) => {
        const config = {
          position,
          renderConfig: { aspectRatio, overflow: 'hidden' as const }
        };
        
        const result = computeLayoutClasses(config);
        
        expect(result.itemClasses).toContain(expectedAspect);
        expect(result.itemClasses).toContain('overflow-hidden');
        
        // Should have proper grid positioning
        if (position.x > 0) {
          expect(result.itemClasses).toContain(`col-start-${position.x + 1}`);
        }
        expect(result.itemClasses).toContain(`col-span-${position.w}`);
      });
    });

    it('should handle aspect ratios with height constraints', () => {
      const config = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {
          aspectRatio: 1.0,
          minHeight: 200,
          maxHeight: 400,
          overflow: 'hidden' as const
        }
      };
      
      const result = computeLayoutClasses(config);
      
      // Should have both aspect ratio and height constraints
      expect(result.itemClasses).toContain('aspect-square');
      expect(result.itemClasses).toContain('min-h-[200px]');
      expect(result.itemClasses).toContain('max-h-[400px]');
      expect(result.itemClasses).toContain('overflow-hidden');
      
      // Should not have conflicting height classes
      expect(result.itemClasses).not.toContain('h-auto');
    });

    it('should prioritize fixed height over aspect ratio when both are specified', () => {
      const config = {
        position: { x: 0, y: 0, w: 6, h: 2 },
        renderConfig: {
          aspectRatio: 1.0,
          fixedHeight: 300,
          overflow: 'hidden' as const
        }
      };
      
      const result = computeLayoutClasses(config);
      
      // Fixed height should take precedence
      expect(result.itemClasses).toContain('h-[300px]');
      expect(result.itemClasses).toContain('overflow-hidden');
      
      // Aspect ratio should still be applied (CSS will resolve the conflict)
      expect(result.itemClasses).toContain('aspect-square');
    });
  });

  describe('Responsive Overflow Prevention', () => {
    it('should maintain overflow control across breakpoints', () => {
      const config = {
        position: { x: 0, y: 0, w: 12, h: 2 },
        renderConfig: {
          aspectRatio: 0.6,
          minHeight: 180,
          overflow: 'hidden' as const
        },
        responsive: true
      };
      
      const result = computeLayoutClasses(config);
      
      // Should have responsive grid classes
      expect(result.containerClasses).toContain('grid-cols-1');
      expect(result.containerClasses).toContain('md:grid-cols-2');
      expect(result.containerClasses).toContain('lg:grid-cols-3');
      expect(result.containerClasses).toContain('xl:grid-cols-4');
      
      // Should maintain overflow control
      expect(result.itemClasses).toContain('overflow-hidden');
      expect(result.itemClasses).toContain('min-h-0'); // Prevent flex item overflow
      
      // Content should be properly contained
      expect(result.contentClasses).toContain('overflow-hidden');
      expect(result.contentClasses).toContain('min-h-0');
    });

    it('should handle mobile stacking without overflow', () => {
      const widgets = [
        {
          id: 'widget1',
          position: { x: 0, y: 0, w: 6, h: 2 },
          renderConfig: { aspectRatio: 1.0, overflow: 'hidden' as const }
        },
        {
          id: 'widget2',
          position: { x: 6, y: 0, w: 6, h: 2 },
          renderConfig: { aspectRatio: 0.6, overflow: 'hidden' as const }
        }
      ];
      
      const layout = computeLayoutClasses(widgets[0]);
      
      // Should have proper mobile-first classes
      expect(layout.containerClasses).toContain('grid-cols-1'); // Mobile: single column
      expect(layout.itemClasses).toContain('col-span-6'); // Desktop: half width
      expect(layout.itemClasses).toContain('overflow-hidden');
      expect(layout.itemClasses).toContain('min-h-0');
    });
  });

  describe('Layout Validation for Overflow Prevention', () => {
    it('should detect layouts that would cause overflow', () => {
      const layoutWithOverflow = {
        grid: {
          mode: 'strict-grid',
          gap: 4,
          breakpoints: {
            sm: { minWidth: 640, columns: 1, rowUnit: 80 },
            md: { minWidth: 768, columns: 2, rowUnit: 80 },
            lg: { minWidth: 1024, columns: 3, rowUnit: 80 },
            xl: { minWidth: 1280, columns: 4, rowUnit: 80 }
          }
        },
        widgets: [
          {
            id: 'widget1',
            type: 'bar',
            title: 'Overflowing Widget',
            dataSource: { componentId: 1 },
            render: { aspectRatio: 0.6, minHeight: 200 }
          }
        ],
        rows: [
          {
            id: 'row1',
            rowHeight: 'auto' as const,
            items: [
              { widgetRef: 'widget1', col: 10, span: 6, h: 2 } // Would overflow: col 10 + span 6 = 16 > 12
            ]
          }
        ]
      };
      
      const result = validateLayoutConfig(layoutWithOverflow);
      
      expect(result.isValid).toBe(false);
      
      // Should detect validation errors (the specific error message may vary)
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should validate height constraints for overflow prevention', () => {
      const layoutWithInvalidHeights = {
        grid: {
          mode: 'strict-grid',
          gap: 4,
          breakpoints: {
            sm: { minWidth: 640, columns: 1, rowUnit: 80 },
            md: { minWidth: 768, columns: 2, rowUnit: 80 },
            lg: { minWidth: 1024, columns: 3, rowUnit: 80 },
            xl: { minWidth: 1280, columns: 4, rowUnit: 80 }
          }
        },
        widgets: [
          {
            id: 'widget1',
            type: 'bar',
            title: 'Invalid Height Widget',
            dataSource: { componentId: 1 },
            render: {
              minHeight: 400,
              maxHeight: 200, // Max less than min - would cause overflow
              overflow: 'hidden'
            }
          }
        ],
        rows: [
          {
            id: 'row1',
            rowHeight: 'auto' as const,
            items: [
              { widgetRef: 'widget1', col: 1, span: 6, h: 2 }
            ]
          }
        ]
      };
      
      const result = validateLayoutConfig(layoutWithInvalidHeights);
      
      expect(result.isValid).toBe(false);
      
      // Should detect validation errors (the specific error message may vary)
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should provide suggestions for fixing overflow issues', () => {
      const layoutWithOverflow = {
        grid: {
          mode: 'strict-grid',
          gap: 4,
          breakpoints: {
            sm: { minWidth: 640, columns: 1, rowUnit: 80 },
            md: { minWidth: 768, columns: 2, rowUnit: 80 },
            lg: { minWidth: 1024, columns: 3, rowUnit: 80 },
            xl: { minWidth: 1280, columns: 4, rowUnit: 80 }
          }
        },
        widgets: [
          {
            id: 'widget1',
            type: 'bar',
            title: 'Test Widget',
            dataSource: { componentId: 1 },
            render: { aspectRatio: 0.6 }
          }
        ],
        rows: [
          {
            id: 'row1',
            rowHeight: 'auto' as const,
            items: [
              { widgetRef: 'widget1', col: 15, span: 3, h: 2 } // Invalid column position
            ]
          }
        ]
      };
      
      const result = validateLayoutConfig(layoutWithOverflow);
      
      expect(result.isValid).toBe(false);
      
      // Should provide helpful suggestions
      expect(result.errors.length).toBeGreaterThan(0);
      
      // At least one error should have suggestions
      const hasErrorsWithSuggestions = result.errors.some(error => 
        error.suggestions && error.suggestions.length > 0
      );
      expect(hasErrorsWithSuggestions).toBe(true);
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle zero-width components gracefully', () => {
      const position = { x: 0, y: 0, w: 0, h: 2 };
      const validated = validateGridPosition(position, 12);
      
      expect(validated.w).toBe(1); // Should enforce minimum width
      
      const classes = getGridColumnClass(validated, 12);
      expect(classes).toBe('col-span-1');
    });

    it('should handle zero-height components gracefully', () => {
      const position = { x: 0, y: 0, w: 6, h: 0 };
      const classes = getGridRowClass(position);
      
      expect(classes).toBe(''); // Zero height should not generate row span
    });

    it('should handle components at grid boundaries', () => {
      const testCases = [
        { x: 0, w: 1 }, // First column
        { x: 11, w: 1 }, // Last column
        { x: 0, w: 12 }, // Full width
      ];
      
      testCases.forEach(({ x, w }) => {
        const position = { x, y: 0, w, h: 2 };
        const validated = validateGridPosition(position, 12);
        const classes = getGridColumnClass(validated, 12);
        
        // Should not cause overflow
        expect(validated.x + validated.w).toBeLessThanOrEqual(12);
        
        // Should generate valid classes
        expect(classes).toMatch(/col-span-\d+/);
        if (x > 0) {
          expect(classes).toMatch(/col-start-\d+/);
        }
      });
    });

    it('should handle very large component spans', () => {
      const position = { x: 0, y: 0, w: 100, h: 2 }; // Unreasonably large width
      const validated = validateGridPosition(position, 12);
      
      expect(validated.w).toBe(12); // Should clamp to grid width
      
      const classes = getGridColumnClass(validated, 12);
      expect(classes).toBe('col-span-12');
    });

    it('should handle negative positions and sizes', () => {
      const position = { x: -5, y: -2, w: -3, h: -1 };
      const validated = validateGridPosition(position, 12, 10);
      
      expect(validated.x).toBe(0); // Should clamp to 0
      expect(validated.y).toBe(0); // Should clamp to 0
      expect(validated.w).toBe(1); // Should enforce minimum
      expect(validated.h).toBe(1); // Should enforce minimum
    });
  });
});