import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { ThemeProvider, AVAILABLE_THEMES, ThemeKey } from '../contexts/ThemeContext';
import ThemeSelector from '../components/ThemeSelector';

// Mock API client and auth context
jest.mock('../services/api', () => ({
  apiClient: {
    updateUserTheme: jest.fn().mockResolvedValue({ user: {} }),
    logout: jest.fn().mockResolvedValue(undefined),
    getAccessiblePages: jest.fn().mockResolvedValue({ 
      pages: [
        { id: 1, name: 'Test Dashboard', permission_level: 'regular' }
      ], 
      user_role: 'regular' 
    }),
    getPageComponents: jest.fn().mockResolvedValue([]),
  },
}));

jest.mock('../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { 
      id: '1', 
      email: '<EMAIL>', 
      role: 'regular',
      organization: 'Test Org',
      ui_theme: 'corporate'
    },
    updateUser: jest.fn(),
    logout: jest.fn(),
    isAuthenticated: true,
  }),
}));

// Mock ChartComponent to avoid rendering issues
jest.mock('../components/ChartComponent', () => {
  const React = require('react');
  return React.forwardRef((props: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      refresh: jest.fn().mockResolvedValue(undefined),
    }));
    return React.createElement('div', { 'data-testid': 'chart-component' }, 'Chart Component');
  });
});

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock console methods to avoid noise in tests
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

// Utility function to capture computed styles of an element
const captureElementStyles = (element: Element) => {
  const styles = window.getComputedStyle(element);
  return {
    backgroundColor: styles.backgroundColor,
    color: styles.color,
    borderColor: styles.borderColor,
    boxShadow: styles.boxShadow,
    fontSize: styles.fontSize,
    fontWeight: styles.fontWeight,
    padding: styles.padding,
    margin: styles.margin,
  };
};

// Utility function to get theme-specific CSS custom properties
const getThemeCustomProperties = () => {
  const documentStyles = window.getComputedStyle(document.documentElement);
  return {
    primary: documentStyles.getPropertyValue('--p'),
    secondary: documentStyles.getPropertyValue('--s'),
    accent: documentStyles.getPropertyValue('--a'),
    neutral: documentStyles.getPropertyValue('--n'),
    base100: documentStyles.getPropertyValue('--b1'),
    base200: documentStyles.getPropertyValue('--b2'),
    base300: documentStyles.getPropertyValue('--b3'),
    baseContent: documentStyles.getPropertyValue('--bc'),
  };
};

describe('Visual Regression Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    document.documentElement.removeAttribute('data-theme');
  });

  describe('Theme Consistency Across Components', () => {
    AVAILABLE_THEMES.forEach(theme => {
      describe(`Theme: ${theme.name} (${theme.key})`, () => {
        test('Simple dashboard component maintains consistent styling', async () => {
          const SimpleDashboard = () => (
            <div className="bg-base-100 p-8 rounded-lg shadow-lg">
              <h2 className="text-3xl font-bold text-base-content mb-6">
                대시보드 메인
              </h2>
              <p className="text-base-content/70 text-base leading-relaxed mb-6">
                대시보드 컴포넌트가 여기에 구현될 예정입니다.
              </p>
              <div className="mt-8 p-6 bg-base-200 rounded-lg border border-base-300">
                <h3 className="text-xl font-semibold text-base-content mb-4">
                  사용자 정보
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <strong className="text-base-content">이메일:</strong>
                    <span className="ml-2 text-base-content/70"><EMAIL></span>
                  </div>
                </div>
              </div>
            </div>
          );

          const { container } = render(
            <ThemeProvider defaultTheme={theme.key}>
              <SimpleDashboard />
            </ThemeProvider>
          );

          // Wait for theme to be applied
          await waitFor(() => {
            expect(document.documentElement.getAttribute('data-theme')).toBe(theme.key);
          });

          // Capture styles of key elements
          const mainContainer = container.querySelector('[class*="bg-base-100"]');
          const headings = container.querySelectorAll('h1, h2, h3');
          const textElements = container.querySelectorAll('[class*="text-base-content"]');

          expect(mainContainer).toBeInTheDocument();
          
          if (mainContainer) {
            const containerStyles = captureElementStyles(mainContainer);
            
            // Verify theme-appropriate background is applied
            expect(containerStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
            expect(containerStyles.backgroundColor).not.toBe('transparent');
          }

          // Verify all headings use theme-appropriate colors
          headings.forEach(heading => {
            const headingStyles = captureElementStyles(heading);
            expect(headingStyles.color).not.toBe('rgba(0, 0, 0, 0)');
          });

          // Verify text elements use theme colors
          textElements.forEach(textElement => {
            const textStyles = captureElementStyles(textElement);
            expect(textStyles.color).not.toBe('rgba(0, 0, 0, 0)');
          });
        });

        test('ThemeSelector modal maintains consistent styling', async () => {
          const { container } = render(
            <ThemeProvider defaultTheme={theme.key}>
              <ThemeSelector isOpen={true} onClose={() => {}} />
            </ThemeProvider>
          );

          await waitFor(() => {
            expect(document.documentElement.getAttribute('data-theme')).toBe(theme.key);
          });

          const modal = container.querySelector('.modal-box');
          const themeCards = container.querySelectorAll('.card');
          const buttons = container.querySelectorAll('.btn');

          expect(modal).toBeInTheDocument();
          
          if (modal) {
            const modalStyles = captureElementStyles(modal);
            expect(modalStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
            expect(modalStyles.color).not.toBe('rgba(0, 0, 0, 0)');
          }

          // Verify theme cards have consistent styling
          themeCards.forEach(card => {
            const cardStyles = captureElementStyles(card);
            expect(cardStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
          });

          // Verify buttons have theme-appropriate styling
          buttons.forEach(button => {
            const buttonStyles = captureElementStyles(button);
            expect(buttonStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
          });
        });

        test('Theme attribute is properly set on document', async () => {
          render(
            <ThemeProvider defaultTheme={theme.key}>
              <div>Theme Test</div>
            </ThemeProvider>
          );

          await waitFor(() => {
            expect(document.documentElement.getAttribute('data-theme')).toBe(theme.key);
          });

          // Verify theme attribute is set correctly
          expect(document.documentElement).toHaveAttribute('data-theme', theme.key);
        });
      });
    });
  });

  describe('Theme Switching Visual Consistency', () => {
    test('elements maintain structure when switching themes', async () => {
      const TestComponent = () => {
        const [currentTheme, setCurrentTheme] = React.useState<ThemeKey>('corporate');
        
        return (
          <ThemeProvider defaultTheme={currentTheme}>
            <div className="bg-base-100 p-8" data-testid="theme-container">
              <h1 className="text-3xl font-bold text-base-content mb-4">Test Heading</h1>
              <div className="card bg-base-200 p-4 mb-4">
                <p className="text-base-content">Card content</p>
              </div>
              <button 
                className="btn btn-primary"
                onClick={() => setCurrentTheme(currentTheme === 'corporate' ? 'dark' : 'corporate')}
              >
                Switch Theme
              </button>
              <div data-testid="current-theme">{currentTheme}</div>
            </div>
          </ThemeProvider>
        );
      };

      const { container } = render(<TestComponent />);
      
      // Capture initial layout
      const initialHeading = container.querySelector('h1');
      const initialCard = container.querySelector('.card');
      const initialButton = container.querySelector('.btn');
      
      expect(initialHeading).toBeInTheDocument();
      expect(initialCard).toBeInTheDocument();
      expect(initialButton).toBeInTheDocument();

      // Switch theme
      const switchButton = screen.getByText('Switch Theme');
      switchButton.click();

      // Wait for theme state to change
      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      });

      // Verify elements are still present after theme change
      const newHeading = container.querySelector('h1');
      const newCard = container.querySelector('.card');
      const newButton = container.querySelector('.btn');

      expect(newHeading).toBeInTheDocument();
      expect(newCard).toBeInTheDocument();
      expect(newButton).toBeInTheDocument();

      // Verify elements still have the correct classes
      expect(newHeading).toHaveClass('text-base-content');
      expect(newCard).toHaveClass('bg-base-200');
      expect(newButton).toHaveClass('btn-primary');
    });

    test('no visual artifacts during theme transitions', async () => {
      const TestComponent = () => {
        const [currentTheme, setCurrentTheme] = React.useState<ThemeKey>('corporate');
        const [switchCount, setSwitchCount] = React.useState(0);
        
        React.useEffect(() => {
          if (switchCount < 3) {
            const timeout = setTimeout(() => {
              setCurrentTheme(prev => prev === 'corporate' ? 'dark' : 'corporate');
              setSwitchCount(prev => prev + 1);
            }, 100);
            
            return () => clearTimeout(timeout);
          }
        }, [switchCount]);
        
        return (
          <ThemeProvider defaultTheme={currentTheme}>
            <div className="bg-base-100 text-base-content p-8" data-testid="theme-container">
              <h1 className="text-2xl font-bold mb-4">Rapid Theme Switch Test</h1>
              <div className="card bg-base-200 p-4">
                <p>Content should remain stable</p>
              </div>
              <div data-testid="switch-count">{switchCount}</div>
            </div>
          </ThemeProvider>
        );
      };

      const { container } = render(<TestComponent />);
      
      // Wait for theme switching to complete
      await waitFor(() => {
        expect(screen.getByTestId('switch-count')).toHaveTextContent('3');
      }, { timeout: 1000 });

      // Verify elements are still properly rendered
      const heading = container.querySelector('h1');
      const card = container.querySelector('.card');
      const paragraph = container.querySelector('p');

      expect(heading).toBeInTheDocument();
      expect(card).toBeInTheDocument();
      expect(paragraph).toBeInTheDocument();

      // Verify elements still have correct classes
      expect(heading).toHaveClass('text-2xl', 'font-bold', 'mb-4');
      expect(card).toHaveClass('bg-base-200');
    });
  });

  describe('Responsive Design Consistency', () => {
    const viewports = [
      { width: 320, height: 568, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1024, height: 768, name: 'Desktop' },
      { width: 1920, height: 1080, name: 'Large Desktop' },
    ];

    viewports.forEach(viewport => {
      test(`maintains theme consistency at ${viewport.name} viewport`, async () => {
        // Mock window dimensions
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          configurable: true,
          value: viewport.width,
        });
        Object.defineProperty(window, 'innerHeight', {
          writable: true,
          configurable: true,
          value: viewport.height,
        });

        const { container } = render(
          <ThemeProvider defaultTheme="corporate">
            <div className="bg-base-100 min-h-screen">
              <div className="container mx-auto p-4">
                <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-base-content mb-4">
                  Responsive Heading
                </h1>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="card bg-base-200 p-4">
                    <p className="text-base-content">Card 1</p>
                  </div>
                  <div className="card bg-base-200 p-4">
                    <p className="text-base-content">Card 2</p>
                  </div>
                  <div className="card bg-base-200 p-4">
                    <p className="text-base-content">Card 3</p>
                  </div>
                </div>
              </div>
            </div>
          </ThemeProvider>
        );

        await waitFor(() => {
          expect(document.documentElement.getAttribute('data-theme')).toBe('corporate');
        });

        // Verify theme is applied consistently regardless of viewport
        const mainContainer = container.querySelector('[class*="bg-base-100"]');
        const cards = container.querySelectorAll('.card');
        const heading = container.querySelector('h1');

        expect(mainContainer).toBeInTheDocument();
        expect(cards.length).toBe(3);
        expect(heading).toBeInTheDocument();

        // Verify theme colors are applied
        if (mainContainer) {
          const containerStyles = captureElementStyles(mainContainer);
          expect(containerStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
        }

        cards.forEach(card => {
          const cardStyles = captureElementStyles(card);
          expect(cardStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
        });

        if (heading) {
          const headingStyles = captureElementStyles(heading);
          expect(headingStyles.color).not.toBe('rgba(0, 0, 0, 0)');
        }
      });
    });
  });

  describe('Animation and Transition Consistency', () => {
    test('theme transitions are smooth and complete', async () => {
      const TestComponent = () => {
        const [theme, setTheme] = React.useState<ThemeKey>('corporate');
        
        return (
          <ThemeProvider defaultTheme={theme}>
            <div className="bg-base-100 transition-colors duration-300 p-8" data-testid="main-container">
              <h1 className="text-base-content transition-colors duration-300 text-2xl mb-4">
                Transition Test
              </h1>
              <button 
                className="btn btn-primary transition-colors duration-300"
                onClick={() => setTheme(theme === 'corporate' ? 'dark' : 'corporate')}
              >
                Switch Theme
              </button>
              <div data-testid="current-theme">{theme}</div>
            </div>
          </ThemeProvider>
        );
      };

      const { container } = render(<TestComponent />);
      
      const switchButton = screen.getByText('Switch Theme');
      const mainDiv = container.querySelector('[data-testid="main-container"]');
      const heading = container.querySelector('h1');

      // Verify initial state
      expect(screen.getByTestId('current-theme')).toHaveTextContent('corporate');

      // Trigger theme switch
      switchButton.click();

      // Wait for theme state to change
      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      });

      // Verify elements are still present and have correct classes
      expect(mainDiv).toBeInTheDocument();
      expect(heading).toBeInTheDocument();
      expect(switchButton).toBeInTheDocument();

      // Verify transition classes are still applied
      expect(mainDiv).toHaveClass('transition-colors');
      expect(heading).toHaveClass('transition-colors');
      expect(switchButton).toHaveClass('transition-colors');
    });
  });
});