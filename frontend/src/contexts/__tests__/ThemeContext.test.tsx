import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { ThemeProvider, useTheme, DEFAULT_THEME, AVAILABLE_THEMES } from '../ThemeContext';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock console methods to avoid noise in tests
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

// Test component that uses the theme context
const TestComponent: React.FC = () => {
  const { currentTheme, setTheme, availableThemes, initializeFromUser, isStylesLoaded } = useTheme();
  
  return (
    <div>
      <div data-testid="current-theme">{currentTheme}</div>
      <div data-testid="styles-loaded">{isStylesLoaded.toString()}</div>
      <div data-testid="available-themes-count">{availableThemes.length}</div>
      <button 
        data-testid="set-dark-theme" 
        onClick={() => setTheme('dark')}
      >
        Set Dark
      </button>
      <button 
        data-testid="set-corporate-theme" 
        onClick={() => setTheme('corporate')}
      >
        Set Corporate
      </button>
      <button 
        data-testid="initialize-from-user" 
        onClick={() => initializeFromUser('business')}
      >
        Initialize from User
      </button>
    </div>
  );
};

describe('ThemeContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    // Reset document theme attribute
    document.documentElement.removeAttribute('data-theme');
  });

  describe('ThemeProvider initialization', () => {
    test('initializes with default theme when no saved theme exists', async () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      expect(screen.getByTestId('current-theme')).toHaveTextContent(DEFAULT_THEME);
      
      // Wait for theme to be applied to document
      await waitFor(() => {
        expect(document.documentElement.getAttribute('data-theme')).toBe(DEFAULT_THEME);
      });
    });

    test('initializes with saved theme from localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('dark');
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
    });

    test('falls back to default theme when saved theme is invalid', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-theme');
      
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      expect(screen.getByTestId('current-theme')).toHaveTextContent(DEFAULT_THEME);
    });

    test('uses custom default theme when provided', () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      
      render(
        <ThemeProvider defaultTheme="business">
          <TestComponent />
        </ThemeProvider>
      );

      expect(screen.getByTestId('current-theme')).toHaveTextContent('business');
    });
  });

  describe('Theme switching', () => {
    test('changes theme and updates localStorage', async () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      const setDarkButton = screen.getByTestId('set-dark-theme');
      
      await act(async () => {
        setDarkButton.click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      });
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('ui-theme', 'dark');
      
      await waitFor(() => {
        expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
      });
    });

    test('ignores invalid theme changes', async () => {
      const TestComponentWithInvalidTheme: React.FC = () => {
        const { setTheme } = useTheme();
        
        React.useEffect(() => {
          // @ts-ignore - Testing invalid theme
          setTheme('invalid-theme');
        }, [setTheme]);
        
        return <TestComponent />;
      };

      render(
        <ThemeProvider>
          <TestComponentWithInvalidTheme />
        </ThemeProvider>
      );

      // Should remain on default theme
      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent(DEFAULT_THEME);
      });
      expect(mockLocalStorage.setItem).not.toHaveBeenCalledWith('ui-theme', 'invalid-theme');
    });

    test('handles localStorage errors gracefully', async () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      const setDarkButton = screen.getByTestId('set-dark-theme');
      
      await act(async () => {
        setDarkButton.click();
      });

      // Theme should still change even if localStorage fails
      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      });
      
      await waitFor(() => {
        expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
      });
    });
  });

  describe('User initialization', () => {
    test('initializes theme from user preference', async () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      const initButton = screen.getByTestId('initialize-from-user');
      
      await act(async () => {
        initButton.click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('business');
      });
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('ui-theme', 'business');
      
      await waitFor(() => {
        expect(document.documentElement.getAttribute('data-theme')).toBe('business');
      });
    });

    test('falls back to localStorage when user theme is invalid', async () => {
      mockLocalStorage.getItem.mockReturnValue('retro');
      
      const TestComponentWithInvalidUser: React.FC = () => {
        const { initializeFromUser } = useTheme();
        
        React.useEffect(() => {
          initializeFromUser('invalid-theme');
        }, [initializeFromUser]);
        
        return <TestComponent />;
      };

      render(
        <ThemeProvider>
          <TestComponentWithInvalidUser />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('retro');
      });
    });

    test('prevents multiple initializations', async () => {
      const TestComponentWithMultipleInit: React.FC = () => {
        const { initializeFromUser } = useTheme();
        
        return (
          <div>
            <TestComponent />
            <button 
              data-testid="init-first" 
              onClick={() => initializeFromUser('dark')}
            >
              Init First
            </button>
            <button 
              data-testid="init-second" 
              onClick={() => initializeFromUser('cyberpunk')}
            >
              Init Second
            </button>
          </div>
        );
      };

      render(
        <ThemeProvider>
          <TestComponentWithMultipleInit />
        </ThemeProvider>
      );

      const initFirstButton = screen.getByTestId('init-first');
      const initSecondButton = screen.getByTestId('init-second');
      
      await act(async () => {
        initFirstButton.click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      });

      await act(async () => {
        initSecondButton.click();
      });

      // Should still be dark, not cyberpunk
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
    });
  });

  describe('Available themes', () => {
    test('provides all available themes', () => {
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      expect(screen.getByTestId('available-themes-count')).toHaveTextContent(
        AVAILABLE_THEMES.length.toString()
      );
    });

    test('available themes contain required properties', () => {
      AVAILABLE_THEMES.forEach(theme => {
        expect(theme).toHaveProperty('key');
        expect(theme).toHaveProperty('name');
        expect(theme).toHaveProperty('description');
        expect(typeof theme.key).toBe('string');
        expect(typeof theme.name).toBe('string');
        expect(typeof theme.description).toBe('string');
      });
    });
  });

  describe('Error handling', () => {
    test('handles context usage outside provider', () => {
      const TestComponentOutsideProvider: React.FC = () => {
        try {
          useTheme();
          return <div>Should not render</div>;
        } catch (error) {
          return <div data-testid="error">Error caught</div>;
        }
      };

      render(<TestComponentOutsideProvider />);
      
      expect(screen.getByTestId('error')).toBeInTheDocument();
    });

    test('handles localStorage read errors during initialization', () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('localStorage not available');
      });

      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      // Should fall back to default theme
      expect(screen.getByTestId('current-theme')).toHaveTextContent(DEFAULT_THEME);
    });
  });

  describe('CSS styles loading detection', () => {
    test('detects when styles are loaded', async () => {
      // Mock getComputedStyle to simulate loaded styles
      const mockGetComputedStyle = jest.fn().mockReturnValue({
        backgroundColor: 'rgb(255, 255, 255)'
      });
      Object.defineProperty(window, 'getComputedStyle', {
        value: mockGetComputedStyle,
        writable: true
      });

      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('styles-loaded')).toHaveTextContent('true');
      });
    });

    test('handles styles not loaded initially', async () => {
      // Mock getComputedStyle to simulate styles not loaded
      const mockGetComputedStyle = jest.fn()
        .mockReturnValueOnce({ backgroundColor: 'rgba(0, 0, 0, 0)' })
        .mockReturnValue({ backgroundColor: 'rgb(255, 255, 255)' });
      
      Object.defineProperty(window, 'getComputedStyle', {
        value: mockGetComputedStyle,
        writable: true
      });

      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );

      // Should eventually detect styles as loaded
      await waitFor(() => {
        expect(screen.getByTestId('styles-loaded')).toHaveTextContent('true');
      }, { timeout: 1000 });
    });
  });
});