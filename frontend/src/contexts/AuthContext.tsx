import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, AuthContextType } from '../types/auth';
import { apiClient } from '../services/api';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize auth state on app load
  useEffect(() => {
    const initializeAuth = async () => {
      const accessToken = localStorage.getItem('access_token');
      const refreshToken = localStorage.getItem('refresh_token');
      
      if (accessToken && refreshToken) {
        try {
          // Validate token by fetching user data
          const userData = await apiClient.getCurrentUser();
          setUser(userData);
        } catch (error) {
          console.warn('Token validation failed:', error);
          // Clear invalid tokens
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          
          // Try to refresh if we have a refresh token
          if (refreshToken) {
            try {
              // The API client will handle token refresh automatically
              // Just try to get user data again, which will trigger refresh if needed
              const userData = await apiClient.getCurrentUser();
              setUser(userData);
            } catch (refreshError) {
              console.warn('Token refresh failed during initialization:', refreshError);
              // Clear all auth data if refresh fails
              localStorage.removeItem('access_token');
              localStorage.removeItem('refresh_token');
              // Only clear remember login if it's not set
              const rememberLogin = localStorage.getItem('remember_login');
              if (!rememberLogin) {
                localStorage.removeItem('remember_login');
                localStorage.removeItem('remembered_email');
              }
            }
          }
        }
      }
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string, rememberMe: boolean = false): Promise<void> => {
    try {
      const response = await apiClient.login({ email, password });
      
      // Store tokens with correct field names
      localStorage.setItem('access_token', response.access);
      localStorage.setItem('refresh_token', response.refresh);
      
      // Handle remember me preference
      if (rememberMe) {
        localStorage.setItem('remember_login', 'true');
        localStorage.setItem('remembered_email', email);
      } else {
        localStorage.removeItem('remember_login');
        localStorage.removeItem('remembered_email');
      }
      
      // Set user state
      setUser(response.user);
    } catch (error) {
      throw error;
    }
  };

  const loginWithGoogle = async (): Promise<void> => {
    try {
      // Use the backend callback endpoint as redirect URI
      const backendUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';
      const redirectUri = `${backendUrl}/api/auth/google/callback/`;
      
      // Get Google OAuth config from backend
      const config = await apiClient.getGoogleOAuthConfig();
      
      const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${config.client_id}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `response_type=code&` +
        `scope=openid email profile&` +
        `access_type=offline&` +
        `prompt=consent`;

      window.location.href = googleAuthUrl;
    } catch (error) {
      throw error;
    }
  };

  const handleGoogleCallback = async (code: string): Promise<void> => {
    try {
      const response = await apiClient.loginWithGoogle(code);
      
      // Store tokens with correct field names
      localStorage.setItem('access_token', response.access);
      localStorage.setItem('refresh_token', response.refresh);
      
      // Set user state
      setUser(response.user);
    } catch (error) {
      throw error;
    }
  };

  const setUserFromTokens = async (userId?: string, email?: string): Promise<void> => {
    try {
      // If we have tokens, fetch current user info
      const userData = await apiClient.getCurrentUser();
      setUser(userData);
    } catch (error) {
      // If fetching user fails, create user object from available data
      if (userId && email) {
        const user: User = {
          id: userId,
          email: email,
          role: 'regular',
          organization: undefined,
          created_at: new Date().toISOString()
        };
        setUser(user);
      } else {
        throw error;
      }
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await apiClient.logout();
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Clear all authentication data
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('remember_login');
      localStorage.removeItem('remembered_email');
      setUser(null);
    }
  };

  const updateUser = (updatedUser: User): void => {
    setUser(updatedUser);
  };

  const value: AuthContextType = {
    user,
    login,
    loginWithGoogle,
    logout,
    isAuthenticated,
    loading,
    updateUser,
  };

  // Expose handleGoogleCallback and setUserFromTokens for the callback route
  (value as any).handleGoogleCallback = handleGoogleCallback;
  (value as any).setUserFromTokens = setUserFromTokens;

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};