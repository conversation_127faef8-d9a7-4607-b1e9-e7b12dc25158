import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';

// Default theme for the application
export const DEFAULT_THEME = 'corporate' as const;

// Available DaisyUI themes
export const AVAILABLE_THEMES = [
  { key: 'corporate', name: '기업용', description: '깔끔하고 전문적인 비즈니스 테마' },
  { key: 'business', name: '비즈니스', description: '현대적인 비즈니스 인터페이스' },
  { key: 'dark', name: '다크 모드', description: '어두운 배경의 모던한 테마' },
  { key: 'retro', name: '레트로', description: '빈티지 스타일의 클래식 테마' },
  { key: 'cyberpunk', name: '사이버펑크', description: '미래지향적 네온 컬러 테마' },
] as const;

export type ThemeKey = typeof AVAILABLE_THEMES[number]['key'];

// Theme-related logging utility
const themeLogger = {
  info: (message: string, data?: any) => {
    console.log(`🎨 [Theme] ${message}`, data || '');
  },
  warn: (message: string, error?: any) => {
    console.warn(`⚠️ [Theme] ${message}`, error || '');
  },
  error: (message: string, error?: any) => {
    console.error(`❌ [Theme] ${message}`, error || '');
  }
};

interface ThemeContextType {
  currentTheme: ThemeKey;
  setTheme: (theme: ThemeKey) => void;
  availableThemes: typeof AVAILABLE_THEMES;
  initializeFromUser: (userTheme?: string) => void;
  isStylesLoaded: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: ThemeKey;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  defaultTheme = DEFAULT_THEME 
}) => {
  const [currentTheme, setCurrentTheme] = useState<ThemeKey>(() => {
    try {
      // Load theme from localStorage or use default
      const savedTheme = localStorage.getItem('ui-theme') as ThemeKey;
      const validTheme = AVAILABLE_THEMES.find(t => t.key === savedTheme)?.key;
      const initialTheme = validTheme || defaultTheme;
      
      themeLogger.info('Initializing theme provider', { 
        savedTheme, 
        validTheme, 
        initialTheme, 
        defaultTheme 
      });
      
      return initialTheme;
    } catch (error) {
      themeLogger.error('Failed to load theme from localStorage, using default', error);
      return defaultTheme;
    }
  });
  const [initializedFromUser, setInitializedFromUser] = useState(false);
  const [isStylesLoaded, setIsStylesLoaded] = useState(false);

  // CSS loading protection - check if DaisyUI styles are available
  const checkStylesLoaded = useCallback((): boolean => {
    try {
      const testElement = document.createElement('div');
      testElement.className = 'bg-base-100';
      testElement.style.position = 'absolute';
      testElement.style.visibility = 'hidden';
      testElement.style.pointerEvents = 'none';
      
      document.body.appendChild(testElement);
      
      const styles = window.getComputedStyle(testElement);
      const hasStyles = styles.backgroundColor !== 'rgba(0, 0, 0, 0)' && 
                       styles.backgroundColor !== 'transparent' &&
                       styles.backgroundColor !== '';
      
      document.body.removeChild(testElement);
      
      themeLogger.info('CSS styles check', { 
        hasStyles, 
        backgroundColor: styles.backgroundColor 
      });
      
      return hasStyles;
    } catch (error) {
      themeLogger.warn('Failed to check CSS styles, assuming loaded', error);
      return true; // Assume styles are loaded if check fails
    }
  }, []);

  // Enhanced theme application with error handling and fallback
  const applyTheme = useCallback((theme: ThemeKey, retryCount = 0): void => {
    const maxRetries = 3;
    const retryDelay = 100;

    try {
      // Validate theme
      const validTheme = AVAILABLE_THEMES.find(t => t.key === theme);
      if (!validTheme) {
        themeLogger.warn(`Invalid theme "${theme}", falling back to default`, { theme, defaultTheme: DEFAULT_THEME });
        theme = DEFAULT_THEME;
      }

      // Check if styles are loaded before applying theme
      if (!isStylesLoaded && !checkStylesLoaded()) {
        if (retryCount < maxRetries) {
          themeLogger.info(`Styles not ready, retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})`);
          setTimeout(() => applyTheme(theme, retryCount + 1), retryDelay);
          return;
        } else {
          themeLogger.warn('Styles still not loaded after retries, applying theme anyway');
        }
      }

      // Apply theme to document
      document.documentElement.setAttribute('data-theme', theme);
      
      // Verify theme was applied
      const appliedTheme = document.documentElement.getAttribute('data-theme');
      if (appliedTheme !== theme) {
        throw new Error(`Theme application failed: expected "${theme}", got "${appliedTheme}"`);
      }

      themeLogger.info('Theme applied successfully', { 
        theme, 
        appliedTheme, 
        retryCount,
        stylesLoaded: isStylesLoaded 
      });

    } catch (error) {
      themeLogger.error('Failed to apply theme', { theme, error, retryCount });
      
      // Fallback to default theme if not already trying default
      if (theme !== DEFAULT_THEME && retryCount === 0) {
        themeLogger.info('Attempting fallback to default theme');
        applyTheme(DEFAULT_THEME, 0);
      } else {
        // Last resort: force apply default theme
        try {
          document.documentElement.setAttribute('data-theme', DEFAULT_THEME);
          themeLogger.warn('Applied default theme as last resort');
        } catch (fallbackError) {
          themeLogger.error('Critical: Failed to apply even default theme', fallbackError);
        }
      }
    }
  }, [isStylesLoaded, checkStylesLoaded]);

  const setTheme = useCallback((theme: ThemeKey) => {
    try {
      themeLogger.info('setTheme called', { theme, currentTheme });
      
      // Validate theme before setting
      const validTheme = AVAILABLE_THEMES.find(t => t.key === theme);
      if (!validTheme) {
        themeLogger.warn(`Invalid theme "${theme}", ignoring request`);
        return;
      }

      setCurrentTheme(theme);
      
      // Save to localStorage with error handling
      try {
        localStorage.setItem('ui-theme', theme);
        themeLogger.info('Theme saved to localStorage', { theme });
      } catch (storageError) {
        themeLogger.warn('Failed to save theme to localStorage', storageError);
      }
      
      // Apply theme to document
      applyTheme(theme);
      
    } catch (error) {
      themeLogger.error('setTheme failed', { theme, error });
    }
  }, [currentTheme, applyTheme]);

  const initializeFromUser = useCallback((userTheme?: string) => {
    if (initializedFromUser) {
      themeLogger.info('Theme already initialized from user, skipping');
      return;
    }

    try {
      themeLogger.info('Initializing theme from user data', { 
        userTheme, 
        currentTheme, 
        defaultTheme 
      });

      let themeToUse: ThemeKey;

      if (userTheme) {
        // User has a theme set - validate and use it
        const validTheme = AVAILABLE_THEMES.find(t => t.key === userTheme)?.key;
        if (validTheme) {
          themeToUse = validTheme;
          themeLogger.info('Using valid user theme', { userTheme: validTheme });
        } else {
          themeLogger.warn(`Invalid user theme "${userTheme}", falling back to localStorage or default`);
          // Fall back to localStorage theme or default
          const savedTheme = localStorage.getItem('ui-theme') as ThemeKey;
          themeToUse = AVAILABLE_THEMES.find(t => t.key === savedTheme)?.key || defaultTheme;
        }
      } else {
        // User has no theme set - use localStorage theme or default
        try {
          const savedTheme = localStorage.getItem('ui-theme') as ThemeKey;
          themeToUse = AVAILABLE_THEMES.find(t => t.key === savedTheme)?.key || defaultTheme;
          themeLogger.info('No user theme, using localStorage or default', { 
            savedTheme, 
            themeToUse 
          });
        } catch (storageError) {
          themeLogger.warn('Failed to read from localStorage, using default theme', storageError);
          themeToUse = defaultTheme;
        }
      }

      // Apply the determined theme if it's different from current
      if (themeToUse !== currentTheme) {
        themeLogger.info('Applying new theme from user initialization', { 
          from: currentTheme, 
          to: themeToUse 
        });
        
        setCurrentTheme(themeToUse);
        
        // Save to localStorage with error handling
        try {
          localStorage.setItem('ui-theme', themeToUse);
        } catch (storageError) {
          themeLogger.warn('Failed to save theme to localStorage during initialization', storageError);
        }
        
        // Apply theme to document
        applyTheme(themeToUse);
      } else {
        themeLogger.info('Theme unchanged during user initialization', { theme: themeToUse });
        // Still apply to ensure document has correct theme
        applyTheme(themeToUse);
      }

      setInitializedFromUser(true);
      themeLogger.info('Theme initialization from user completed', { finalTheme: themeToUse });

    } catch (error) {
      themeLogger.error('Failed to initialize theme from user, using fallback', error);
      
      // Fallback to default theme
      try {
        setCurrentTheme(defaultTheme);
        localStorage.setItem('ui-theme', defaultTheme);
        applyTheme(defaultTheme);
        setInitializedFromUser(true);
        themeLogger.info('Fallback theme initialization completed', { theme: defaultTheme });
      } catch (fallbackError) {
        themeLogger.error('Critical: Fallback theme initialization failed', fallbackError);
        setInitializedFromUser(true); // Prevent infinite retry
      }
    }
  }, [initializedFromUser, currentTheme, defaultTheme, applyTheme]);

  // Check for CSS loading on mount
  useEffect(() => {
    const checkAndSetStylesLoaded = () => {
      const loaded = checkStylesLoaded();
      setIsStylesLoaded(loaded);
      
      if (loaded) {
        themeLogger.info('DaisyUI styles detected as loaded');
        // Apply current theme now that styles are ready
        applyTheme(currentTheme);
      } else {
        themeLogger.warn('DaisyUI styles not yet loaded, will retry');
        // Retry after a short delay
        setTimeout(checkAndSetStylesLoaded, 50);
      }
    };

    // Initial check
    checkAndSetStylesLoaded();
  }, [applyTheme, currentTheme, checkStylesLoaded]);

  // Apply theme when currentTheme changes (but only if styles are loaded or after retries)
  useEffect(() => {
    if (currentTheme) {
      themeLogger.info('Theme changed, applying new theme', { 
        currentTheme, 
        isStylesLoaded 
      });
      applyTheme(currentTheme);
    }
  }, [currentTheme, applyTheme, isStylesLoaded]);

  const value: ThemeContextType = {
    currentTheme,
    setTheme,
    availableThemes: AVAILABLE_THEMES,
    initializeFromUser,
    isStylesLoaded,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    const error = new Error('useTheme must be used within a ThemeProvider');
    themeLogger.error('useTheme called outside of ThemeProvider', error);
    throw error;
  }
  return context;
};

// Hook for getting theme-specific CSS variables
export const useThemeColors = () => {
  const { currentTheme, isStylesLoaded } = useTheme();
  
  // Use currentTheme to potentially return theme-specific values in the future
  const themePrefix = currentTheme === 'dark' ? 'dark' : 'light';
  
  // Warn if styles aren't loaded yet
  if (!isStylesLoaded) {
    themeLogger.warn('useThemeColors called before styles are fully loaded', { currentTheme });
  }
  
  return {
    themePrefix,
    isStylesLoaded,
    primary: 'hsl(var(--p))',
    secondary: 'hsl(var(--s))',
    accent: 'hsl(var(--a))',
    neutral: 'hsl(var(--n))',
    base100: 'hsl(var(--b1))',
    base200: 'hsl(var(--b2))',
    base300: 'hsl(var(--b3))',
    info: 'hsl(var(--in))',
    success: 'hsl(var(--su))',
    warning: 'hsl(var(--wa))',
    error: 'hsl(var(--er))',
  };
};