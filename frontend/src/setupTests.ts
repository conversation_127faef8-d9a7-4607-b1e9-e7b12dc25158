import '@testing-library/jest-dom';
// React 19 deprecated `ReactDOMTestUtils.act`. Ensure all tests use
// `React.act` instead by mocking the old module to re-export the new `act`.
jest.mock('react-dom/test-utils', () => {
  const actual = jest.requireActual('react-dom/test-utils');
  const { act } = jest.requireActual('react');
  return { ...actual, act };
});

// Mock canvas for Tremor Charts in Jest (jsdom lacks Canvas API)
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: jest.fn(() => ({})),
});
