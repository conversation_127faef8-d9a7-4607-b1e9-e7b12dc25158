import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider, DEFAULT_THEME } from './contexts/ThemeContext';
import PrivateRoute from './components/PrivateRoute';
import LoginPage from './components/LoginPage';
import GoogleCallback from './components/GoogleCallback';
import Dashboard from './components/Dashboard';
import AdminPanel from './components/AdminPanel';
import PermissionManagement from './components/PermissionManagement';
import './App.css';

function App() {
  return (
    <ThemeProvider defaultTheme={DEFAULT_THEME}>
      <AuthProvider>
        <div className="App min-h-screen bg-base-100 text-base-content">
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/auth/google/callback" element={<GoogleCallback />} />
          
          {/* Protected routes */}
          <Route 
            path="/dashboard" 
            element={
              <PrivateRoute>
                <Dashboard />
              </PrivateRoute>
            } 
          />
          
          {/* Admin routes - requires org_admin or higher */}
          <Route 
            path="/admin" 
            element={
              <PrivateRoute requiredRole="org_admin">
                <AdminPanel />
              </PrivateRoute>
            } 
          />
          
          {/* Permission Management - requires manage_users permission */}
          <Route 
            path="/admin/permissions" 
            element={
              <PrivateRoute requiredRole="org_admin">
                <PermissionManagement />
              </PrivateRoute>
            } 
          />
          
          {/* Default redirect */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          
          {/* 404 fallback */}
          <Route 
            path="*" 
            element={
              <div className="flex flex-col items-center justify-center min-h-screen text-center">
                <h1 className="text-3xl font-bold mb-4 text-base-content">404 - 페이지를 찾을 수 없습니다</h1>
                <p className="text-base-content/70 mb-6">
                  요청하신 페이지가 존재하지 않습니다.
                </p>
                <a 
                  href="#/dashboard" 
                  className="btn btn-primary"
                >
                  대시보드로 이동
                </a>
              </div>
            } 
          />
        </Routes>
        </div>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;