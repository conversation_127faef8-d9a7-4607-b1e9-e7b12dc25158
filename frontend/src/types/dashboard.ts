/**
 * Defines responsive breakpoint configuration for dashboard grid layouts
 * 
 * @interface GridBreakpoint
 * @property {number} minWidth - Minimum viewport width in pixels for this breakpoint
 * @property {number} columns - Number of grid columns at this breakpoint
 * @property {number} rowUnit - Base row height unit in pixels
 */
export interface GridBreakpoint {
  minWidth: number;
  columns: number;
  rowUnit: number;
}

/**
 * Configuration for dashboard grid layout system
 * 
 * @interface GridConfig
 * @property {'strict-grid' | 'masonry'} mode - Layout mode: strict-grid for uniform rows, masonry for variable heights
 * @property {number} gap - Gap between grid items in pixels
 * @property {Object} breakpoints - Responsive breakpoint configurations
 * @property {GridBreakpoint} breakpoints.sm - Small screen breakpoint (typically 640px+)
 * @property {GridBreakpoint} breakpoints.md - Medium screen breakpoint (typically 768px+)
 * @property {GridBreakpoint} breakpoints.lg - Large screen breakpoint (typically 1024px+)
 * @property {GridBreakpoint} breakpoints.xl - Extra large screen breakpoint (typically 1280px+)
 */
export interface GridConfig {
  mode: 'strict-grid' | 'masonry';
  gap: number;
  breakpoints: {
    sm: GridBreakpoint;
    md: GridBreakpoint;
    lg: GridBreakpoint;
    xl: GridBreakpoint;
  };
}

/**
 * Represents a widget placement within a dashboard row
 * 
 * @interface RowItem
 * @property {string} widgetRef - Reference ID to the widget to be rendered
 * @property {number} col - Starting column position (1-based index)
 * @property {number} span - Number of columns the widget spans
 * @property {number} h - Height in row units (used for masonry layouts)
 */
export interface RowItem {
  widgetRef: string;
  col: number;
  span: number;
  h: number;
}

/**
 * Defines a horizontal row in the dashboard layout
 * 
 * @interface RowLayout
 * @property {string} id - Unique identifier for the row
 * @property {'auto' | number} rowHeight - Row height: 'auto' for content-based height, number for fixed pixel height
 * @property {RowItem[]} items - Array of widget placements within this row
 */
export interface RowLayout {
  id: string;
  rowHeight: 'auto' | number;
  items: RowItem[];
}

/**
 * Configuration for widget rendering behavior and constraints
 * 
 * @interface WidgetRenderConfig
 * @property {boolean} [autoHeight] - If true, widget height adjusts to content within min/max constraints
 * @property {number} [aspectRatio] - Desired aspect ratio (height/width) for the widget
 * @property {number} [minHeight] - Minimum height in pixels
 * @property {number} [maxHeight] - Maximum height in pixels
 * @property {number} [fixedHeight] - Fixed height in pixels (overrides autoHeight)
 * @property {'hidden' | 'scroll' | 'visible'} [overflow] - How to handle content overflow
 * @property {number} [padding] - Internal padding in pixels
 */
export interface WidgetRenderConfig {
  autoHeight?: boolean;
  aspectRatio?: number;
  minHeight?: number;
  maxHeight?: number;
  fixedHeight?: number;
  overflow?: 'hidden' | 'scroll' | 'visible';
  padding?: number;
}

/**
 * Represents a dashboard widget with its configuration and data source
 * 
 * @interface Widget
 * @property {string} id - Unique identifier for the widget
 * @property {string} type - Chart type (e.g., 'bar', 'line', 'pie', 'radar')
 * @property {string} title - Display title for the widget
 * @property {Object} dataSource - Data source configuration
 * @property {number} [dataSource.componentId] - Backend component template ID
 * @property {string} [dataSource.apiUrl] - Direct API endpoint URL
 * @property {WidgetRenderConfig} render - Rendering configuration and constraints
 */
export interface Widget {
  id: string;
  type: string;
  title: string;
  dataSource: {
    componentId?: number;
    apiUrl?: string;
  };
  render: WidgetRenderConfig;
}

/**
 * Complete dashboard layout configuration
 * 
 * @interface DashboardLayout
 * @property {GridConfig} grid - Grid system configuration and responsive breakpoints
 * @property {RowLayout[]} rows - Array of row layouts defining widget placement
 * @property {Widget[]} widgets - Array of widget definitions and configurations
 */
export interface DashboardLayout {
  grid: GridConfig;
  rows: RowLayout[];
  widgets: Widget[];
}

/**
 * Utility function to determine the current responsive breakpoint based on viewport width
 * 
 * @param {GridConfig['breakpoints']} breakpoints - Available responsive breakpoints
 * @param {number} windowWidth - Current viewport width in pixels
 * @returns {GridBreakpoint} The appropriate breakpoint configuration for the current width
 * 
 * @example
 * ```typescript
 * const breakpoints = {
 *   sm: { minWidth: 640, columns: 1, rowUnit: 200 },
 *   md: { minWidth: 768, columns: 2, rowUnit: 180 },
 *   lg: { minWidth: 1024, columns: 3, rowUnit: 160 },
 *   xl: { minWidth: 1280, columns: 4, rowUnit: 140 }
 * };
 * 
 * const currentBp = getCurrentBreakpoint(breakpoints, 1200);
 * // Returns lg breakpoint (3 columns)
 * ```
 */
export function getCurrentBreakpoint(breakpoints: GridConfig['breakpoints'], windowWidth: number): GridBreakpoint {
  const sortedBreakpoints = Object.entries(breakpoints)
    .sort(([, a], [, b]) => b.minWidth - a.minWidth);
  
  for (const [, bp] of sortedBreakpoints) {
    if (windowWidth >= bp.minWidth) {
      return bp;
    }
  }
  
  return breakpoints.sm;
}