import { Organization } from '../services/api';

export interface User {
  id: string;
  email: string;
  role: 'regular' | 'org_admin' | 'super_admin';
  organization?: Organization;
  ui_theme?: string;
  created_at: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  access: string;
  refresh: string;
  user: User;
  message?: string;
}

export interface AuthContextType {
  user: User | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  loading: boolean;
  updateUser: (user: User) => void;
}