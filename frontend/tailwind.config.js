/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html",
    "./node_modules/daisyui/**/*.js"
  ],
  theme: {
    extend: {
      // Support for container queries
      containers: {
        'xs': '20rem',
        'sm': '24rem',
        'md': '28rem',
        'lg': '32rem',
        'xl': '36rem',
        '2xl': '42rem',
      },
      // Extended aspect ratio values for dashboard widgets
      aspectRatio: {
        '4/3': '4 / 3',
        '3/4': '3 / 4',
        '5/3': '5 / 3',
        '3/5': '3 / 5',
        '5/4': '5 / 4',
        '4/5': '4 / 5',
        '1/2': '1 / 2',
        '2/1': '2 / 1',
      },
      // Extended grid column and row spans
      gridColumn: {
        'span-13': 'span 13 / span 13',
        'span-14': 'span 14 / span 14',
        'span-15': 'span 15 / span 15',
        'span-16': 'span 16 / span 16',
      },
      gridRow: {
        'span-7': 'span 7 / span 7',
        'span-8': 'span 8 / span 8',
        'span-9': 'span 9 / span 9',
        'span-10': 'span 10 / span 10',
      },
      // Extended grid template columns
      gridTemplateColumns: {
        '13': 'repeat(13, minmax(0, 1fr))',
        '14': 'repeat(14, minmax(0, 1fr))',
        '15': 'repeat(15, minmax(0, 1fr))',
        '16': 'repeat(16, minmax(0, 1fr))',
      },
    },
  },
  plugins: [
    require("daisyui"),
    require('@tailwindcss/container-queries')
  ],
  daisyui: {
    themes: [
      "corporate",
      "business", 
      "dark",
      "retro",
      "cyberpunk"
    ],
    darkTheme: "dark",
    base: true,
    styled: true,
    utils: true,
  },
}