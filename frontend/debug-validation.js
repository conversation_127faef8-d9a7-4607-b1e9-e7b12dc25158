// Simple test to debug the validation issue
const pageData = {
  "id": 7,
  "name": "📊 새로운 스키마 쇼케이스",
  "permission_level": "regular",
  "layout_config": {
    "grid": {
      "breakpoints": {
        "lg": {
          "columns": 3,
          "minWidth": 1024,
          "rowUnit": 260
        },
        "md": {
          "columns": 2,
          "minWidth": 640,
          "rowUnit": 240
        },
        "sm": {
          "columns": 1,
          "minWidth": 0,
          "rowUnit": 220
        },
        "xl": {
          "columns": 4,
          "minWidth": 1440,
          "rowUnit": 280
        }
      },
      "gap": 12,
      "mode": "strict-grid"
    },
    "rows": [
      {
        "id": "showcase-row-1",
        "items": [
          {
            "col": 1,
            "h": 1,
            "span": 1,
            "widgetRef": "showcase-widget-1"
          }
        ],
        "rowHeight": "auto"
      }
    ],
    "widgets": [
      {
        "dataSource": {
          "componentId": 12
        },
        "id": "showcase-widget-1",
        "render": {
          "aspectRatio": 1.0,
          "autoHeight": true,
          "minHeight": 220
        },
        "title": "고객 만족도 레이더 (Radar Chart)",
        "type": "radar"
      }
    ]
  },
  "components": []
};

console.log('Testing layout schema detection...');

// Basic structure check
const layout_config = pageData.layout_config;
console.log('Layout config:', layout_config);

const hasGrid = layout_config.grid && typeof layout_config.grid === 'object';
const hasWidgets = Array.isArray(layout_config.widgets);
const hasRows = Array.isArray(layout_config.rows);

console.log('Schema structure check:', { hasGrid, hasWidgets, hasRows });

if (hasGrid && hasWidgets && hasRows) {
  console.log('✅ Basic new schema structure detected');
  console.log('Grid:', layout_config.grid);
  console.log('Widgets count:', layout_config.widgets.length);
  console.log('Rows count:', layout_config.rows.length);
} else {
  console.log('❌ Missing required new schema structure');
}