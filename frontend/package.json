{"name": "dynamic-bi-dashboard-frontend", "version": "0.1.0", "private": true, "dependencies": {"@tailwindcss/container-queries": "^0.1.1", "@tremor/react": "^3.18.7", "@types/jest": "^27.5.2", "axios": "^1.11.0", "google-auth-library": "^10.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.8.1", "react-scripts": "5.0.1", "recharts": "^3.1.2", "web-vitals": "^5.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "./", "devDependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^18.19.123", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "ajv": "^8.17.1", "autoprefixer": "^10.4.21", "daisyui": "^5.0.50", "tailwindcss": "^3.4.17", "typescript": "^5.9.2"}}