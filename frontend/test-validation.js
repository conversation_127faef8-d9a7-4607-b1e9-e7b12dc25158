// Test the validation function directly
const { validateLayoutConfig } = require('./src/utils/layoutValidation');

const pageData = {
  "grid": {
    "breakpoints": {
      "lg": {
        "columns": 3,
        "minWidth": 1024,
        "rowUnit": 260
      },
      "md": {
        "columns": 2,
        "minWidth": 640,
        "rowUnit": 240
      },
      "sm": {
        "columns": 1,
        "minWidth": 0,
        "rowUnit": 220
      },
      "xl": {
        "columns": 4,
        "minWidth": 1440,
        "rowUnit": 280
      }
    },
    "gap": 12,
    "mode": "strict-grid"
  },
  "rows": [
    {
      "id": "showcase-row-1",
      "items": [
        {
          "col": 1,
          "h": 1,
          "span": 1,
          "widgetRef": "showcase-widget-1"
        }
      ],
      "rowHeight": "auto"
    }
  ],
  "widgets": [
    {
      "dataSource": {
        "componentId": 12
      },
      "id": "showcase-widget-1",
      "render": {
        "aspectRatio": 1.0,
        "autoHeight": true,
        "minHeight": 220
      },
      "title": "고객 만족도 레이더 (Radar Chart)",
      "type": "radar"
    }
  ]
};

console.log('Testing validation function...');
try {
  const result = validateLayoutConfig(pageData);
  console.log('Validation result:', result);
} catch (error) {
  console.error('Validation error:', error);
}