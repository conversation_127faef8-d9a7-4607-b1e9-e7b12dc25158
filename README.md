# Dynamic BI Dashboard

동적 BI 대시보드 생성 앱 - React.js 프론트엔드와 Django 5.2 백엔드로 구성된 풀스택 웹 애플리케이션

## 프로젝트 구조

```
dynamic-bi-dashboard/
├── backend/                    # Django 5.2 백엔드
│   ├── dynamic_bi_dashboard/   # Django 프로젝트 설정
│   ├── accounts/              # 사용자 인증 앱
│   ├── dashboard/             # 대시보드 관리 앱
│   ├── requirements.txt       # Python 의존성
│   ├── manage.py             # Django 관리 스크립트
│   └── .env.example          # 환경 변수 예시
├── frontend/                  # React.js TypeScript 프론트엔드
│   ├── public/               # 정적 파일
│   ├── src/                  # React 소스 코드
│   ├── package.json          # Node.js 의존성
│   ├── tsconfig.json         # TypeScript 설정
│   └── .env.example          # 환경 변수 예시
├── .gitignore               # Git 무시 파일 설정
└── README.md               # 프로젝트 문서
```

## 기술 스택

### 백엔드
- Django 5.2
- Django REST Framework
- PostgreSQL
- Google Cloud BigQuery
- OAuth2 (Google 로그인)

### 프론트엔드
- React.js 18+ with TypeScript
- React Router (HashRouter)
- Tremor Charts
- Axios

## 설치 및 실행

### Docker Compose로 실행

```bash
docker-compose up --build
```

Backend: http://localhost:8000
Frontend: http://localhost:3000
PostgreSQL: localhost:5432



### 백엔드 설정

1. 가상환경 생성 및 활성화
```bash
cd backend
python -m venv venv
source venv/bin/activate  # macOS/Linux
# 또는 venv\Scripts\activate  # Windows
```

2. 의존성 설치
```bash
pip install -r requirements.txt
```

3. 환경 변수 설정
```bash
cp .env.example .env
# .env 파일을 편집하여 실제 값으로 설정
```

4. 데이터베이스 마이그레이션
```bash
python manage.py makemigrations
python manage.py migrate
```

5. 개발 서버 실행
```bash
python manage.py runserver
```

### 프론트엔드 설정

1. 의존성 설치
```bash
cd frontend
npm install
```

2. 환경 변수 설정
```bash
cp .env.example .env
# .env 파일을 편집하여 실제 값으로 설정
```

3. 개발 서버 실행
```bash
npm start
```

## 대시보드 레이아웃 구성

### 레이아웃 스키마

대시보드는 새로운 DashboardLayout 스키마를 사용하여 반응형 그리드 레이아웃을 지원합니다:

```json
{
  "grid": {
    "mode": "strict-grid",
    "gap": 16,
    "breakpoints": {
      "sm": { "minWidth": 640, "columns": 1, "rowUnit": 200 },
      "md": { "minWidth": 768, "columns": 2, "rowUnit": 180 },
      "lg": { "minWidth": 1024, "columns": 3, "rowUnit": 160 },
      "xl": { "minWidth": 1280, "columns": 4, "rowUnit": 140 }
    }
  },
  "rows": [
    {
      "id": "kpi-row",
      "rowHeight": "auto",
      "items": [
        { "widgetRef": "revenue", "col": 1, "span": 1, "h": 1 },
        { "widgetRef": "orders", "col": 2, "span": 1, "h": 1 },
        { "widgetRef": "customers", "col": 3, "span": 1, "h": 1 },
        { "widgetRef": "conversion", "col": 4, "span": 1, "h": 1 }
      ]
    }
  ],
  "widgets": [
    {
      "id": "revenue",
      "type": "gauge",
      "title": "총 매출",
      "dataSource": { "componentId": 101 },
      "render": {
        "aspectRatio": 1.0,
        "minHeight": 120,
        "autoHeight": true,
        "overflow": "hidden"
      }
    }
  ]
}
```

### 위젯 렌더링 구성

각 위젯은 다음과 같은 렌더링 옵션을 지원합니다:

- **autoHeight**: 콘텐츠에 따른 자동 높이 조정
- **aspectRatio**: 가로세로 비율 유지 (예: 1.0 = 정사각형)
- **minHeight/maxHeight**: 최소/최대 높이 제한
- **fixedHeight**: 고정 높이 설정
- **overflow**: 콘텐츠 오버플로우 처리 방식

### 반응형 디자인

모바일 우선 접근 방식으로 다양한 화면 크기를 지원:

- **모바일 (기본)**: 1열 레이아웃
- **태블릿 (768px+)**: 2열 레이아웃  
- **데스크톱 (1024px+)**: 3열 레이아웃
- **대형 화면 (1280px+)**: 4열 레이아웃

자세한 레이아웃 구성 가이드는 [docs/layout-configuration-guide.md](docs/layout-configuration-guide.md)를 참조하세요.

## 개발 진행 상황

- [x] 프로젝트 초기 설정 및 환경 구성
- [x] Django 백엔드 기본 구조 구현
- [x] 인증 시스템 구현
- [x] React 프론트엔드 기본 구조 구현
- [x] 대시보드 레이아웃 구현
- [x] BigQuery 데이터 연동 구현
- [x] 차트 컴포넌트 구현
- [x] 그리드 레이아웃 시스템 구현
- [x] 반응형 레이아웃 및 위젯 렌더링 최적화
- [x] 보안 및 배포 준비
- [x] 테스트 구현
- [x] 통합 테스트 및 최종 검증

## 라이선스

Copyright reserved by kimjj81
