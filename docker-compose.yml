# version: "3.9" # version is obsolute

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: panelistbi
      POSTGRES_USER: panelistbi
      POSTGRES_PASSWORD: panelistbi
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  backend:
    build: ./backend
    command: bash -c "python manage.py migrate && uvicorn dynamic_bi_dashboard.asgi:application --host 0.0.0.0 --port 8000"
    volumes:
      - ./backend:/app
      - ./backend/credentials:/app/credentials:ro
    environment:
      DB_NAME: panelistbi
      DB_USER: panelistbi
      DB_PASSWORD: panelistbi
      DB_HOST: db
      DB_PORT: 5432
      # BigQuery 설정 (필요시 .env 파일에서 설정)
      # GOOGLE_CLOUD_PROJECT: your-project-id
      # GOOGLE_APPLICATION_CREDENTIALS: /app/credentials/service-account.json
    depends_on:
      - db
    ports:
      - "8000:8000"

  frontend:
    build: ./frontend
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    depends_on:
      - backend
    ports:
      - "3000:80"

volumes:
  postgres_data:
