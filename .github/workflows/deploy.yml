name: Deploy

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: ${{ secrets.GCP_REGION }}
  REPOSITORY: ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/panelistbi

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SERVICE_ACCOUNT_EMAIL }}

      - name: Set up gcloud
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Configure Docker
        run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Build backend image
        run: docker build -t $REPOSITORY/backend:${{ github.sha }} backend

      - name: Build frontend image
        run: docker build -t $REPOSITORY/frontend:${{ github.sha }} frontend

      - name: Push images
        run: |
          docker push $REPOSITORY/backend:${{ github.sha }}
          docker push $REPOSITORY/frontend:${{ github.sha }}

      - name: Deploy to Cloud Run
        run: |
          gcloud run deploy backend --image $REPOSITORY/backend:${{ github.sha }} --region $REGION --platform managed --allow-unauthenticated
          gcloud run deploy frontend --image $REPOSITORY/frontend:${{ github.sha }} --region $REGION --platform managed --allow-unauthenticated
