# Project Structure

## Root Directory Layout
```
dynamic-bi-dashboard/
├── backend/           # Django backend application
├── frontend/          # React frontend application
├── docs/             # Project documentation
├── docker-compose.yml # Container orchestration
└── README.md         # Project overview
```

## Backend Structure (`backend/`)
```
backend/
├── dynamic_bi_dashboard/    # Django project settings
│   ├── settings.py         # Main configuration
│   ├── urls.py            # URL routing
│   ├── middleware.py      # Custom middleware
│   └── exceptions.py      # Custom exception handlers
├── accounts/              # User authentication app
│   ├── models.py         # User, Organization models
│   ├── views.py          # Auth API views
│   ├── serializers.py    # DRF serializers
│   ├── oauth_views.py    # OAuth2 endpoints
│   └── permissions.py    # Custom permissions
├── dashboard/            # Dashboard management app
│   ├── models.py        # Page, Component models
│   ├── views.py         # Dashboard API views
│   ├── services.py      # BigQuery integration
│   └── serializers.py   # DRF serializers
├── credentials/         # Service account files
├── logs/               # Application logs
├── staticfiles/        # Collected static files
├── requirements.in     # Python dependencies (source)
├── requirements.txt    # Compiled dependencies
└── manage.py          # Django management script
```

## Frontend Structure (`frontend/`)
```
frontend/
├── public/
│   └── index.html        # HTML template
├── src/
│   ├── components/       # React components
│   │   ├── ui/          # Reusable UI components
│   │   └── __tests__/   # Component tests
│   ├── contexts/        # React contexts (AuthContext)
│   ├── services/        # API client and services
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   ├── styles/         # Global styles
│   ├── theme/          # Material theme configuration
│   ├── mocks/          # Mock data for development
│   ├── App.tsx         # Main application component
│   └── index.tsx       # Application entry point
├── package.json        # Node.js dependencies
└── tsconfig.json      # TypeScript configuration
```

## Django App Organization

### `accounts` App
- **Purpose**: User authentication, OAuth2, organization management
- **Models**: User (custom), Organization
- **Key Files**: oauth_views.py, oauth_services.py

### `dashboard` App  
- **Purpose**: Dashboard pages, components, templates, BigQuery integration
- **Models**: Page, PageComponent, ComponentTemplate, PageTemplate
- **Key Files**: services.py (BigQuery), management/commands/

## Naming Conventions

### Backend (Python/Django)
- **Files**: snake_case (e.g., `oauth_views.py`)
- **Classes**: PascalCase (e.g., `ComponentTemplate`)
- **Functions/Variables**: snake_case (e.g., `user_has_access`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `USER_ROLES`)

### Frontend (TypeScript/React)
- **Files**: PascalCase for components (e.g., `LoginPage.tsx`), camelCase for utilities
- **Components**: PascalCase (e.g., `DashboardLayout`)
- **Functions/Variables**: camelCase (e.g., `getCurrentUser`)
- **Types/Interfaces**: PascalCase (e.g., `AuthResponse`)

## Configuration Files
- **Backend**: `.env` for environment variables, `settings.py` for Django config
- **Frontend**: `.env` for environment variables, `tsconfig.json` for TypeScript
- **Docker**: `docker-compose.yml` for container orchestration
- **Dependencies**: `requirements.in` (backend), `package.json` (frontend)

## Key Architectural Patterns

### Backend
- **Django Apps**: Feature-based app organization (accounts, dashboard)
- **DRF ViewSets**: RESTful API endpoints with proper serialization
- **Custom Middleware**: Security headers, rate limiting, logging
- **Service Layer**: BigQuery integration in `dashboard/services.py`

### Frontend
- **Component Structure**: Organized by feature with shared UI components
- **Context API**: Global state management (AuthContext)
- **Service Layer**: Centralized API client in `services/api.ts`
- **Error Boundaries**: Component-level error handling