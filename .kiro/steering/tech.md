# Technology Stack

## Backend
- **Framework**: Django 5.2 with Django REST Framework
- **Database**: PostgreSQL (production) / SQLite (development)
- **Authentication**: JWT tokens via djangorestframework-simplejwt + OAuth2 (django-oauth-toolkit)
- **Data Source**: Google Cloud BigQuery with pandas-gbq
- **Server**: Uvicorn ASGI server
- **Environment Management**: python-decouple for configuration

## Frontend
- **Framework**: React 18+ with TypeScript
- **Routing**: React Router (HashRouter)
- **Charts**: ToastUI Chart (@toast-ui/react-chart)
- **HTTP Client**: Axios
- **UI Components**: Material Web Components (@material/web)
- **Build Tool**: Create React App

## Infrastructure
- **Containerization**: Docker with docker-compose
- **Database**: PostgreSQL 15
- **Static Files**: Django staticfiles

## Development Dependencies
- **Python**: pip-tools for dependency management
- **Node.js**: npm for frontend package management

## Common Commands

### Backend Development
```bash
# Setup virtual environment
cd backend
python -m venv venv
source venv/bin/activate  # macOS/Linux

# Install dependencies
pip install -r requirements.txt

# Database operations
python manage.py makemigrations
python manage.py migrate

# Create test data
python manage.py create_test_data

# Run development server
python manage.py runserver
```

### Frontend Development
```bash
cd frontend
npm install
npm start          # Development server
npm run build      # Production build
npm test           # Run tests
```

### Docker Development
```bash
# Full stack with Docker
docker-compose up --build

# Individual services
docker-compose up db          # Database only
docker-compose up backend     # Backend only
```

### Dependency Management
```bash
# Backend - update requirements
cd backend
pip-compile requirements.in

# Frontend - update packages
cd frontend
npm update
```

## Environment Configuration
- Backend: `.env` file with database, OAuth2, and BigQuery settings
- Frontend: `.env` file with API URL and OAuth2 client configuration
- Both have `.env.example` templates for reference