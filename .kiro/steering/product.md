# Product Overview

Dynamic BI Dashboard is a full-stack web application for creating and managing dynamic business intelligence dashboards. The system allows users to build interactive data visualizations using BigQuery as the data source.

## Key Features

- **Multi-tenant Architecture**: Organization-based access control with role-based permissions
- **Dynamic Dashboard Creation**: Users can create custom dashboards with various chart types
- **BigQuery Integration**: Direct connection to Google Cloud BigQuery for data retrieval
- **OAuth2 Authentication**: Google OAuth2 integration for secure user authentication
- **Role-based Access Control**: Three user roles (regular, org_admin, super_admin) with different permission levels
- **Template System**: Predefined page and component templates for quick dashboard creation
- **Grid Layout System**: Flexible grid-based layout for dashboard components

## User Roles

- **Regular User**: Can view dashboards assigned to their organization
- **Organization Admin**: Can manage users and dashboards within their organization
- **Super Admin**: Full system access across all organizations

## Primary Use Cases

1. Creating interactive BI dashboards from BigQuery data
2. Managing user access and permissions across organizations
3. Providing self-service analytics capabilities to business users
4. Standardizing dashboard layouts through templates