# Implementation Plan

- [x] 1. 프로젝트 초기 설정 및 환경 구성
  - Django 5.2 프로젝트 생성 및 기본 설정 구성
  - React.js TypeScript 프로젝트 생성 및 HashRouter 설정
  - .env 파일 구조 및 .gitignore 설정
  - 기본 폴더 구조 생성 (frontend/backend 분리)
  - _Requirements: 7.1, 7.3_

- [x] 2. Django 백엔드 기본 구조 구현
- [x] 2.1 Django 모델 및 데이터베이스 설정
  - User, Organization, Page, ComponentTemplate, PageComponent 모델 구현
  - Django 마이그레이션 파일 생성 및 적용
  - 권한 시스템 (USER_ROLES, PERMISSION_LEVELS) 구현
  - _Requirements: 2.1, 2.2, 2.3, 6.1, 6.2_

- [x] 2.2 Django REST Framework 기본 설정
  - DRF 설정 및 시리얼라이저 구현
  - 기본 API 뷰 클래스 구조 생성
  - 권한 클래스 및 인증 미들웨어 구현
  - _Requirements: 2.4, 7.4_

- [-] 3. 인증 시스템 구현
- [x] 3.1 이메일 기반 인증 API 구현
  - 회원가입, 로그인, 로그아웃 API 엔드포인트 구현
  - JWT 토큰 기반 인증 시스템 구현
  - 비밀번호 검증 및 보안 처리
  - _Requirements: 1.1, 1.3, 1.5_

- [x] 3.2 Google OAuth2 인증 구현
  - Django OAuth Toolkit 설정 및 Google OAuth2 연동
  - OAuth2 콜백 처리 및 사용자 정보 동기화
  - 소셜 로그인 API 엔드포인트 구현
  - _Requirements: 1.2_

- [x] 4. React 프론트엔드 기본 구조 구현
- [x] 4.1 인증 컴포넌트 구현
  - LoginPage 컴포넌트 및 폼 검증 구현
  - AuthProvider 컨텍스트 및 인증 상태 관리
  - 자동 로그인 기능 (localStorage 토큰 관리)
  - Google OAuth2 로그인 버튼 및 처리 로직
  - _Requirements: 1.1, 1.2, 1.4, 1.5_

- [x] 4.2 라우팅 및 보호된 라우트 구현
  - HashRouter 기반 라우팅 설정
  - PrivateRoute 컴포넌트로 인증 필요 페이지 보호
  - 권한별 접근 제어 로직 구현
  - _Requirements: 2.4, 8.1_

- [x] 5. 대시보드 레이아웃 구현
- [x] 5.1 대시보드 기본 레이아웃 구현
  - DashboardLayout 컴포넌트 (상단 헤더 + 페이지 네비게이션 + 콘텐츠 영역)
  - 사용자 정보 표시 및 로그아웃 버튼
  - 반응형 레이아웃 (모바일 대응)
  - _Requirements: 8.1, 8.4_

- [x] 5.2 페이지 네비게이션 구현
  - 가로 스크롤 가능한 페이지 탭 메뉴 구현
  - 페이지 클릭 시 하단 콘텐츠 영역 변경 로직
  - 활성 페이지 표시 및 상태 관리
  - _Requirements: 3.2, 8.2, 8.3_

- [x] 6. 백엔드 페이지 및 컴포넌트 API 구현
- [x] 6.1 페이지 관리 API 구현
  - 사용자 권한별 페이지 목록 조회 API
  - 페이지 상세 정보 및 레이아웃 조회 API
  - 권한 기반 필터링 로직 구현
  - _Requirements: 3.1, 3.3, 3.4_

- [x] 6.2 컴포넌트 템플릿 관리 API 구현
  - 컴포넌트 템플릿 CRUD API (관리자 전용)
  - 페이지 템플릿 CRUD API (관리자 전용)
  - 템플릿 기반 페이지 생성 API
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 7. BigQuery 데이터 연동 구현
- [x] 7.1 BigQuery 서비스 클래스 구현
  - Google Cloud BigQuery 클라이언트 설정
  - SQL 쿼리 실행 및 결과 처리 로직
  - 연결 오류 처리 및 재시도 메커니즘
  - _Requirements: 5.1_

- [x] 7.2 데이터 집계 및 파라미터 처리 구현
  - 시간 단위별 집계 로직 (분/시간/일/월)
  - 합계/윈도우 방식 데이터 처리
  - 동적 쿼리 파라미터 바인딩
  - _Requirements: 5.2, 5.3_

- [x] 7.3 컴포넌트 데이터 API 구현
  - GET /api/components/{component_id}/data/ 엔드포인트 구현
  - 쿼리 파라미터 검증 및 처리 (날짜 범위, 집계 옵션)
  - BigQuery 결과를 프론트엔드 차트 형식으로 변환
  - _Requirements: 5.4_

- [x] 8. 차트 컴포넌트 구현
- [x] 8.1 ToastUI 차트 기본 설정
  - ToastUI Chart 라이브러리 설치 및 설정
  - 차트 타입별 기본 컴포넌트 구조 생성
  - 차트 데이터 형식 표준화
  - _Requirements: 4.2_

- [x] 8.2 동적 차트 컴포넌트 구현
  - ChartComponent 범용 컴포넌트 구현
  - 15가지 차트 타입 지원 (Bar, Column, Line, Area, Pie, Bubble, Scatter, Heatmap, Treemap, Radar, BoxPlot, RadialBar, Gauge, SolidGauge)
  - API 데이터 바인딩 및 실시간 업데이트
  - _Requirements: 4.2, 4.3_

- [x] 8.3 차트 로딩 상태 및 에러 처리
  - 컴포넌트별 로딩 스피너 구현
  - 데이터 호출 성공 시점 표시 (컴포넌트 하단)
  - 로드 실패 시 "-" 표시 및 에러 상태 처리
  - _Requirements: 9.2, 9.3, 9.4_

- [x] 9. 그리드 레이아웃 시스템 구현
- [x] 9.1 ComponentGrid 구현
  - CSS Grid 기반 동적 레이아웃 시스템
  - 컴포넌트 위치 및 크기 동적 조정
  - 반응형 그리드 (모바일 대응)
  - _Requirements: 4.1, 4.4_

- [x] 9.2 페이지 데이터 로딩 관리
  - 페이지 전체 로딩 상태 표시
  - 컴포넌트별 비동기 데이터 로딩
  - 로딩 완료 후 레이아웃 렌더링
  - _Requirements: 9.1, 9.2_

- [x] 10. 페이지 새로고침 기능 구현
- [x] 10.1 새로고침 UI 및 상태 관리
  - 페이지별 새로고침 버튼 구현
  - 새로고침 중 그레이 오버레이 표시
  - 새로고침 진행 상태 표시
  - _Requirements: 10.1, 10.2_

- [x] 10.2 새로고침 에러 처리
  - 데이터 로딩 실패 시 팝업 표시
  - 이전 데이터 유지 및 복구 로직
  - 순차적 컴포넌트 데이터 재로드
  - _Requirements: 10.3, 10.4_

- [x] 11. 에러 처리 및 사용자 경험 개선
- [x] 11.1 프론트엔드 에러 바운더리 구현
  - ComponentErrorBoundary 구현
  - API 에러 처리 및 사용자 친화적 메시지 표시
  - 네트워크 오류 및 타임아웃 처리
  - _Requirements: 1.3_

- [x] 11.2 백엔드 예외 처리 구현
  - 커스텀 예외 클래스 정의
  - 글로벌 예외 핸들러 구현
  - API 응답 표준화 및 에러 코드 정의
  - _Requirements: 5.4, 7.4_

- [x] 12. 보안 및 배포 준비
- [x] 12.1 보안 설정 구현
  - CORS 설정 및 HTTPS 강제
  - JWT 토큰 보안 설정 (만료 시간, 리프레시)
  - 환경 변수 보안 검증
  - _Requirements: 7.1, 7.2, 7.4_

- [x] 12.2 배포 설정 및 최적화
  - React 빌드 최적화 및 정적 배포 설정
  - Django 프로덕션 설정 (DEBUG=False, ALLOWED_HOSTS)
  - 성능 최적화 (코드 스플리팅, 이미지 최적화)
  - _Requirements: 7.1_

- [x] 13. 테스트 구현
- [x] 13.1 백엔드 단위 테스트
  - 모델 테스트 (User, Page, ComponentTemplate)
  - API 엔드포인트 테스트
  - BigQuery 연동 테스트 (모킹)
  - _Requirements: 모든 백엔드 기능_

- [x] 13.2 프론트엔드 단위 테스트
  - 컴포넌트 렌더링 테스트
  - 인증 플로우 테스트
  - 차트 컴포넌트 데이터 바인딩 테스트
  - _Requirements: 모든 프론트엔드 기능_

- [x] 14. 통합 테스트 및 최종 검증
- [x] 14.1 E2E 테스트 시나리오 구현
  - 로그인부터 대시보드 조회까지 전체 플로우 테스트
  - 권한별 접근 제어 테스트
  - 데이터 새로고침 및 에러 처리 테스트
  - _Requirements: 전체 시스템 통합_

- [x] 14.2 성능 테스트 및 최적화
  - BigQuery 쿼리 성능 측정 및 최적화
  - 프론트엔드 렌더링 성능 측정
  - 동시 사용자 부하 테스트
  - _Requirements: 시스템 성능 요구사항_

- [x] 15. 미리 정의된 페이지와 컴포넌트 추가 명령어
  - 컴포넌트, 페이지 구성 예제를 제공한다.
  - 그래프 별 컴포넌트 템플릿 예제를 만든다.
  - 모든 컴포넌트가 하나의 페이지에 있는 템플릿을 만든다.
  - 한 페이지에 모든 종류의 그래프를 구성한다.

- [x] 16. Organization-Page N:N 관계 구현
  - PageOrganizationAccess 중간 모델 생성
  - Organization과 Page 간의 다대다 관계 설정
  - 기존 권한 레벨 기반 접근 제어와 조직 기반 접근 권한 통합
  - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5_

- [x] 16.1 백엔드 모델 및 마이그레이션
  - PageOrganizationAccess 모델 생성 (Organization, Page, granted_at, granted_by)
  - Page 모델에 accessible_organizations ManyToManyField 추가
  - Django 마이그레이션 생성 및 적용
  - _Requirements: 11.1_

- [x] 16.2 API 엔드포인트 수정
  - 페이지 목록 조회 로직 수정 (권한 레벨 + 조직 권한)
  - 페이지 접근 권한 확인 로직 수정
  - 권한 관리 API 추가 (관리자용)
  - _Requirements: 11.2, 11.3, 11.5_

- [x] 16.3 관리자용 권한 관리 API
  - POST /api/admin/pages/{page_id}/organizations/{org_id}/access/ (권한 부여)
  - DELETE /api/admin/pages/{page_id}/organizations/{org_id}/access/revoke/ (권한 해제)
  - GET /api/admin/pages/{page_id}/organizations/ (페이지별 접근 권한 조직 목록)
  - GET /api/admin/organizations/{org_id}/pages/ (조직별 접근 가능 페이지)
  - _Requirements: 11.1, 11.4_

- [x] 16.4 프론트엔드 권한 관리 UI (선택사항)
  - 관리자 페이지에 조직-페이지 권한 관리 인터페이스 추가 (수퍼 관리자 전용)
  - 페이지별 조직 할당/해제 기능 구현
  - 조직별 페이지 접근 권한 조회 기능 구현
  - 사용자의 조직 정보 표시 개선
  - AdminPanel 또는 PermissionManagement 컴포넌트에 새로운 탭 추가
  - _Requirements: 11.1, 11.4_

- [x] 16.5 테스트 추가
  - Organization-Page N:N 관계 모델 테스트
  - 조직 기반 권한 API 접근 테스트
  - 권한 레벨과 조직 권한 조합 테스트
  - _Requirements: 전체 권한 시스템_
