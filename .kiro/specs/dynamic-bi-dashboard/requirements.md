# Requirements Document

## Introduction

동적 BI 대시보드 생성 앱은 사용자가 템플릿과 컴포넌트를 정의하여 동적으로 비즈니스 인텔리전스 대시보드를 생성할 수 있는 웹 애플리케이션입니다. React.js 기반의 SPA 프론트엔드와 Django 5.2 백엔드로 구성되며, 권한 기반 접근 제어와 BigQuery 데이터 연동을 지원합니다.

## Requirements

### Requirement 1

**User Story:** 사용자로서, 이메일 또는 Google OAuth2를 통해 로그인하고 싶습니다. 그래야 개인화된 대시보드에 접근할 수 있습니다.

#### Acceptance Criteria

1. WHEN 사용자가 이메일과 비밀번호를 입력하고 로그인 버튼을 클릭 THEN 시스템은 인증을 수행하고 성공 시 대시보드 화면으로 이동해야 합니다
2. WHEN 사용자가 Google OAuth2 로그인 버튼을 클릭 THEN 시스템은 Google 인증 페이지로 리다이렉트하고 인증 완료 후 대시보드로 이동해야 합니다
3. WHEN 로그인이 실패 THEN 시스템은 적절한 오류 메시지를 표시해야 합니다
4. WHEN 사용자가 자동로그인을 선택 THEN 시스템은 다음 방문 시 자동으로 로그인해야 합니다
5. WHEN 사용자가 회원가입을 요청 THEN 시스템은 이메일 기반 회원가입 프로세스를 제공해야 합니다

### Requirement 2

**User Story:** 시스템 관리자로서, 사용자에게 역할 기반 권한을 부여하고 싶습니다. 그래야 적절한 수준의 접근 권한을 제어할 수 있습니다.

#### Acceptance Criteria

1. WHEN 일반 사용자가 로그인 THEN 시스템은 조회 권한만 부여하고 편집 기능을 제한해야 합니다
2. WHEN 조직 관리자가 로그인 THEN 시스템은 조직 내 계정 조회 및 신규 계정 승인 권한을 부여해야 합니다
3. WHEN 운영 관리자가 로그인 THEN 시스템은 모든 기능과 Django 어드민 페이지 접근 권한을 부여해야 합니다
4. WHEN 사용자가 권한이 없는 페이지에 접근 시도 THEN 시스템은 접근을 차단하고 적절한 메시지를 표시해야 합니다

### Requirement 3

**User Story:** 사용자로서, 권한에 따라 볼 수 있는 대시보드 페이지 목록을 확인하고 싶습니다. 그래야 접근 가능한 대시보드를 쉽게 찾을 수 있습니다.

#### Acceptance Criteria

1. WHEN 사용자가 대시보드 메인 화면에 접근 THEN 시스템은 사용자 권한에 맞는 페이지 목록을 표시해야 합니다
2. WHEN 페이지가 많을 경우 THEN 시스템은 가로 스크롤을 제공하여 모든 페이지를 탐색할 수 있게 해야 합니다
3. WHEN 일반 사용자가 접근 THEN 시스템은 "일반 사용자" 권한 페이지만 표시해야 합니다
4. WHEN 조직 관리자가 접근 THEN 시스템은 "조직 관리자" 및 "일반 사용자" 권한 페이지를 표시해야 합니다

### Requirement 4

**User Story:** 사용자로서, 선택한 페이지의 BI 컴포넌트들을 그리드 레이아웃으로 보고 싶습니다. 그래야 데이터를 시각적으로 분석할 수 있습니다.

#### Acceptance Criteria

1. WHEN 사용자가 페이지를 선택 THEN 시스템은 페이지 정의에 따라 그리드 레이아웃으로 컴포넌트들을 배치해야 합니다
2. WHEN 페이지에 차트 컴포넌트가 포함 THEN 시스템은 ToastUI 차트를 사용하여 Bar, Column, Line, Area, Pie, Bubble, Scatter, Heatmap, Treemap, Radar, BoxPlot, RadialBar, Gauge, SolidGauge 차트를 렌더링해야 합니다
3. WHEN 컴포넌트가 API 엔드포인트를 가지고 있 THEN 시스템은 해당 API를 호출하여 실시간 데이터를 표시해야 합니다
4. WHEN 모바일 기기에서 접근 THEN 시스템은 반응형 레이아웃으로 컴포넌트들을 적절히 배치해야 합니다

### Requirement 5

**User Story:** 개발자로서, BigQuery에서 데이터를 조회하는 API를 구성하고 싶습니다. 그래야 실시간 데이터 기반 대시보드를 제공할 수 있습니다.

#### Acceptance Criteria

1. WHEN 컴포넌트가 데이터를 요청 THEN 시스템은 BigQuery SQL을 실행하여 데이터를 조회해야 합니다
2. WHEN API 파라미터에 집계 단위가 포함 THEN 시스템은 분/시간/일/월 단위로 데이터를 집계해야 합니다
3. WHEN 기간별 집계 요청 THEN 시스템은 합계 또는 윈도우 방식으로 데이터를 처리해야 합니다
4. WHEN 복잡한 파라미터 조합이 필요 THEN 시스템은 별도의 전용 API 엔드포인트를 제공해야 합니다

### Requirement 6

**User Story:** 관리자로서, 컴포넌트 템플릿과 페이지 템플릿을 정의하고 싶습니다. 그래야 재사용 가능한 대시보드 구성 요소를 만들 수 있습니다.

#### Acceptance Criteria

1. WHEN 관리자가 컴포넌트 템플릿을 생성 THEN 시스템은 제목, 차트 종류, API 엔드포인트, API 파라미터, BigQuery SQL을 저장해야 합니다
2. WHEN 관리자가 페이지 템플릿을 생성 THEN 시스템은 그리드 레이아웃과 컴포넌트 배치 정보를 저장해야 합니다
3. WHEN 페이지 템플릿을 사용하여 새 페이지 생성 THEN 시스템은 템플릿 구조를 복사하여 새로운 페이지를 만들어야 합니다
4. WHEN 컴포넌트 템플릿을 페이지에 추가 THEN 시스템은 선택된 템플릿을 페이지 레이아웃에 삽입해야 합니다

### Requirement 7

**User Story:** 사용자로서, 안전한 환경에서 애플리케이션을 사용하고 싶습니다. 그래야 데이터 보안이 보장됩니다.

#### Acceptance Criteria

1. WHEN 애플리케이션이 배포 THEN 시스템은 환경 변수를 통해 설정을 관리하고 프론트엔드에 민감한 정보를 노출하지 않아야 합니다
2. WHEN 백엔드에서 외부 서비스 연동 THEN 시스템은 API 키와 인증 정보를 안전하게 관리해야 합니다
3. WHEN Git 저장소에 코드 커밋 THEN 시스템은 .gitignore를 통해 민감한 파일들을 제외해야 합니다
4. WHEN 사용자 세션 관리 THEN 시스템은 적절한 세션 타임아웃과 보안 헤더를 설정해야 합니다

### Requirement 8

**User Story:** 사용자로서, 직관적인 사용자 인터페이스를 통해 대시보드를 탐색하고 싶습니다. 그래야 효율적으로 데이터를 분석할 수 있습니다.

#### Acceptance Criteria

1. WHEN 사용자가 대시보드에 접근 THEN 시스템은 상단에 사용자 정보와 로그아웃 버튼을 표시해야 합니다
2. WHEN 페이지 목록이 표시 THEN 시스템은 가로 스크롤 가능한 메뉴 형태로 페이지들을 나열해야 합니다
3. WHEN 사용자가 페이지 이름을 클릭 THEN 시스템은 하단 영역에 해당 페이지 내용을 표시해야 합니다
4. WHEN 모바일에서 접근 THEN 시스템은 터치 친화적인 인터페이스와 적절한 크기 조정을 제공해야 합니다

### Requirement 9

**User Story:** 사용자로서, 데이터 로딩 상태를 명확히 확인하고 싶습니다. 그래야 시스템이 정상 작동하는지 알 수 있습니다.

#### Acceptance Criteria

1. WHEN 페이지가 처음 로드 THEN 시스템은 데이터 호출 중임을 표시해야 합니다
2. WHEN 각 컴포넌트의 데이터가 로딩 중 THEN 시스템은 컴포넌트별로 비동기 로딩 상태를 표시해야 합니다
3. WHEN 컴포넌트 데이터 로딩이 완료 THEN 시스템은 컴포넌트 하단에 데이터 호출 성공 시점을 작게 표시해야 합니다
4. WHEN 컴포넌트가 한 번도 성공적으로 로드되지 않았 THEN 시스템은 시점 표시 영역에 "-"를 표시해야 합니다

### Requirement 10

**User Story:** 사용자로서, 페이지 데이터를 새로고침하고 싶습니다. 그래야 최신 데이터를 확인할 수 있습니다.

#### Acceptance Criteria

1. WHEN 사용자가 페이지 새로고침 버튼을 클릭 THEN 시스템은 해당 페이지의 모든 컴포넌트 데이터를 순차적으로 다시 로드해야 합니다
2. WHEN 새로고침이 진행 중 THEN 시스템은 해당 페이지를 그레이 처리하여 새로고침 상태임을 표시해야 합니다
3. WHEN 새로고침 중 데이터 호출이 실패 THEN 시스템은 실패 팝업을 표시하고 이전 데이터와 화면을 유지해야 합니다
4. WHEN 새로고침이 성공적으로 완료 THEN 시스템은 그레이 처리를 해제하고 새로운 데이터를 표시해야 합니다

### Requirement 11

**User Story:** 관리자로서, 특정 조직에 페이지 접근 권한을 할당하고 싶습니다. 그래야 조직 단위로 효율적인 접근 제어가 가능합니다.

#### Acceptance Criteria

1. WHEN 관리자가 조직에 특정 페이지 접근 권한을 부여 THEN 시스템은 해당 조직의 모든 사용자가 그 페이지에 접근할 수 있도록 해야 합니다
2. WHEN 사용자가 로그인 THEN 시스템은 권한 레벨뿐만 아니라 소속 조직에 할당된 페이지들도 표시해야 합니다
3. WHEN 조직에 페이지 접근 권한이 있는 사용자 THEN 시스템은 해당 페이지의 권한 레벨과 관계없이 접근을 허용해야 합니다
4. WHEN 관리자가 조직의 페이지 접근 권한을 해제 THEN 시스템은 해당 조직의 사용자들이 더 이상 그 페이지에 접근할 수 없도록 해야 합니다
5. WHEN 사용자가 권한이 없는 페이지에 직접 접근 시도 THEN 시스템은 접근을 차단하고 적절한 오류 메시지를 표시해야 합니다