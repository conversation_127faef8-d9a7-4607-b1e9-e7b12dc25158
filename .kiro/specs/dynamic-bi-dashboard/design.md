# Design Document

## Overview

동적 BI 대시보드 앱은 React.js SPA 프론트엔드와 Django 5.2 백엔드로 구성된 풀스택 웹 애플리케이션입니다. 사용자는 권한에 따라 다양한 BI 대시보드 페이지에 접근하고, BigQuery 데이터를 기반으로 한 실시간 차트와 테이블을 확인할 수 있습니다.

## Architecture

### System Architecture

```mermaid
graph TB
    subgraph "Frontend (React SPA)"
        A[Login Page] --> B[Dashboard Layout]
        B --> C[Page Navigation]
        B --> D[Component Grid]
        D --> E[Chart Components]
        D --> F[Table Components]
    end
    
    subgraph "Backend (Django 5.2)"
        G[Authentication API] --> H[User Management]
        I[Dashboard API] --> J[Page Service]
        I --> K[Component Service]
        L[Data API] --> M[BigQuery Service]
    end
    
    subgraph "External Services"
        N[Google OAuth2]
        O[BigQuery]
    end
    
    A --> G
    C --> I
    E --> L
    F --> L
    G --> N
    L --> O
```

### Technology Stack

**Frontend:**
- React.js 18+ with TypeScript
- React Router (HashRouter for static deployment)
- ToastUI Chart for data visualization
- Axios for API communication
- CSS Grid/Flexbox for responsive layout
- Material-UI or Tailwind CSS for UI components

**Backend:**
- Django 5.2 with Django REST Framework
- PostgreSQL for application data
- Google Cloud BigQuery for analytics data
- Django OAuth Toolkit for OAuth2 integration
- Celery for background tasks (optional)
- Redis for caching (optional)

**Infrastructure:**
- Static hosting for frontend (Netlify/Vercel)
- Cloud hosting for backend (GCP/AWS)
- Environment-based configuration

## Components and Interfaces

### Frontend Components

#### 1. Authentication Components
```typescript
// LoginPage.tsx
interface LoginPageProps {
  onLogin: (token: string) => void;
}

// AuthProvider.tsx
interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}
```

#### 2. Dashboard Components
```typescript
// DashboardLayout.tsx
interface DashboardLayoutProps {
  user: User;
  pages: Page[];
  activePage: string;
  onPageChange: (pageId: string) => void;
  onRefresh: (pageId: string) => void;
}

// PageNavigation.tsx
interface PageNavigationProps {
  pages: Page[];
  activePage: string;
  onPageSelect: (pageId: string) => void;
  isRefreshing: boolean;
}

// ComponentGrid.tsx
interface ComponentGridProps {
  layout: GridLayout;
  components: ComponentDefinition[];
  isLoading: boolean;
}
```

#### 3. Chart Components
```typescript
// ChartComponent.tsx
interface ChartComponentProps {
  id: string;
  type: ChartType;
  title: string;
  apiEndpoint: string;
  apiParams: Record<string, any>;
  gridPosition: GridPosition;
}

// ChartTypes
type ChartType = 
  | 'bar' | 'column' | 'line' | 'area' | 'pie' 
  | 'bubble' | 'scatter' | 'heatmap' | 'treemap' 
  | 'radar' | 'boxplot' | 'radialbar' | 'gauge' | 'solidgauge';
```

### Backend API Interfaces

#### 1. Authentication APIs
```python
# Authentication endpoints
POST /api/auth/login/
POST /api/auth/google/
POST /api/auth/logout/
POST /api/auth/register/
GET /api/auth/user/
```

#### 2. Dashboard APIs
```python
# Dashboard management endpoints
GET /api/pages/                    # Get user's accessible pages
GET /api/pages/{page_id}/          # Get page definition

# Component data endpoints
GET /api/components/{component_id}/data/  # Get component data with query parameters
# Query parameters:
# - start_date, end_date: Date range filtering
# - aggregation: minute/hour/day/month
# - aggregation_type: sum/avg/count/window
# - custom_params: Component-specific parameters
```

#### 3. Admin APIs
```python
# Template management (admin only)
GET /api/admin/component-templates/
POST /api/admin/component-templates/
GET /api/admin/page-templates/
POST /api/admin/page-templates/
```

## Data Models

### Django Models

#### 1. User and Permission Models
```python
# models.py
class User(AbstractUser):
    email = models.EmailField(unique=True)
    role = models.CharField(max_length=20, choices=USER_ROLES)
    organization = models.ForeignKey('Organization', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

class Organization(models.Model):
    name = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

USER_ROLES = [
    ('regular', 'Regular User'),
    ('org_admin', 'Organization Admin'),
    ('super_admin', 'Super Admin'),
]
```

#### 2. Page and Component Models
```python
class Page(models.Model):
    name = models.CharField(max_length=100)
    permission_level = models.CharField(max_length=20, choices=PERMISSION_LEVELS)
    layout_config = models.JSONField()  # Grid layout configuration
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

class ComponentTemplate(models.Model):
    name = models.CharField(max_length=100)
    chart_type = models.CharField(max_length=20, choices=CHART_TYPES)
    api_endpoint = models.CharField(max_length=200)
    api_params = models.JSONField(default=dict)
    bigquery_sql = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

class PageComponent(models.Model):
    page = models.ForeignKey(Page, on_delete=models.CASCADE)
    component_template = models.ForeignKey(ComponentTemplate, on_delete=models.CASCADE)
    grid_position = models.JSONField()  # {x, y, width, height}
    custom_params = models.JSONField(default=dict)
    order = models.IntegerField(default=0)

PERMISSION_LEVELS = [
    ('regular', 'Regular User'),
    ('org_admin', 'Organization Admin'),
]

CHART_TYPES = [
    ('bar', 'Bar Chart'),
    ('column', 'Column Chart'),
    ('line', 'Line Chart'),
    ('area', 'Area Chart'),
    ('pie', 'Pie Chart'),
    ('bubble', 'Bubble Chart'),
    ('scatter', 'Scatter Chart'),
    ('heatmap', 'Heatmap'),
    ('treemap', 'Treemap'),
    ('radar', 'Radar Chart'),
    ('boxplot', 'Box Plot'),
    ('radialbar', 'Radial Bar'),
    ('gauge', 'Gauge'),
    ('solidgauge', 'Solid Gauge'),
]
```

### Frontend Data Models

#### 1. Page Layout Structure
```typescript
interface GridLayout {
  type: 'grid';
  columns: number;
  gap: string;
}

interface ComponentDefinition {
  id: string;
  type: 'chart' | 'table' | 'text';
  props: ChartProps | TableProps | TextProps;
  gridPosition: GridPosition;
  lastUpdated?: string;
}

interface GridPosition {
  x: number;
  y: number;
  width: number;  // percentage
  height: number; // pixels or auto
}
```

#### 2. API Response Models
```typescript
interface PageResponse {
  id: string;
  name: string;
  layout: GridLayout;
  components: ComponentDefinition[];
  permission_level: string;
}

interface ChartDataResponse {
  categories: string[];
  series: SeriesData[];
  lastUpdated: string;
}

interface SeriesData {
  name: string;
  data: number[];
  type?: string;
}
```

## Error Handling

### Frontend Error Handling

#### 1. API Error Handling
```typescript
// api/client.ts
class ApiClient {
  async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await axios(config);
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        // Redirect to login
        window.location.hash = '#/login';
      } else if (error.response?.status === 403) {
        // Show permission error
        throw new PermissionError('Access denied');
      } else if (error.response?.status >= 500) {
        // Show server error
        throw new ServerError('Server error occurred');
      }
      throw error;
    }
  }
}
```

#### 2. Component Error Boundaries
```typescript
// ErrorBoundary.tsx
class ComponentErrorBoundary extends React.Component {
  state = { hasError: false, error: null };
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  render() {
    if (this.state.hasError) {
      return <ComponentErrorFallback error={this.state.error} />;
    }
    return this.props.children;
  }
}
```

### Backend Error Handling

#### 1. Custom Exception Classes
```python
# exceptions.py
class BigQueryError(Exception):
    """BigQuery operation failed"""
    pass

class PermissionDeniedError(Exception):
    """User lacks required permissions"""
    pass

class DataValidationError(Exception):
    """Invalid data provided"""
    pass
```

#### 2. Global Exception Handler
```python
# views.py
from rest_framework.views import exception_handler
from rest_framework.response import Response

def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)
    
    if response is not None:
        custom_response_data = {
            'error': True,
            'message': str(exc),
            'status_code': response.status_code
        }
        response.data = custom_response_data
    
    return response
```

## Testing Strategy

### Frontend Testing

#### 1. Unit Tests
- Component rendering tests with React Testing Library
- Hook functionality tests
- Utility function tests
- API client tests with mocked responses

#### 2. Integration Tests
- User authentication flow
- Page navigation and data loading
- Chart component data binding
- Error handling scenarios

#### 3. E2E Tests
- Complete user journey from login to dashboard viewing
- Permission-based access control
- Data refresh functionality
- Mobile responsive behavior

### Backend Testing

#### 1. Unit Tests
```python
# tests/test_models.py
class TestPageModel(TestCase):
    def test_page_creation(self):
        page = Page.objects.create(
            name="Test Page",
            permission_level="regular",
            layout_config={"columns": 100}
        )
        self.assertEqual(page.name, "Test Page")

# tests/test_views.py
class TestPageAPI(APITestCase):
    def test_get_user_pages(self):
        response = self.client.get('/api/pages/')
        self.assertEqual(response.status_code, 200)
```

#### 2. Integration Tests
- BigQuery connection and query execution
- OAuth2 authentication flow
- Permission-based API access
- Data aggregation functionality

#### 3. Performance Tests
- BigQuery query performance
- API response times
- Concurrent user handling
- Memory usage optimization

### Test Data Management

#### 1. Frontend Test Data
```typescript
// __mocks__/testData.ts
export const mockPages: Page[] = [
  {
    id: '1',
    name: 'Sales Dashboard',
    permission_level: 'regular',
    layout: { type: 'grid', columns: 100, gap: '16px' },
    components: [
      {
        id: 'chart1',
        type: 'chart',
        props: { chartType: 'bar', title: 'Monthly Sales' },
        gridPosition: { x: 0, y: 0, width: 50, height: 300 }
      }
    ]
  }
];
```

#### 2. Backend Test Fixtures
```python
# fixtures/test_data.json
[
  {
    "model": "dashboard.page",
    "pk": 1,
    "fields": {
      "name": "Test Dashboard",
      "permission_level": "regular",
      "layout_config": {"columns": 100}
    }
  }
]
```

## Security Considerations

### Authentication & Authorization
- JWT tokens for API authentication
- OAuth2 integration with Google
- Role-based access control (RBAC)
- Session management and timeout

### Data Protection
- Environment variables for sensitive configuration
- HTTPS enforcement
- CORS configuration
- SQL injection prevention through ORM
- XSS protection through content sanitization

### BigQuery Security
- Service account authentication
- Query parameter sanitization
- Row-level security implementation
- Audit logging for data access