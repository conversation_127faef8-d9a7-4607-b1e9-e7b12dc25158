# Design Document

## Overview

This design addresses the comprehensive theme system fixes for the Dynamic BI Dashboard. The solution involves replacing hardcoded inline styles with Tailwind CSS classes that respond to DaisyUI themes, removing legacy UI library references, and ensuring consistent theme application across all components.

## Architecture

### Theme Application Strategy

The theme system will use DaisyUI's `data-theme` attribute approach, which is already implemented at the document level. The key is to replace all hardcoded styles with Tailwind classes that reference DaisyUI's CSS custom properties.

**Current State:**
- Theme context properly sets `data-theme` on document element
- Nav<PERSON> uses DaisyUI classes and responds to themes
- Main content uses hardcoded inline styles that ignore themes

**Target State:**
- All components use Tailwind classes with DaisyUI theme variables
- No hardcoded colors or inline styles for theming
- Consistent theme application across entire application

### DaisyUI Theme Integration

DaisyUI provides semantic color tokens that automatically adjust based on the selected theme:
- `bg-base-100`, `bg-base-200`, `bg-base-300` for backgrounds
- `text-base-content` for primary text
- `text-base-content/70` for secondary text
- `bg-primary`, `text-primary-content` for accent elements
- `border-base-300` for borders

## Components and Interfaces

### 1. Dashboard Component Redesign

**Current Issues:**
- Uses hardcoded white backgrounds (`backgroundColor: 'white'`)
- Uses hardcoded colors (`color: '#333'`, `color: '#666'`)
- Uses hardcoded box shadows and borders

**Design Solution:**
```tsx
// Replace hardcoded styles with DaisyUI classes
<div className="bg-base-100 p-8 rounded-lg shadow-lg">
  <h2 className="text-3xl font-bold text-base-content mb-6">
    대시보드 메인
  </h2>
  <p className="text-base-content/70 text-base leading-relaxed mb-6">
    대시보드 컴포넌트가 여기에 구현될 예정입니다.
  </p>
  
  <div className="mt-8 p-6 bg-base-200 rounded-lg border border-base-300">
    <h3 className="text-xl font-semibold text-base-content mb-4">
      사용자 정보
    </h3>
    {/* User info grid with theme-aware styling */}
  </div>
</div>
```

### 2. ThemeSelector Component Enhancement

**Current Issues:**
- Uses hardcoded colors for modal background and content
- Uses basic HTML styling instead of DaisyUI components

**Design Solution:**
```tsx
// Use DaisyUI modal and card components
<div className="modal modal-open">
  <div className="modal-box bg-base-100 text-base-content">
    <h3 className="font-bold text-lg mb-4">테마 선택</h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      {availableThemes.map((theme) => (
        <div className="card bg-base-200 hover:bg-base-300 cursor-pointer transition-colors">
          {/* Theme option content */}
        </div>
      ))}
    </div>
    
    <div className="modal-action">
      <button className="btn btn-ghost">취소</button>
    </div>
  </div>
</div>
```

### 3. Page Content Areas

**Design Solution:**
- Replace all hardcoded background colors with `bg-base-100` or `bg-base-200`
- Replace hardcoded text colors with `text-base-content` and `text-base-content/70`
- Use DaisyUI loading components instead of custom loading overlays
- Apply consistent spacing using Tailwind spacing classes

### 4. Legacy Code Cleanup

**Material Web Components:**
- Remove `frontend/src/types/material-web.d.ts` file
- Verify no components are using Material Web elements

**Package Dependencies:**
- Confirm no Material UI or Toast UI packages in package.json
- Remove any unused imports or references

## Data Models

No data model changes are required. The theme system uses existing user preferences and localStorage for persistence.

## Error Handling

### Theme Fallback Strategy

```tsx
// Enhanced theme initialization with better error handling
const initializeTheme = (userTheme?: string) => {
  try {
    const validTheme = AVAILABLE_THEMES.find(t => t.key === userTheme)?.key;
    const fallbackTheme = validTheme || DEFAULT_THEME;
    
    setCurrentTheme(fallbackTheme);
    localStorage.setItem('ui-theme', fallbackTheme);
    document.documentElement.setAttribute('data-theme', fallbackTheme);
  } catch (error) {
    console.warn('Theme initialization failed, using default:', error);
    document.documentElement.setAttribute('data-theme', DEFAULT_THEME);
  }
};
```

### CSS Loading Protection

Ensure DaisyUI styles are loaded before theme application:
```tsx
// Wait for styles to be available
useEffect(() => {
  const checkStylesLoaded = () => {
    const testElement = document.createElement('div');
    testElement.className = 'bg-base-100';
    document.body.appendChild(testElement);
    
    const styles = window.getComputedStyle(testElement);
    const hasStyles = styles.backgroundColor !== 'rgba(0, 0, 0, 0)';
    
    document.body.removeChild(testElement);
    return hasStyles;
  };
  
  if (checkStylesLoaded()) {
    applyTheme(currentTheme);
  } else {
    // Retry after a short delay
    setTimeout(() => applyTheme(currentTheme), 100);
  }
}, [currentTheme]);
```

## Testing Strategy

### Visual Regression Testing

1. **Theme Switching Tests:**
   - Verify all components update when theme changes
   - Test theme persistence across page reloads
   - Validate theme initialization from user preferences

2. **Component Integration Tests:**
   - Test Dashboard component with different themes
   - Verify ThemeSelector modal functionality
   - Test loading states with theme-appropriate colors

3. **Accessibility Testing:**
   - Verify color contrast ratios in all themes
   - Test keyboard navigation in ThemeSelector
   - Validate screen reader compatibility

### Unit Tests

```tsx
// Example test for theme-aware component
describe('Dashboard Component', () => {
  it('should apply theme-appropriate classes', () => {
    render(
      <ThemeProvider defaultTheme="dark">
        <Dashboard />
      </ThemeProvider>
    );
    
    expect(document.documentElement).toHaveAttribute('data-theme', 'dark');
    expect(screen.getByRole('main')).toHaveClass('bg-base-100');
  });
});
```

### Manual Testing Checklist

1. Switch between all available themes
2. Verify theme persistence after page reload
3. Test theme selector modal in different themes
4. Validate responsive behavior with themes
5. Check loading states and overlays
6. Verify no hardcoded colors remain visible

## Implementation Considerations

### Performance

- DaisyUI themes use CSS custom properties, providing efficient theme switching
- No JavaScript color calculations required
- Minimal bundle size impact

### Browser Compatibility

- CSS custom properties supported in all modern browsers
- Fallback colors defined in Tailwind configuration
- Progressive enhancement approach

### Maintenance

- Centralized theme definitions in ThemeContext
- Consistent naming conventions for theme-related classes
- Clear separation between theme logic and component logic