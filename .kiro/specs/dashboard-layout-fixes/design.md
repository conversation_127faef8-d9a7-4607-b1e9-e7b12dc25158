# Design Document

## Overview

This design addresses the layout consistency issues between backend and frontend in the Dynamic BI Dashboard system. The solution involves standardizing the layout schema implementation, fixing component sizing and overflow issues, and ensuring proper responsive behavior across different screen sizes.

## Architecture

### Current State Analysis

**Backend Layout Schemas:**
1. **Legacy Schema**: Uses `grid_position` with `{x, y, w, h}` coordinates
2. **New Schema**: Uses `DashboardLayout` with `{grid, rows, widgets}` structure including render configurations

**Frontend Implementation Issues:**
1. `DashboardGrid` component doesn't properly apply widget render configurations
2. Component overflow due to missing CSS constraints
3. Inconsistent height calculation between `autoHeight` and `fixedHeight` modes
4. Missing aspect ratio enforcement

### Target Architecture

```mermaid
graph TB
    A[Backend Layout Config] --> B[Layout Schema Validator]
    B --> C[Standardized Layout Object]
    C --> D[Frontend Layout Renderer]
    D --> E[Component Grid System]
    E --> F[Individual Chart Components]
    
    G[Layout Editor UI] --> H[Layout Config Generator]
    H --> A
    
    I[Responsive Breakpoint Manager] --> D
    J[Component Size Calculator] --> F
```

## Components and Interfaces

### 1. Backend Schema Standardization

**Enhanced WidgetRenderConfig Interface:**
```typescript
interface WidgetRenderConfig {
  autoHeight?: boolean;
  aspectRatio?: number;
  minHeight?: number;
  maxHeight?: number;
  fixedHeight?: number;
  overflow?: 'hidden' | 'scroll' | 'visible';
  padding?: number;
}
```

**Layout Validation Service:**
```python
class LayoutConfigValidator:
    def validate_dashboard_layout(self, layout_config: dict) -> ValidationResult
    def migrate_legacy_layout(self, grid_position: dict) -> dict
    def normalize_widget_config(self, widget: dict) -> dict
```

### 2. Frontend Component Enhancements

**Enhanced DashboardGrid Component:**
- Proper render config application
- CSS Grid with overflow control
- Dynamic height calculation
- Responsive breakpoint handling

**Component Size Calculator:**
```typescript
class ComponentSizeCalculator {
  calculateDimensions(
    widget: Widget, 
    containerWidth: number, 
    breakpoint: GridBreakpoint
  ): ComponentDimensions
  
  applyAspectRatio(width: number, aspectRatio: number): number
  enforceConstraints(height: number, constraints: WidgetRenderConfig): number
}
```

### 3. CSS Layout System

**Modern CSS Framework Integration:**
- **Tailwind CSS v3.4+**: Utility-first CSS framework for responsive design
- **DaisyUI v4.x**: Component library built on Tailwind CSS
- **Tremor v3.x**: Dashboard component library for React

**Grid Container Implementation:**
```typescript
// Using Tailwind CSS classes with dynamic values
const gridClasses = `
  grid 
  gap-${gap} 
  grid-cols-${breakpoint.columns}
  auto-rows-min
  w-full
  h-full
`;

// DaisyUI card components for containers
const cardClasses = `
  card 
  bg-base-100 
  shadow-lg 
  overflow-hidden
  flex 
  flex-col
  min-h-0
`;
```

**Component Sizing with Tailwind:**
```typescript
// Dynamic aspect ratio using Tailwind's aspect-ratio utilities
const aspectRatioClass = aspectRatio === 1.0 ? 'aspect-square' : 
                        aspectRatio === 0.5 ? 'aspect-[2/1]' :
                        aspectRatio === 0.6 ? 'aspect-[5/3]' :
                        aspectRatio === 0.8 ? 'aspect-[5/4]' : 'aspect-auto';

// Height constraints using Tailwind utilities
const heightClasses = `
  ${minHeight ? `min-h-[${minHeight}px]` : ''}
  ${maxHeight ? `max-h-[${maxHeight}px]` : ''}
  ${fixedHeight ? `h-[${fixedHeight}px]` : 'h-auto'}
`;
```

**Tremor Integration for Charts:**
```typescript
// Use Tremor's responsive chart containers
import { Card, Title } from '@tremor/react';

const ChartContainer = ({ children, title, renderConfig }) => (
  <Card className={`
    ${heightClasses}
    ${aspectRatioClass}
    overflow-hidden
    flex
    flex-col
  `}>
    <Title className="truncate">{title}</Title>
    <div className="flex-1 min-h-0 overflow-hidden">
      {children}
    </div>
  </Card>
);
```

## Data Models

### Enhanced Page Model

```python
class Page(models.Model):
    # ... existing fields ...
    layout_version = models.CharField(
        max_length=10, 
        default='2.0',
        choices=[('1.0', 'Legacy'), ('2.0', 'New Schema')]
    )
    
    def get_normalized_layout(self):
        """Returns layout in standardized format"""
        if self.layout_version == '1.0':
            return self.migrate_legacy_layout()
        return self.layout_config
```

### Component Template Enhancements

```python
class ComponentTemplate(models.Model):
    # ... existing fields ...
    recommended_render_config = models.JSONField(
        default=dict,
        help_text="Chart type specific recommended render settings"
    )
    
    def get_default_render_config(self):
        """Returns chart-type optimized render config"""
        defaults = {
            'radar': {'aspectRatio': 1.0, 'minHeight': 220},
            'pie': {'aspectRatio': 1.0, 'minHeight': 200},
            'bar': {'aspectRatio': 0.6, 'minHeight': 180},
            'line': {'aspectRatio': 0.5, 'minHeight': 160},
            'area': {'aspectRatio': 0.5, 'minHeight': 160},
            'column': {'aspectRatio': 0.6, 'minHeight': 180},
            'gauge': {'aspectRatio': 1.0, 'minHeight': 180},
            'heatmap': {'aspectRatio': 0.8, 'minHeight': 200},
        }
        return defaults.get(self.chart_type, {'aspectRatio': 0.7, 'minHeight': 200})
```

## Error Handling

### Layout Validation Errors

```python
class LayoutValidationError(Exception):
    def __init__(self, field: str, message: str, suggestions: List[str] = None):
        self.field = field
        self.message = message
        self.suggestions = suggestions or []
```

### Frontend Error Boundaries

```typescript
class LayoutErrorBoundary extends React.Component {
  // Handle layout rendering errors gracefully
  // Provide fallback layouts for broken configurations
  // Log detailed error information for debugging
}
```

## Testing Strategy

### Backend Tests

1. **Layout Schema Validation Tests**
   - Valid/invalid layout configurations
   - Legacy to new schema migration
   - Widget render config validation

2. **API Response Tests**
   - Consistent layout format in API responses
   - Proper error handling for invalid layouts

### Frontend Tests

1. **Component Rendering Tests**
   - Proper application of render configurations
   - Overflow handling verification
   - Responsive behavior testing

2. **Layout Calculation Tests**
   - Aspect ratio enforcement
   - Height constraint validation
   - Breakpoint transition testing

### Integration Tests

1. **End-to-End Layout Tests**
   - Full dashboard rendering pipeline
   - Cross-browser layout consistency
   - Performance under various layout configurations

## Implementation Phases

### Phase 1: Backend Schema Standardization
- Implement layout validation service
- Add migration utilities for legacy layouts
- Enhance API responses with normalized layout data

### Phase 2: Frontend Component Fixes
- Fix DashboardGrid component render config application
- Implement proper CSS overflow handling
- Add component size calculation utilities

### Phase 3: Responsive Layout Improvements
- Enhance breakpoint handling
- Improve mobile layout behavior
- Add smooth transition animations

### Phase 4: Layout Editor UI
- Create visual dashboard layout editor
- Implement drag-and-drop functionality
- Add real-time preview capabilities

### Phase 5: Documentation and Migration
- Update all layout-related documentation
- Create migration guide for existing dashboards
- Implement automated layout validation tools

## Performance Considerations

### Modern CSS Framework Optimization

1. **Tailwind CSS Purging**: Ensure unused CSS classes are purged in production
2. **DaisyUI Theme Optimization**: Use CSS custom properties for dynamic theming
3. **Tremor Component Lazy Loading**: Import Tremor components dynamically to reduce bundle size

### Framework-Specific Optimizations

```typescript
// Tailwind CSS JIT compilation for dynamic classes
const dynamicClasses = useMemo(() => ({
  gridCols: `grid-cols-${breakpoint.columns}`,
  gap: `gap-${gap}`,
  minHeight: minHeight ? `min-h-[${minHeight}px]` : '',
  aspectRatio: getAspectRatioClass(aspectRatio)
}), [breakpoint.columns, gap, minHeight, aspectRatio]);

// Tremor component lazy loading
const TremorChart = lazy(() => import('@tremor/react').then(module => ({ 
  default: module.AreaChart 
})));
```

### Memory Management

1. **Component Cleanup**: Proper cleanup of chart instances on unmount
2. **Event Listener Management**: Remove resize listeners appropriately  
3. **Data Caching**: Implement smart caching for chart data
4. **CSS-in-JS Optimization**: Use Tailwind's utility classes instead of runtime CSS generation

## Security Considerations

### Layout Configuration Security

1. **Input Validation**: Strict validation of layout JSON configurations
2. **XSS Prevention**: Sanitize any user-provided layout titles or descriptions
3. **Access Control**: Ensure proper permissions for layout modification

## Monitoring and Debugging

### Layout Health Monitoring

1. **Error Tracking**: Monitor layout rendering errors in production
2. **Performance Metrics**: Track layout calculation and rendering times
3. **User Experience Metrics**: Monitor dashboard load times and interaction responsiveness

### Debug Tools

1. **Layout Inspector**: Browser extension or dev tool for inspecting layout calculations
2. **Configuration Validator**: Online tool for validating layout JSON
3. **Migration Assistant**: Tool for converting legacy layouts to new schema