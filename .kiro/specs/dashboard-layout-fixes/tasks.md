# Implementation Plan

- [x] 1. Backend Layout Schema Standardization
  - Implement layout configuration validator service in Django
  - Add schema version tracking to Page model
  - Create migration utilities for converting legacy grid_position to new DashboardLayout schema
  - _Requirements: 2.1, 2.2, 3.2, 3.3_

- [x] 1.1 Create Layout Validation Service
  - Write LayoutConfigValidator class in backend/dashboard/services.py
  - Implement validate_dashboard_layout method with comprehensive schema validation
  - Add migrate_legacy_layout method to convert old grid_position format to new schema
  - Write unit tests for validation logic
  - _Requirements: 2.1, 3.3_

- [x] 1.2 Enhance Page Model with Schema Versioning
  - Add layout_version field to Page model with migration
  - Implement get_normalized_layout method that returns standardized layout format
  - Update Page serializer to include normalized layout data in API responses
  - Write model tests for layout normalization
  - _Requirements: 3.2, 3.3_

- [x] 1.3 Update ComponentTemplate with Render Defaults
  - Add recommended_render_config field to ComponentTemplate model
  - Implement get_default_render_config method with chart-type specific defaults
  - Update ComponentTemplate serializer to include render configuration
  - Create migration and update existing templates with recommended configs
  - _Requirements: 2.2, 2.3_

- [x] 2. Frontend CSS Grid Layout Implementation
  - Implement pure CSS Grid solution using Tailwind utilities
  - Create utility functions for generating Tailwind grid classes
  - Add CSS custom properties for dynamic grid values
  - Use CSS aspect-ratio and container queries for responsive sizing
  - _Requirements: 1.1, 1.4, 2.2, 2.3_

- [x] 2.1 Create Tailwind Grid Utility Functions
  - Create frontend/src/utils/gridUtils.ts using Tailwind's grid system
  - Implement getGridColumnClass function using col-span-{n} and col-start-{n} utilities
  - Add getGridRowClass function using row-span-{n} for multi-row components
  - Use grid-cols-{n} for responsive column layouts (grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4)
  - Reference: https://tailwindcss.com/docs/grid-column for proper grid positioning
  - _Requirements: 1.1, 4.1, 4.2, 4.3_

- [x] 2.2 Implement CSS-Based Size Constraints
  - Use Tailwind's aspect-ratio utilities for maintaining widget proportions
  - Implement min-h-[Npx] and max-h-[Npx] classes for height constraints
  - Add CSS container queries for responsive chart sizing
  - Use CSS Grid's minmax() function for flexible sizing within constraints
  - _Requirements: 1.4, 2.2, 2.3_

- [x] 3. Fix DashboardGrid Component Render Configuration
  - Update DashboardGrid component to properly apply widget render configurations
  - Implement proper CSS overflow handling using Tailwind utilities
  - Add component height calculation based on autoHeight/fixedHeight settings
  - Fix responsive grid behavior across different breakpoints
  - _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 2.3, 4.1, 4.2, 4.3_

- [x] 3.1 Update DashboardGrid Component Structure
  - Modify frontend/src/components/DashboardGrid.tsx to use Tailwind's grid system
  - Replace inline styles with Tailwind classes: grid, grid-cols-{n}, gap-{size}
  - Use col-span-{n} and col-start-{n} for widget positioning within grid
  - Implement responsive grid with sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4
  - Add overflow-hidden and min-h-0 classes for proper content containment
  - _Requirements: 1.1, 1.2, 2.1, 2.2_

- [x] 3.2 Implement Widget Render Configuration with Tailwind Classes
  - Update RowGrid component to convert render configs to Tailwind classes
  - Use aspect-w-{n} aspect-h-{n} or aspect-square/aspect-video for aspect ratios
  - Apply h-{size} for fixedHeight, min-h-{size} and max-h-{size} for constraints
  - Use Tailwind's arbitrary value syntax like h-[240px] for exact pixel values
  - Add proper fallback classes for missing render configurations
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 3.3 Fix Component Container Overflow Issues
  - Update grid-item CSS classes to use flex layout with proper overflow handling
  - Add component-chart wrapper with flex-1 and min-h-0 classes
  - Implement content clipping within container boundaries
  - Test overflow behavior with various chart types and data sizes
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 4. Enhance ChartComponent with Size Constraints
  - Update ChartComponent to accept and apply render configuration props
  - Implement proper chart resizing based on container dimensions
  - Add support for aspect ratio maintenance during window resize
  - Integrate with Tremor v3.x components for consistent styling
  - _Requirements: 1.4, 2.2, 2.3, 2.4_

- [x] 4.1 Update ChartComponent Props Interface
  - Modify frontend/src/components/ChartComponent.tsx to accept renderConfig prop
  - Add WidgetRenderConfig TypeScript interface matching backend schema
  - Update component to apply height and aspect ratio constraints
  - Implement proper chart instance cleanup on configuration changes
  - _Requirements: 2.2, 2.3, 2.4_

- [x] 4.2 Integrate Tremor Components for Chart Containers
  - Replace custom card components with Tremor Card components where appropriate
  - Ensure Tremor v3.x compatibility and proper styling integration
  - Add Tremor Title component for consistent chart titles
  - Test chart rendering within Tremor containers
  - _Requirements: 1.1, 1.4_

- [x] 5. Implement Responsive Layout Improvements
  - Fix breakpoint transition behavior in DashboardGrid
  - Add smooth animations for layout changes using Tailwind transitions
  - Implement proper mobile layout stacking
  - Test responsive behavior across different screen sizes
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 5.1 Implement Tailwind Responsive Grid System
  - Replace custom breakpoint logic with Tailwind's responsive prefixes (sm:, md:, lg:, xl:)
  - Use responsive grid classes: grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4
  - Apply responsive column spans: col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3
  - Add transition-all duration-300 ease-in-out for smooth layout changes
  - Test responsive behavior using Tailwind's standard breakpoints (640px, 768px, 1024px, 1280px)
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 5.2 Implement Mobile-First Responsive Layout
  - Use Tailwind's mobile-first approach with base classes for mobile
  - Apply col-span-12 (full width) for mobile, then add responsive overrides
  - Use w-full and max-w-none to prevent horizontal overflow on mobile
  - Add px-4 sm:px-6 lg:px-8 for responsive padding
  - Test with Tailwind's responsive design principles on various screen sizes
  - _Requirements: 4.2, 4.3_

- [x] 6. Update PageContent Component Integration
  - Modify PageContent component to pass render configurations to DashboardGrid
  - Fix layout schema detection logic for proper component selection
  - Add error handling for layout configuration parsing
  - Implement fallback behavior for invalid or missing layouts
  - _Requirements: 2.1, 3.1, 3.4_

- [x] 6.1 Fix Layout Schema Detection
  - Update hasNewSchema function in PageContent.tsx for accurate schema detection
  - Add proper error handling for malformed layout configurations
  - Implement fallback to legacy ComponentGrid when new schema fails
  - Add console warnings for layout configuration issues
  - _Requirements: 3.1, 3.4_

- [x] 6.2 Apply Render Configurations via CSS Classes
  - Update PageContent to convert widget render configs to Tailwind CSS classes
  - Use CSS custom properties to pass dynamic values to grid items
  - Apply aspect-ratio, min-height, and max-height classes directly to components
  - Implement CSS-based fallbacks for missing render configurations
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 7. Add Layout Configuration Validation
  - Implement frontend validation for layout configurations
  - Add error boundaries for layout rendering failures
  - Create user-friendly error messages for layout issues
  - Add development-mode layout debugging tools
  - _Requirements: 3.3, 3.4_

- [x] 7.1 Create Layout Error Boundary Component
  - Implement LayoutErrorBoundary React component for graceful error handling
  - Add fallback UI for layout rendering failures
  - Implement error logging for debugging layout issues
  - Create recovery mechanisms for partial layout failures
  - _Requirements: 3.3, 3.4_

- [x] 7.2 Add Layout Configuration Validation
  - Create validateLayoutConfig utility function for frontend validation
  - Add schema validation for DashboardLayout structure
  - Implement widget reference validation (ensure all widgetRefs exist)
  - Add helpful error messages for common configuration mistakes
  - _Requirements: 3.3, 3.4_

- [x] 8. Update Documentation and Comments
  - Update all layout-related code comments with current schema information
  - Add JSDoc comments to new utility functions and components
  - Update README.md with layout configuration examples
  - Create migration guide for converting legacy layouts
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 8.1 Update Code Documentation
  - Add comprehensive JSDoc comments to ComponentSizeCalculator
  - Update DashboardGrid component comments with new functionality
  - Document all new TypeScript interfaces and types
  - Add inline comments explaining complex layout calculations
  - _Requirements: 3.1, 3.3_

- [x] 8.2 Create Layout Configuration Guide
  - Write comprehensive documentation for DashboardLayout schema
  - Add examples of common layout patterns and configurations
  - Document best practices for widget render configurations
  - Create troubleshooting guide for common layout issues
  - _Requirements: 3.1, 3.2_

- [x] 9. Comprehensive Testing Implementation
  - Write unit tests for all new utility functions and components
  - Add integration tests for layout rendering pipeline
  - Implement visual regression tests for layout consistency
  - Create performance tests for layout calculation efficiency
  - _Requirements: All requirements_

- [x] 9.1 Unit Tests for Layout Utilities
  - Write tests for CSS Grid utility functions (getGridClasses, getItemClasses)
  - Add tests for Tailwind class generation with dynamic values
  - Test layout validation functions with various input scenarios
  - Create tests for CSS custom property application logic
  - _Requirements: 1.4, 2.2, 2.3, 3.3_

- [x] 9.2 Integration Tests for Dashboard Rendering
  - Write tests for complete dashboard layout rendering pipeline
  - Test responsive behavior across different breakpoints
  - Add tests for error handling and fallback scenarios
  - Create tests for layout migration from legacy to new schema
  - _Requirements: 2.1, 3.2, 4.1, 4.2, 4.3_

- [x] 9.3 Visual Regression Tests
  - Implement screenshot-based tests for layout consistency
  - Add tests for component overflow prevention
  - Create tests for responsive layout behavior
  - Test chart rendering within constrained containers
  - _Requirements: 1.1, 1.2, 1.3, 4.4_