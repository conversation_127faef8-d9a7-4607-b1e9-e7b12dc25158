# Authentication System Fixes - Design Document

## Overview

This design addresses critical authentication issues in the Dynamic BI Dashboard by implementing consistent token handling, automatic token refresh, proper session persistence, and reliable auto-login functionality. The solution focuses on fixing the disconnect between backend token response format and frontend expectations, while adding robust session management.

## Architecture

### Current Authentication Flow Issues

1. **Token Field Mismatch**: Backend returns `access_token`/`refresh_token`, frontend expects `access`/`refresh`
2. **No Token Refresh**: Frontend doesn't automatically refresh expired tokens
3. **Session Loss**: Browser refresh clears authentication state
4. **Broken Auto-login**: Remember me functionality doesn't work properly

### Proposed Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as AuthContext
    participant API as Backend API
    participant LS as LocalStorage

    U->>F: Login with credentials
    F->>API: POST /auth/login/
    API->>F: Return tokens (access, refresh, user)
    F->>LS: Store tokens + remember preference
    F->>A: Set user state
    A->>F: Update UI (authenticated)

    Note over F,API: Token Refresh Flow
    F->>API: API request with expired token
    API->>F: 401 Unauthorized
    F->>API: POST /auth/token/refresh/
    API->>F: New access token
    F->>LS: Update stored token
    F->>API: Retry original request
    API->>F: Success response

    Note over F,A: Browser Refresh Flow
    U->>F: Refresh browser
    F->>A: Initialize AuthContext
    A->>LS: Check stored tokens
    A->>API: Validate token (/auth/profile/)
    API->>A: User data or 401
    A->>F: Set authentication state
```

## Components and Interfaces

### 1. Backend Token Response Standardization

**File**: `backend/accounts/serializers.py`

```python
class TokenSerializer(serializers.Serializer):
    """Standardized JWT token response"""
    access = serializers.CharField()  # Changed from access_token
    refresh = serializers.CharField()  # Changed from refresh_token
    user = UserSerializer(read_only=True)

    @classmethod
    def get_token_for_user(cls, user):
        """Generate standardized token response"""
        refresh = RefreshToken.for_user(user)
        return {
            "access": str(refresh.access_token),  # Standardized field name
            "refresh": str(refresh),              # Standardized field name
            "user": UserSerializer(user).data,
        }
```

**File**: `backend/accounts/views.py`

```python
def login_view(request):
    # ... existing validation ...
    
    # Standardized response format
    token_data = TokenSerializer.get_token_for_user(user)
    
    return Response({
        'message': '로그인되었습니다.',
        'access': token_data['access'],    # Consistent naming
        'refresh': token_data['refresh'],  # Consistent naming
        'user': token_data['user']
    }, status=status.HTTP_200_OK)
```

### 2. Frontend Token Refresh Interceptor

**File**: `frontend/src/services/api.ts`

```typescript
class ApiClient {
  private isRefreshing = false;
  private failedQueue: Array<{resolve: Function, reject: Function}> = [];

  constructor() {
    // ... existing setup ...

    // Response interceptor for automatic token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // Queue requests while refreshing
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then(token => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.client(originalRequest);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              const newToken = response.access;
              
              localStorage.setItem('access_token', newToken);
              this.client.defaults.headers.Authorization = `Bearer ${newToken}`;
              
              // Process queued requests
              this.failedQueue.forEach(({ resolve }) => resolve(newToken));
              this.failedQueue = [];
              
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, clear tokens and redirect
            this.clearTokens();
            window.location.hash = '#/login';
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private async refreshToken(refreshToken: string) {
    return this.request<{access: string}>({
      method: 'POST',
      url: '/auth/token/refresh/',
      data: { refresh: refreshToken },
    });
  }

  private clearTokens() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    delete this.client.defaults.headers.Authorization;
  }
}
```

### 3. Enhanced AuthContext with Session Persistence

**File**: `frontend/src/contexts/AuthContext.tsx`

```typescript
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Enhanced initialization with proper token validation
  useEffect(() => {
    const initializeAuth = async () => {
      const accessToken = localStorage.getItem('access_token');
      const refreshToken = localStorage.getItem('refresh_token');
      
      if (accessToken && refreshToken) {
        try {
          // Validate token by fetching user data
          const userData = await apiClient.getCurrentUser();
          setUser(userData);
        } catch (error) {
          console.warn('Token validation failed:', error);
          // Clear invalid tokens
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          
          // Try to refresh if we have a refresh token
          if (refreshToken) {
            try {
              const response = await apiClient.refreshToken(refreshToken);
              localStorage.setItem('access_token', response.access);
              const userData = await apiClient.getCurrentUser();
              setUser(userData);
            } catch (refreshError) {
              console.warn('Token refresh failed:', refreshError);
              localStorage.removeItem('refresh_token');
            }
          }
        }
      }
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string, rememberMe: boolean = false): Promise<void> => {
    try {
      const response = await apiClient.login({ email, password });
      
      // Store tokens with consistent field names
      localStorage.setItem('access_token', response.access);
      localStorage.setItem('refresh_token', response.refresh);
      
      // Handle remember me preference
      if (rememberMe) {
        localStorage.setItem('remember_login', 'true');
        localStorage.setItem('remembered_email', email);
      } else {
        localStorage.removeItem('remember_login');
        localStorage.removeItem('remembered_email');
      }
      
      setUser(response.user);
    } catch (error) {
      throw error;
    }
  };
};
```

### 4. Improved Login Component with Auto-login

**File**: `frontend/src/components/LoginPage.tsx`

```typescript
const LoginPage: React.FC = () => {
  const [email, setEmail] = useState(() => {
    // Pre-fill email if remembered
    return localStorage.getItem('remembered_email') || '';
  });
  const [rememberMe, setRememberMe] = useState(() => {
    return localStorage.getItem('remember_login') === 'true';
  });

  // Auto-login attempt on component mount
  useEffect(() => {
    const attemptAutoLogin = async () => {
      const rememberedLogin = localStorage.getItem('remember_login') === 'true';
      const accessToken = localStorage.getItem('access_token');
      
      if (rememberedLogin && accessToken && !isAuthenticated) {
        try {
          // Validate existing token
          await apiClient.getCurrentUser();
          // If successful, AuthContext will handle the state update
        } catch (error) {
          console.warn('Auto-login failed:', error);
          // Clear invalid tokens but keep email for convenience
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
        }
      }
    };

    if (!isAuthenticated) {
      attemptAutoLogin();
    }
  }, [isAuthenticated]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      await login(email, password, rememberMe);
      navigate('/dashboard');
    } catch (error: any) {
      // Enhanced error handling
      handleLoginError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginError = (error: any) => {
    let errorMessage = '로그인에 실패했습니다.';
    
    if (error.response?.data?.error) {
      errorMessage = error.response.data.error;
    } else if (error.response?.data?.non_field_errors) {
      errorMessage = error.response.data.non_field_errors[0];
    } else if (error.response?.status === 401) {
      errorMessage = '이메일 또는 비밀번호가 올바르지 않습니다.';
    }
    
    setErrors({ general: errorMessage });
  };
};
```

## Data Models

### Token Storage Structure

```typescript
// LocalStorage keys and their purposes
interface TokenStorage {
  access_token: string;      // JWT access token
  refresh_token: string;     // JWT refresh token
  remember_login: 'true' | null;  // Remember me preference
  remembered_email: string;  // Email for convenience
}
```

### Authentication State

```typescript
interface AuthState {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
}

interface User {
  id: string;
  email: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  role: 'regular' | 'org_admin' | 'super_admin';
  organization?: Organization;
  created_at: string;
}
```

## Error Handling

### Token Refresh Error Scenarios

1. **Refresh Token Expired**: Clear all tokens, redirect to login
2. **Network Error During Refresh**: Show network error, allow retry
3. **Invalid Refresh Token**: Clear tokens, redirect to login
4. **Server Error During Refresh**: Show error message, allow manual retry

### Login Error Scenarios

1. **Invalid Credentials**: Show specific error message
2. **Account Disabled**: Show account status message
3. **Rate Limiting**: Show rate limit message with retry time
4. **Network Error**: Show network error with retry option
5. **Server Error**: Show generic server error message

### Session Persistence Error Scenarios

1. **Corrupted Token**: Clear and require fresh login
2. **Token Validation Failure**: Attempt refresh, then clear if failed
3. **User Data Fetch Failure**: Clear tokens and show login

## Testing Strategy

### Unit Tests

1. **Token Serializer Tests**: Verify consistent field naming
2. **AuthContext Tests**: Test initialization, login, logout flows
3. **API Client Tests**: Test token refresh interceptor
4. **Login Component Tests**: Test form validation, error handling

### Integration Tests

1. **End-to-End Login Flow**: Test complete login process
2. **Token Refresh Flow**: Test automatic token refresh
3. **Session Persistence**: Test browser refresh scenarios
4. **Auto-login Flow**: Test remember me functionality

### Test Cases for Specific Issues

1. **Email Login Test**: Verify <EMAIL> can login with vosjf8152
2. **Browser Refresh Test**: Verify user stays logged in after refresh
3. **Token Expiration Test**: Verify automatic refresh works
4. **Remember Me Test**: Verify auto-login works across sessions

## Security Considerations

1. **Token Storage**: Use localStorage for persistence but implement proper cleanup
2. **Token Refresh**: Implement proper queue management to prevent multiple refresh attempts
3. **Error Handling**: Don't expose sensitive information in error messages
4. **Session Management**: Clear all authentication data on logout
5. **CSRF Protection**: Maintain CSRF protection for sensitive operations

## Performance Considerations

1. **Token Refresh Queue**: Prevent multiple simultaneous refresh requests
2. **Lazy Loading**: Only validate tokens when needed
3. **Error Recovery**: Implement exponential backoff for failed requests
4. **Memory Management**: Properly clean up event listeners and timers