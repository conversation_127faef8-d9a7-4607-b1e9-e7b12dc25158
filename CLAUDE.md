# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Rule

- Before command execution related with backend, activate virtual environment first. "cd backend && source venv/bin/activate"
- If run frontend, run "cd frontend && npm install" first. And run as development mode.
- Use "python3" instead of "python"

## Project Overview

PanelistBI is a dynamic BI dashboard application allowing users to create and view business intelligence dashboards through templates and components. It consists of a React.js 18+ TypeScript frontend and Django 5.2 backend with Google Cloud BigQuery integration for data analytics.

## Common Commands

### Docker Development (Recommended)
```bash
# Start all services (frontend, backend, PostgreSQL)
docker-compose up --build

# Start in background
docker-compose up -d --build

# Stop services
docker-compose down

# View logs for specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f db
```

### Backend Development (Django 5.2)
```bash
cd backend

# Virtual environment setup
python -m venv venv
source venv/bin/activate  # macOS/Linux
# venv\Scripts\activate    # Windows

# Install dependencies
pip install -r requirements.txt

# Database operations
python manage.py makemigrations
python manage.py migrate

# Create test data (custom command)
python manage.py create_test_data

# Create predefined dashboard templates with all chart types (NEW!)
python manage.py create_predefined_templates --user-email <EMAIL>

# Run development server
python manage.py runserver

# Create superuser
python manage.py createsuperuser

# Django shell
python manage.py shell
```

### Frontend Development (React 18 + TypeScript)
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Run tests in watch mode
npm test -- --watch
```

## Architecture Overview

### System Architecture
- **Frontend**: React 18 SPA with TypeScript, HashRouter, Tremor Charts
- **Backend**: Django 5.2 with DRF, custom user model, role-based permissions
- **Database**: PostgreSQL (production) / SQLite (development fallback)
- **External Services**: Google Cloud BigQuery, Google OAuth2
- **Authentication**: JWT tokens with refresh mechanism

### Key Applications
- **accounts/**: User authentication, OAuth2, role management (`regular`, `org_admin`, `super_admin`)
- **dashboard/**: Page/component templates, BigQuery service integration
- **dynamic_bi_dashboard/**: Main project settings, custom middleware

### Permission System
- **Regular User**: Read-only access to assigned dashboards
- **Organization Admin**: Manage organization users, access org-level dashboards
- **Super Admin**: Full system access including Django admin

## Core Models and Data Flow

### Database Models
- **User**: Custom user with email, role, organization fields
- **Organization**: User grouping for access control
- **Page**: Dashboard pages with permission levels and layout config
- **ComponentTemplate**: Reusable chart templates with BigQuery SQL
- **PageComponent**: Component instances on specific pages with grid positions
- **PageTemplate/PageTemplateComponent**: Template system for page layouts

### Frontend Data Flow
1. User authenticates → JWT tokens stored in localStorage
2. Dashboard loads accessible pages based on user role
3. Page selection loads component grid with BigQuery data
4. Components fetch data asynchronously from Django API
5. Django executes BigQuery SQL and returns formatted chart data

## Development Guidelines

### Task Management
- Reference `.kiro/specs/dynamic-bi-dashboard/tasks.md` for implementation progress
- When completing tasks, mark them with `[x]` in the tasks.md file
- Follow the sequential task order for proper dependencies

### API Endpoints Structure
```
/api/auth/          - Authentication (login, logout, user info, Google OAuth)
/api/dashboard/     - Page and component management
/api/components/    - Component data fetching with BigQuery integration
/api/admin/         - Admin-only template management
/api/health/        - Health check
/api/schema/        - API documentation
```

### Frontend Component Structure
- **DashboardLayout**: Main layout wrapper with header and navigation
- **PageNavigation**: Horizontal scrollable page tabs
- **ComponentGrid**: CSS Grid-based dynamic layout system
- **ChartComponent**: Universal chart component supporting 15+ chart types
- **ComponentErrorBoundary**: Error handling for individual components

### Chart Types Supported
Area, Bar, Line, Donut, Scatter, Metric, KPI (via Tremor Charts)

### Environment Configuration
- **Backend**: Uses `python-decouple` for environment variables
- **Frontend**: Standard React environment variables (`REACT_APP_*`)
- **Database**: Auto-fallback to SQLite if PostgreSQL not configured
- **BigQuery**: Requires `GOOGLE_CLOUD_PROJECT` and `GOOGLE_APPLICATION_CREDENTIALS`

## Testing Strategy

### Frontend Tests
- **Location**: `frontend/src/components/__tests__/`
- **Framework**: React Testing Library + Jest
- **Command**: `npm test`
- **Coverage**: Component rendering, user interactions, API integration

### Backend Tests
- **Framework**: Django's built-in testing
- **Command**: `python manage.py test`
- **Coverage**: Models, API endpoints, BigQuery integration (mocked)

### Key Test Areas
- Authentication flows (email + Google OAuth)
- Role-based access control
- Component data loading and error handling
- BigQuery service integration
- Page refresh functionality

## Security Features

### Authentication & Authorization
- JWT tokens with 1-hour access token, 7-day refresh token
- OAuth2 integration with Google
- Role-based permissions throughout application
- Rate limiting on auth endpoints (10/hour for login)

### Data Protection
- Environment variables for sensitive data
- CORS configuration for cross-origin requests
- Custom security middleware for headers
- SQL injection protection via Django ORM
- XSS protection through content sanitization

### BigQuery Security
- Service account authentication
- Parameterized queries to prevent injection
- Row-level security implementation
- Audit logging for data access

## Common Development Tasks

### Adding New Chart Components
1. Update `CHART_TYPES` in `dashboard/models.py`
2. Add chart type support in `ChartComponent.tsx`
3. Test with component template creation via Django admin

### Creating New API Endpoints
1. Add URL pattern in respective `urls.py`
2. Implement view in `views.py` with proper permissions
3. Add serializer in `serializers.py` if needed
4. Update frontend API client in `services/api.ts`

### BigQuery Integration
1. SQL templates stored in `ComponentTemplate.bigquery_sql`
2. Service class handles connection and query execution
3. Results formatted for Tremor Chart consumption
4. Error handling for connection and query failures

## Deployment Notes

### Docker Production
- Frontend serves from Nginx container on port 80
- Backend runs with Uvicorn on port 8000
- PostgreSQL on port 5432
- Environment variables injected via docker-compose

### Security Settings
- `DEBUG=False` for production
- `SECURE_SSL_REDIRECT=True` for HTTPS
- `ALLOWED_HOSTS` configured via environment
- JWT secret keys via environment variables