# Dashboard Layout Configuration Guide

## Overview

The Dynamic BI Dashboard system uses a flexible layout schema that supports responsive design, widget render configurations, and multiple layout modes. This guide provides comprehensive documentation for creating and configuring dashboard layouts.

## Table of Contents

1. [Layout Schema Overview](#layout-schema-overview)
2. [Grid Configuration](#grid-configuration)
3. [Widget Render Configurations](#widget-render-configurations)
4. [Common Layout Patterns](#common-layout-patterns)
5. [Responsive Design](#responsive-design)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)
8. [Migration from Legacy Schema](#migration-from-legacy-schema)

## Layout Schema Overview

The dashboard layout system uses a hierarchical structure:

```typescript
interface DashboardLayout {
  grid: GridConfig;        // Grid system configuration
  rows: RowLayout[];       // Row-based layout definition
  widgets: Widget[];       // Widget definitions and configurations
}
```

### Complete Example

```json
{
  "grid": {
    "mode": "strict-grid",
    "gap": 16,
    "breakpoints": {
      "sm": { "minWidth": 640, "columns": 1, "rowUnit": 200 },
      "md": { "minWidth": 768, "columns": 2, "rowUnit": 180 },
      "lg": { "minWidth": 1024, "columns": 3, "rowUnit": 160 },
      "xl": { "minWidth": 1280, "columns": 4, "rowUnit": 140 }
    }
  },
  "rows": [
    {
      "id": "header-row",
      "rowHeight": "auto",
      "items": [
        { "widgetRef": "kpi-1", "col": 1, "span": 1, "h": 1 },
        { "widgetRef": "kpi-2", "col": 2, "span": 1, "h": 1 },
        { "widgetRef": "kpi-3", "col": 3, "span": 1, "h": 1 },
        { "widgetRef": "kpi-4", "col": 4, "span": 1, "h": 1 }
      ]
    },
    {
      "id": "charts-row",
      "rowHeight": 300,
      "items": [
        { "widgetRef": "sales-chart", "col": 1, "span": 2, "h": 1 },
        { "widgetRef": "revenue-chart", "col": 3, "span": 2, "h": 1 }
      ]
    }
  ],
  "widgets": [
    {
      "id": "kpi-1",
      "type": "gauge",
      "title": "Total Sales",
      "dataSource": { "componentId": 101 },
      "render": {
        "aspectRatio": 1.0,
        "minHeight": 120,
        "autoHeight": true,
        "overflow": "hidden"
      }
    }
  ]
}
```

## Grid Configuration

### GridConfig Interface

```typescript
interface GridConfig {
  mode: 'strict-grid' | 'masonry';
  gap: number;
  breakpoints: {
    sm: GridBreakpoint;
    md: GridBreakpoint;
    lg: GridBreakpoint;
    xl: GridBreakpoint;
  };
}
```

### Layout Modes

#### Strict Grid Mode
- All widgets in a row have uniform height
- Provides consistent, aligned layouts
- Best for dashboards with similar content types

```json
{
  "mode": "strict-grid",
  "gap": 16
}
```

#### Masonry Mode
- Widgets can have variable heights
- More flexible but less structured
- Best for mixed content types

```json
{
  "mode": "masonry",
  "gap": 12
}
```

### Responsive Breakpoints

Define how the grid behaves at different screen sizes:

```json
{
  "breakpoints": {
    "sm": { 
      "minWidth": 640, 
      "columns": 1, 
      "rowUnit": 200 
    },
    "md": { 
      "minWidth": 768, 
      "columns": 2, 
      "rowUnit": 180 
    },
    "lg": { 
      "minWidth": 1024, 
      "columns": 3, 
      "rowUnit": 160 
    },
    "xl": { 
      "minWidth": 1280, 
      "columns": 4, 
      "rowUnit": 140 
    }
  }
}
```

**Breakpoint Properties:**
- `minWidth`: Minimum viewport width for this breakpoint (pixels)
- `columns`: Number of grid columns at this breakpoint
- `rowUnit`: Base height unit for rows (pixels)

## Widget Render Configurations

### WidgetRenderConfig Interface

```typescript
interface WidgetRenderConfig {
  autoHeight?: boolean;      // Auto-adjust height to content
  aspectRatio?: number;      // Width-to-height ratio
  minHeight?: number;        // Minimum height in pixels
  maxHeight?: number;        // Maximum height in pixels
  fixedHeight?: number;      // Fixed height in pixels
  overflow?: 'hidden' | 'scroll' | 'visible';
  padding?: number;          // Internal padding in pixels
}
```

### Height Management

#### Auto Height
Automatically adjusts widget height based on content:

```json
{
  "render": {
    "autoHeight": true,
    "minHeight": 200,
    "maxHeight": 500
  }
}
```

#### Fixed Height
Sets a specific height regardless of content:

```json
{
  "render": {
    "fixedHeight": 300
  }
}
```

#### Height Constraints
Provides flexible height with boundaries:

```json
{
  "render": {
    "minHeight": 150,
    "maxHeight": 400
  }
}
```

### Aspect Ratios

Maintain consistent proportions across different screen sizes:

```json
{
  "render": {
    "aspectRatio": 1.0,    // Square (1:1)
    "minHeight": 200
  }
}
```

**Common Aspect Ratios:**
- `1.0`: Square (1:1) - ideal for gauges, pie charts
- `0.6`: Wide rectangle (5:3) - good for bar charts
- `0.5`: Very wide (2:1) - perfect for line charts
- `1.77`: Video aspect (16:9) - for media content

### Overflow Handling

Control how content behaves when it exceeds container size:

```json
{
  "render": {
    "overflow": "hidden"    // Clip content
  }
}
```

**Overflow Options:**
- `hidden`: Clip content at container boundaries
- `scroll`: Add scrollbars when needed
- `visible`: Allow content to extend beyond container

## Common Layout Patterns

### KPI Dashboard

Four key metrics in the top row, charts below:

```json
{
  "rows": [
    {
      "id": "kpis",
      "rowHeight": "auto",
      "items": [
        { "widgetRef": "revenue", "col": 1, "span": 1, "h": 1 },
        { "widgetRef": "orders", "col": 2, "span": 1, "h": 1 },
        { "widgetRef": "customers", "col": 3, "span": 1, "h": 1 },
        { "widgetRef": "conversion", "col": 4, "span": 1, "h": 1 }
      ]
    },
    {
      "id": "charts",
      "rowHeight": 350,
      "items": [
        { "widgetRef": "trend-chart", "col": 1, "span": 3, "h": 1 },
        { "widgetRef": "breakdown", "col": 4, "span": 1, "h": 1 }
      ]
    }
  ]
}
```

### Executive Summary

Large primary chart with supporting metrics:

```json
{
  "rows": [
    {
      "id": "primary",
      "rowHeight": 400,
      "items": [
        { "widgetRef": "main-chart", "col": 1, "span": 3, "h": 1 },
        { "widgetRef": "summary-stats", "col": 4, "span": 1, "h": 1 }
      ]
    },
    {
      "id": "details",
      "rowHeight": "auto",
      "items": [
        { "widgetRef": "detail-1", "col": 1, "span": 2, "h": 1 },
        { "widgetRef": "detail-2", "col": 3, "span": 2, "h": 1 }
      ]
    }
  ]
}
```

### Operational Dashboard

Mixed widget sizes for different data types:

```json
{
  "rows": [
    {
      "id": "status",
      "rowHeight": "auto",
      "items": [
        { "widgetRef": "system-status", "col": 1, "span": 4, "h": 1 }
      ]
    },
    {
      "id": "metrics",
      "rowHeight": 250,
      "items": [
        { "widgetRef": "cpu-usage", "col": 1, "span": 1, "h": 1 },
        { "widgetRef": "memory-usage", "col": 2, "span": 1, "h": 1 },
        { "widgetRef": "network-io", "col": 3, "span": 2, "h": 1 }
      ]
    }
  ]
}
```

## Responsive Design

### Mobile-First Approach

The system uses a mobile-first responsive design:

1. **Base (Mobile)**: Single column layout
2. **Small (640px+)**: Still single column
3. **Medium (768px+)**: 2 columns
4. **Large (1024px+)**: 3 columns
5. **Extra Large (1280px+)**: 4 columns

### Responsive Widget Behavior

Widgets automatically adapt to smaller screens:

- **4-column span** → 2 columns on medium, 1 on mobile
- **3-column span** → 2 columns on medium, 1 on mobile
- **2-column span** → 1 column on mobile
- **1-column span** → Remains 1 column

### Responsive Render Configurations

Adjust widget sizing for different screen sizes:

```json
{
  "render": {
    "aspectRatio": 0.6,
    "minHeight": 180,
    "maxHeight": 400,
    "autoHeight": true
  }
}
```

On smaller screens:
- `aspectRatio` maintains proportions
- `minHeight` ensures readability
- `maxHeight` prevents excessive height
- `autoHeight` adapts to content

## Best Practices

### Grid Design

1. **Use consistent column spans** for visual harmony
2. **Limit row complexity** - max 4-5 widgets per row
3. **Group related widgets** in the same row
4. **Consider mobile layout** when designing

### Widget Sizing

1. **Set appropriate aspect ratios** for chart types:
   - Gauges: `1.0` (square)
   - Bar charts: `0.6` (wide rectangle)
   - Line charts: `0.5` (very wide)
   - Pie charts: `1.0` (square)

2. **Use height constraints** to prevent layout issues:
   - Always set `minHeight` for readability
   - Set `maxHeight` to prevent excessive growth
   - Use `autoHeight` for dynamic content

3. **Handle overflow appropriately**:
   - Charts: `"overflow": "hidden"`
   - Tables: `"overflow": "scroll"`
   - Text content: `"overflow": "visible"`

### Performance

1. **Limit widgets per dashboard** (recommended: 8-12)
2. **Use appropriate row heights** to minimize reflows
3. **Avoid excessive nesting** in widget configurations
4. **Cache widget configurations** when possible

### Accessibility

1. **Provide meaningful widget titles**
2. **Use sufficient color contrast** in charts
3. **Ensure keyboard navigation** works properly
4. **Test with screen readers**

## Troubleshooting

### Common Issues

#### Widgets Not Displaying

**Problem**: Widget shows "Widget Missing" error

**Solutions**:
1. Check `widgetRef` matches widget `id`
2. Verify widget exists in `widgets` array
3. Ensure widget has valid `dataSource`

```json
// Correct widget reference
{
  "widgetRef": "sales-chart",  // Must match widget.id
  "col": 1,
  "span": 2,
  "h": 1
}
```

#### Layout Overflow

**Problem**: Widgets extend beyond container

**Solutions**:
1. Check column calculations: `col + span <= total_columns`
2. Verify responsive breakpoint configurations
3. Add overflow handling to render config

```json
{
  "render": {
    "overflow": "hidden",
    "maxHeight": 400
  }
}
```

#### Inconsistent Heights

**Problem**: Widgets in same row have different heights

**Solutions**:
1. Use `"rowHeight": "auto"` for uniform heights
2. Set consistent `minHeight` in render configs
3. Avoid mixing `autoHeight` and `fixedHeight`

```json
{
  "rowHeight": "auto",  // Ensures uniform row height
  "items": [
    // All widgets will have same height
  ]
}
```

#### Responsive Issues

**Problem**: Layout breaks on mobile devices

**Solutions**:
1. Test with mobile-first breakpoints
2. Ensure column spans work at all breakpoints
3. Use appropriate aspect ratios

```json
{
  "breakpoints": {
    "sm": { "minWidth": 640, "columns": 1 },  // Single column on mobile
    "md": { "minWidth": 768, "columns": 2 }   // Two columns on tablet
  }
}
```

### Validation Errors

#### Invalid Grid Position

```
Error: Widget extends beyond grid: x(3) + w(2) > 4
```

**Solution**: Ensure `col + span <= breakpoint.columns`

#### Invalid Aspect Ratio

```
Error: Invalid aspectRatio: -0.5. Must be greater than 0
```

**Solution**: Use positive aspect ratio values

#### Height Constraint Conflict

```
Warning: minHeight (300) cannot be greater than maxHeight (200)
```

**Solution**: Ensure `minHeight <= maxHeight`

### Debug Tools

#### Layout Inspector

Add debug classes to visualize grid:

```json
{
  "grid": {
    "debug": true  // Adds visual grid lines
  }
}
```

#### Console Logging

Enable detailed logging:

```javascript
// In browser console
localStorage.setItem('dashboard-debug', 'true');
```

## Migration from Legacy Schema

### Legacy vs New Schema

**Legacy Schema** (grid_position):
```json
{
  "grid_position": {
    "x": 0, "y": 0, "w": 2, "h": 1
  }
}
```

**New Schema** (DashboardLayout):
```json
{
  "rows": [{
    "items": [{
      "widgetRef": "widget-1",
      "col": 1, "span": 2, "h": 1
    }]
  }]
}
```

### Migration Steps

1. **Identify Legacy Layouts**
   - Look for `grid_position` properties
   - Check `layout_version` field in database

2. **Convert Grid Positions**
   ```javascript
   // Legacy: x=0, y=0, w=2, h=1
   // New: col=1, span=2, h=1 (col is 1-based)
   const newCol = legacyX + 1;
   const newSpan = legacyW;
   const newH = legacyH;
   ```

3. **Group by Rows**
   - Group widgets with same `y` value
   - Create row objects with appropriate `rowHeight`

4. **Add Render Configurations**
   - Add appropriate `render` configs for each widget
   - Use chart-type specific defaults

5. **Test Responsive Behavior**
   - Verify layout works at all breakpoints
   - Adjust column spans if needed

### Migration Script Example

```javascript
function migrateLegacyLayout(legacyLayout) {
  const widgets = legacyLayout.widgets || [];
  const rowGroups = {};
  
  // Group widgets by row (y position)
  widgets.forEach(widget => {
    const y = widget.grid_position.y;
    if (!rowGroups[y]) {
      rowGroups[y] = [];
    }
    rowGroups[y].push({
      widgetRef: widget.id,
      col: widget.grid_position.x + 1,  // Convert to 1-based
      span: widget.grid_position.w,
      h: widget.grid_position.h
    });
  });
  
  // Create new layout structure
  const newLayout = {
    grid: {
      mode: 'strict-grid',
      gap: 16,
      breakpoints: {
        sm: { minWidth: 640, columns: 1, rowUnit: 200 },
        md: { minWidth: 768, columns: 2, rowUnit: 180 },
        lg: { minWidth: 1024, columns: 3, rowUnit: 160 },
        xl: { minWidth: 1280, columns: 4, rowUnit: 140 }
      }
    },
    rows: Object.keys(rowGroups).map(y => ({
      id: `row-${y}`,
      rowHeight: 'auto',
      items: rowGroups[y]
    })),
    widgets: widgets.map(widget => ({
      id: widget.id,
      type: widget.type,
      title: widget.title,
      dataSource: widget.dataSource,
      render: getDefaultRenderConfig(widget.type)
    }))
  };
  
  return newLayout;
}

function getDefaultRenderConfig(chartType) {
  const defaults = {
    'gauge': { aspectRatio: 1.0, minHeight: 180 },
    'pie': { aspectRatio: 1.0, minHeight: 200 },
    'bar': { aspectRatio: 0.6, minHeight: 180 },
    'line': { aspectRatio: 0.5, minHeight: 160 }
  };
  
  return defaults[chartType] || { aspectRatio: 0.7, minHeight: 200 };
}
```

## Conclusion

The dashboard layout system provides powerful flexibility for creating responsive, professional dashboards. By following the patterns and best practices outlined in this guide, you can create layouts that work well across all device sizes and provide excellent user experiences.

For additional support or questions, refer to the troubleshooting section or consult the development team.