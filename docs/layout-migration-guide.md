# Layout Migration Guide

## Overview

This guide helps you migrate from the legacy `grid_position` schema to the new `DashboardLayout` schema. The new schema provides better responsive design support, widget render configurations, and improved layout flexibility.

## Schema Comparison

### Legacy Schema (v1.0)

The legacy schema used a simple grid position approach:

```json
{
  "layout_version": "1.0",
  "components": [
    {
      "id": "widget-1",
      "type": "bar",
      "title": "Sales Chart",
      "grid_position": {
        "x": 0,    // 0-based column position
        "y": 0,    // 0-based row position
        "w": 2,    // Width in columns
        "h": 1     // Height in rows
      },
      "dataSource": {
        "componentId": 101
      }
    }
  ]
}
```

### New Schema (v2.0)

The new schema uses a row-based layout with responsive configuration:

```json
{
  "layout_version": "2.0",
  "grid": {
    "mode": "strict-grid",
    "gap": 16,
    "breakpoints": {
      "sm": { "minWidth": 640, "columns": 1, "rowUnit": 200 },
      "md": { "minWidth": 768, "columns": 2, "rowUnit": 180 },
      "lg": { "minWidth": 1024, "columns": 3, "rowUnit": 160 },
      "xl": { "minWidth": 1280, "columns": 4, "rowUnit": 140 }
    }
  },
  "rows": [
    {
      "id": "row-0",
      "rowHeight": "auto",
      "items": [
        {
          "widgetRef": "widget-1",
          "col": 1,    // 1-based column position
          "span": 2,   // Column span (same as legacy width)
          "h": 1       // Height in row units
        }
      ]
    }
  ],
  "widgets": [
    {
      "id": "widget-1",
      "type": "bar",
      "title": "Sales Chart",
      "dataSource": {
        "componentId": 101
      },
      "render": {
        "aspectRatio": 0.6,
        "minHeight": 180,
        "autoHeight": true,
        "overflow": "hidden"
      }
    }
  ]
}
```

## Key Differences

| Aspect | Legacy Schema | New Schema |
|--------|---------------|------------|
| **Column Indexing** | 0-based (x: 0, 1, 2...) | 1-based (col: 1, 2, 3...) |
| **Layout Structure** | Flat component list | Row-based grouping |
| **Responsive Design** | Fixed grid | Responsive breakpoints |
| **Widget Configuration** | Basic properties | Rich render configurations |
| **Height Management** | Simple row heights | Auto/fixed/constrained heights |
| **Overflow Handling** | Not specified | Configurable overflow behavior |

## Migration Process

### Step 1: Identify Legacy Layouts

Check your database for pages using the legacy schema:

```sql
-- Find pages with legacy layout
SELECT id, title, layout_version, layout_config 
FROM dashboard_page 
WHERE layout_version = '1.0' OR layout_version IS NULL;
```

### Step 2: Backup Existing Data

Always backup your data before migration:

```sql
-- Create backup table
CREATE TABLE dashboard_page_backup AS 
SELECT * FROM dashboard_page WHERE layout_version = '1.0';
```

### Step 3: Run Migration Script

Use the provided migration utility:

```python
# backend/dashboard/management/commands/migrate_layouts.py
from django.core.management.base import BaseCommand
from dashboard.models import Page
from dashboard.services import LayoutConfigValidator

class Command(BaseCommand):
    help = 'Migrate legacy layouts to new schema'
    
    def handle(self, *args, **options):
        legacy_pages = Page.objects.filter(layout_version='1.0')
        
        for page in legacy_pages:
            try:
                # Convert legacy layout
                new_layout = self.convert_legacy_layout(page.layout_config)
                
                # Validate new layout
                validator = LayoutConfigValidator()
                validation_result = validator.validate_dashboard_layout(new_layout)
                
                if validation_result.is_valid:
                    # Update page
                    page.layout_config = new_layout
                    page.layout_version = '2.0'
                    page.save()
                    
                    self.stdout.write(
                        self.style.SUCCESS(f'Migrated page: {page.title}')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'Validation failed for page: {page.title}')
                    )
                    for error in validation_result.errors:
                        self.stdout.write(f'  - {error}')
                        
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Migration failed for page {page.title}: {str(e)}')
                )
    
    def convert_legacy_layout(self, legacy_config):
        """Convert legacy grid_position layout to new schema"""
        components = legacy_config.get('components', [])
        
        # Group components by row (y position)
        row_groups = {}
        for component in components:
            grid_pos = component.get('grid_position', {})
            y = grid_pos.get('y', 0)
            
            if y not in row_groups:
                row_groups[y] = []
            
            row_groups[y].append({
                'widgetRef': component['id'],
                'col': grid_pos.get('x', 0) + 1,  # Convert to 1-based
                'span': grid_pos.get('w', 1),
                'h': grid_pos.get('h', 1)
            })
        
        # Create new layout structure
        new_layout = {
            'grid': {
                'mode': 'strict-grid',
                'gap': 16,
                'breakpoints': {
                    'sm': {'minWidth': 640, 'columns': 1, 'rowUnit': 200},
                    'md': {'minWidth': 768, 'columns': 2, 'rowUnit': 180},
                    'lg': {'minWidth': 1024, 'columns': 3, 'rowUnit': 160},
                    'xl': {'minWidth': 1280, 'columns': 4, 'rowUnit': 140}
                }
            },
            'rows': [
                {
                    'id': f'row-{y}',
                    'rowHeight': 'auto',
                    'items': items
                }
                for y, items in sorted(row_groups.items())
            ],
            'widgets': [
                {
                    'id': comp['id'],
                    'type': comp['type'],
                    'title': comp['title'],
                    'dataSource': comp.get('dataSource', {}),
                    'render': self.get_default_render_config(comp['type'])
                }
                for comp in components
            ]
        }
        
        return new_layout
    
    def get_default_render_config(self, chart_type):
        """Get default render configuration for chart type"""
        defaults = {
            'gauge': {'aspectRatio': 1.0, 'minHeight': 180, 'autoHeight': True},
            'pie': {'aspectRatio': 1.0, 'minHeight': 200, 'autoHeight': True},
            'donut': {'aspectRatio': 1.0, 'minHeight': 200, 'autoHeight': True},
            'bar': {'aspectRatio': 0.6, 'minHeight': 180, 'autoHeight': True},
            'column': {'aspectRatio': 0.6, 'minHeight': 180, 'autoHeight': True},
            'line': {'aspectRatio': 0.5, 'minHeight': 160, 'autoHeight': True},
            'area': {'aspectRatio': 0.5, 'minHeight': 160, 'autoHeight': True},
            'radar': {'aspectRatio': 1.0, 'minHeight': 220, 'autoHeight': True},
            'heatmap': {'aspectRatio': 0.8, 'minHeight': 200, 'overflow': 'scroll'},
            'scatter': {'aspectRatio': 0.75, 'minHeight': 200, 'autoHeight': True}
        }
        
        return defaults.get(chart_type, {
            'aspectRatio': 0.7,
            'minHeight': 200,
            'autoHeight': True,
            'overflow': 'hidden'
        })
```

### Step 4: Run the Migration

Execute the migration command:

```bash
cd backend
python manage.py migrate_layouts
```

### Step 5: Verify Migration Results

Check that the migration was successful:

```sql
-- Count migrated pages
SELECT layout_version, COUNT(*) 
FROM dashboard_page 
GROUP BY layout_version;

-- Check specific migrated page
SELECT id, title, layout_version, layout_config 
FROM dashboard_page 
WHERE id = 'your-page-id';
```

## Manual Migration Steps

If you prefer to migrate layouts manually, follow these steps:

### 1. Convert Grid Positions

Transform the coordinate system:

```javascript
// Legacy: 0-based coordinates
const legacy = { x: 0, y: 0, w: 2, h: 1 };

// New: 1-based columns, row-based grouping
const newItem = {
  widgetRef: 'widget-id',
  col: legacy.x + 1,  // Convert to 1-based
  span: legacy.w,     // Width becomes span
  h: legacy.h         // Height remains the same
};
```

### 2. Group by Rows

Organize widgets by their row position:

```javascript
function groupByRows(components) {
  const rows = {};
  
  components.forEach(comp => {
    const y = comp.grid_position.y;
    if (!rows[y]) {
      rows[y] = {
        id: `row-${y}`,
        rowHeight: 'auto',
        items: []
      };
    }
    
    rows[y].items.push({
      widgetRef: comp.id,
      col: comp.grid_position.x + 1,
      span: comp.grid_position.w,
      h: comp.grid_position.h
    });
  });
  
  return Object.values(rows);
}
```

### 3. Add Render Configurations

Enhance widgets with render configurations:

```javascript
function addRenderConfigs(components) {
  return components.map(comp => ({
    id: comp.id,
    type: comp.type,
    title: comp.title,
    dataSource: comp.dataSource,
    render: getDefaultRenderConfig(comp.type)
  }));
}

function getDefaultRenderConfig(chartType) {
  const configs = {
    'bar': { aspectRatio: 0.6, minHeight: 180 },
    'line': { aspectRatio: 0.5, minHeight: 160 },
    'pie': { aspectRatio: 1.0, minHeight: 200 },
    'gauge': { aspectRatio: 1.0, minHeight: 180 }
  };
  
  return {
    ...configs[chartType] || { aspectRatio: 0.7, minHeight: 200 },
    autoHeight: true,
    overflow: 'hidden'
  };
}
```

### 4. Create Complete Layout

Combine all elements:

```javascript
function createNewLayout(legacyConfig) {
  const components = legacyConfig.components || [];
  
  return {
    grid: {
      mode: 'strict-grid',
      gap: 16,
      breakpoints: {
        sm: { minWidth: 640, columns: 1, rowUnit: 200 },
        md: { minWidth: 768, columns: 2, rowUnit: 180 },
        lg: { minWidth: 1024, columns: 3, rowUnit: 160 },
        xl: { minWidth: 1280, columns: 4, rowUnit: 140 }
      }
    },
    rows: groupByRows(components),
    widgets: addRenderConfigs(components)
  };
}
```

## Testing Migration Results

### 1. Visual Verification

After migration, verify that dashboards render correctly:

1. **Load each migrated dashboard**
2. **Check widget positioning** - ensure widgets appear in correct locations
3. **Test responsive behavior** - resize browser window to test breakpoints
4. **Verify widget sizing** - ensure widgets have appropriate heights and aspect ratios

### 2. Functional Testing

Test dashboard functionality:

1. **Data loading** - ensure all widgets load data correctly
2. **Interactions** - test any interactive features
3. **Error handling** - verify error boundaries work properly
4. **Performance** - check that layouts render efficiently

### 3. Cross-browser Testing

Test on different browsers and devices:

1. **Desktop browsers** - Chrome, Firefox, Safari, Edge
2. **Mobile devices** - iOS Safari, Android Chrome
3. **Tablet devices** - iPad, Android tablets

## Rollback Procedure

If migration issues occur, you can rollback:

### 1. Restore from Backup

```sql
-- Restore specific page
UPDATE dashboard_page 
SET layout_config = backup.layout_config, 
    layout_version = backup.layout_version
FROM dashboard_page_backup backup
WHERE dashboard_page.id = backup.id 
  AND dashboard_page.id = 'problematic-page-id';

-- Restore all pages
TRUNCATE dashboard_page;
INSERT INTO dashboard_page SELECT * FROM dashboard_page_backup;
```

### 2. Fix Issues and Re-migrate

1. **Identify the problem** in the migration logic
2. **Fix the migration script**
3. **Test on a single page** first
4. **Re-run migration** for all pages

## Common Migration Issues

### Issue 1: Widget Positioning

**Problem**: Widgets appear in wrong positions after migration

**Cause**: Incorrect coordinate conversion (0-based to 1-based)

**Solution**:
```javascript
// Correct conversion
const newCol = legacyX + 1;  // Add 1 to convert from 0-based to 1-based
```

### Issue 2: Overlapping Widgets

**Problem**: Widgets overlap or extend beyond grid boundaries

**Cause**: Column span exceeds available columns at smaller breakpoints

**Solution**:
```javascript
// Validate column positioning
function validatePosition(col, span, maxColumns) {
  if (col + span - 1 > maxColumns) {
    // Adjust span to fit within grid
    return Math.max(1, maxColumns - col + 1);
  }
  return span;
}
```

### Issue 3: Inconsistent Heights

**Problem**: Widgets in the same row have different heights

**Cause**: Missing or incorrect render configurations

**Solution**:
```javascript
// Ensure consistent render configs
const renderConfig = {
  autoHeight: true,
  minHeight: 180,
  aspectRatio: getAspectRatioForType(chartType),
  overflow: 'hidden'
};
```

### Issue 4: Responsive Layout Issues

**Problem**: Layout breaks on mobile devices

**Cause**: Inappropriate breakpoint configuration or column spans

**Solution**:
```javascript
// Use mobile-friendly breakpoints
const breakpoints = {
  sm: { minWidth: 640, columns: 1 },    // Single column on mobile
  md: { minWidth: 768, columns: 2 },    // Two columns on tablet
  lg: { minWidth: 1024, columns: 3 },   // Three columns on desktop
  xl: { minWidth: 1280, columns: 4 }    // Four columns on large screens
};
```

## Best Practices for Migration

### 1. Plan the Migration

- **Inventory existing layouts** - understand what you're migrating
- **Test migration script** on development environment first
- **Schedule migration** during low-usage periods
- **Prepare rollback plan** in case of issues

### 2. Validate Results

- **Use automated validation** - run layout validation after migration
- **Perform visual checks** - manually review critical dashboards
- **Test user workflows** - ensure user experience is maintained
- **Monitor performance** - check that new layouts perform well

### 3. Communicate Changes

- **Notify users** about the migration and any visual changes
- **Provide documentation** on new layout features
- **Offer training** if new layout capabilities are available
- **Collect feedback** and address any issues promptly

### 4. Optimize Post-Migration

- **Review render configurations** - optimize for better performance
- **Adjust responsive breakpoints** - fine-tune for your user base
- **Update documentation** - reflect new layout capabilities
- **Plan future enhancements** - leverage new schema features

## Conclusion

Migrating from the legacy grid_position schema to the new DashboardLayout schema provides significant benefits in terms of responsive design, widget customization, and layout flexibility. By following this guide and using the provided migration tools, you can successfully upgrade your dashboard layouts while maintaining functionality and improving user experience.

For additional support during migration, consult the [Layout Configuration Guide](layout-configuration-guide.md) or contact the development team.