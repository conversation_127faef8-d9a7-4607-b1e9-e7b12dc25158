# Page Access Management

This document describes the organization-page access management functionality implemented in the Dynamic BI Dashboard.

## Overview

The page access management feature allows super administrators to control which organizations can access specific dashboard pages. This provides fine-grained access control beyond the basic role-based permissions.

## Features

### 1. Organization-Page Permission Management
- **Page-by-Page View**: Select a page and manage which organizations can access it
- **Organization-by-Organization View**: Select an organization and manage which pages it can access
- **Grant Access**: Allow an organization to access a specific page
- **Revoke Access**: Remove an organization's access to a specific page

### 2. User Interface
- **Admin Panel Integration**: Accessible through the "페이지 접근 권한" tab in the Permission Management section
- **Dual View Modes**: Toggle between page-centric and organization-centric views
- **Real-time Updates**: Changes are reflected immediately in the interface
- **Access History**: Shows when access was granted and by whom

### 3. Access Control Logic
The system uses a combined approach for page access:
- **Role-based Access**: Users can access pages based on their role (regular, org_admin, super_admin)
- **Organization-based Access**: Users can access pages specifically granted to their organization
- **Combined Logic**: A user can access a page if either their role allows it OR their organization has been granted access

## API Endpoints

### Organization Management
- `GET /api/auth/organizations/` - List all organizations
- `GET /api/auth/organizations/{id}/` - Get organization details

### Page Management  
- `GET /api/pages/` - List all pages
- `GET /api/pages/{id}/` - Get page details

### Access Management
- `GET /api/admin/pages/{page_id}/organizations/` - Get organizations with access to a page
- `GET /api/admin/organizations/{org_id}/pages/` - Get pages accessible to an organization
- `POST /api/admin/pages/{page_id}/organizations/{org_id}/access/` - Grant page access to organization
- `DELETE /api/admin/pages/{page_id}/organizations/{org_id}/access/revoke/` - Revoke page access from organization

## Usage

### For Super Administrators

1. **Navigate to Admin Panel**
   - Go to `/admin` in the application
   - Click on "권한 관리" (Permission Management)

2. **Access Page Permission Management**
   - Click on the "페이지 접근 권한" (Page Access Permissions) tab
   - Choose between "페이지별 보기" (Page-by-Page View) or "조직별 보기" (Organization-by-Organization View)

3. **Grant Access (Page-by-Page View)**
   - Select a page from the left panel
   - View organizations and their current access status
   - Click "권한 부여" (Grant Access) for organizations that should have access
   - Click "권한 해제" (Revoke Access) to remove access

4. **Grant Access (Organization-by-Organization View)**
   - Select an organization from the left panel
   - View pages and their current access status for that organization
   - Click "권한 부여" (Grant Access) for pages the organization should access
   - Click "권한 해제" (Revoke Access) to remove access

### For End Users

The page access management is transparent to end users. They will simply see the pages they have access to based on:
- Their role permissions
- Their organization's granted page access

## Technical Implementation

### Frontend Components
- `PageAccessTab`: Main component for the page access management interface
- `PageByPageView`: Component for managing access from a page perspective
- `PageByOrganizationView`: Component for managing access from an organization perspective

### Backend Models
- `PageOrganizationAccess`: Junction model linking pages and organizations
- `Page.accessible_organizations`: Many-to-many relationship to organizations

### Database Schema
```sql
-- PageOrganizationAccess model
CREATE TABLE dashboard_pageorganizationaccess (
    id SERIAL PRIMARY KEY,
    organization_id INTEGER REFERENCES accounts_organization(id),
    page_id INTEGER REFERENCES dashboard_page(id),
    granted_at TIMESTAMP DEFAULT NOW(),
    granted_by_id INTEGER REFERENCES accounts_user(id)
);
```

## Security Considerations

- Only super administrators can manage organization-page access
- All access changes are logged with timestamp and granting user
- API endpoints require proper authentication and authorization
- Changes are validated on both frontend and backend

## Future Enhancements

- **Bulk Operations**: Allow granting/revoking access to multiple pages/organizations at once
- **Access Templates**: Create reusable access templates for common organization types
- **Audit Trail**: Enhanced logging and reporting of access changes
- **Time-based Access**: Support for temporary access grants with expiration dates