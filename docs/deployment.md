# Cloud Run / GKE Deployment

## GitHub Actions workflow

The `Deploy` workflow in `.github/workflows/deploy.yml` uses a `workflow_dispatch` trigger so deployments run only when manually started from the GitHub Actions tab.

## Container images

- **Backend**: built from `backend/Dockerfile` and published as `$REGION-docker.pkg.dev/$PROJECT_ID/panelistbi/backend`.
- **Frontend**: built from `frontend/Dockerfile` and published as `$REGION-docker.pkg.dev/$PROJECT_ID/panelistbi/frontend`.

## Cloud Run configuration

- **Service account**: an account with `Cloud Run Admin`, `Artifact Registry Writer`, and `Service Account User` roles. Grant additional access such as `Cloud SQL Client` if needed.
- **Environment variables**:
  - `DB_NAME`, `DB_USER`, `DB_PASSWORD`, `DB_HOST`, `DB_PORT`
  - `DJANGO_SECRET_KEY`
  - `ALLOWED_HOSTS`
- **Networking**: attach a VPC connector when accessing private resources like Cloud SQL.
- **Deploy commands**:

```bash
gcloud run deploy backend --image $REGION-docker.pkg.dev/$PROJECT_ID/panelistbi/backend:TAG \
  --region $REGION --platform managed --service-account SERVICE_ACCOUNT \
  --set-env-vars DB_HOST=...,DB_NAME=...,DB_USER=...,DB_PASSWORD=...,DB_PORT=5432 \
  --vpc-connector CONNECTOR --allow-unauthenticated

gcloud run deploy frontend --image $REGION-docker.pkg.dev/$PROJECT_ID/panelistbi/frontend:TAG \
  --region $REGION --platform managed --service-account SERVICE_ACCOUNT \
  --allow-unauthenticated
```

## GKE configuration

- **Service account**: an account with `Kubernetes Engine Developer` and `Artifact Registry Reader` roles.
- **Environment variables**: same as above, stored in Kubernetes `ConfigMap` and `Secret` objects.
- **Networking**: ensure the cluster's network can reach the database; configure `NetworkPolicy` as needed.
- **Deploy commands**:

```bash
kubectl set image deployment/backend backend=$REGION-docker.pkg.dev/$PROJECT_ID/panelistbi/backend:TAG
kubectl set image deployment/frontend frontend=$REGION-docker.pkg.dev/$PROJECT_ID/panelistbi/frontend:TAG
```
