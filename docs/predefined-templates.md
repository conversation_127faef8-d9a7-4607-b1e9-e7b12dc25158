# Predefined Templates Usage Guide

This document explains how to use the predefined page and component template creation functionality in PanelistBI.

## Overview

The `create_predefined_templates` Django management command allows you to quickly set up example dashboards and chart components with realistic sample data and BigQuery SQL queries. This is perfect for:

- Setting up demo environments
- Providing examples for new users
- Testing all chart types
- Creating template dashboards for common use cases

## Usage

### Basic Command

```bash
# Create all predefined templates (components, pages, and showcase)
python manage.py create_predefined_templates --user-email <EMAIL>
```

### Command Options

| Option | Description |
|--------|-------------|
| `--components-only` | Create only component templates |
| `--pages-only` | Create only page templates |
| `--showcase` | Create showcase page with all chart types |
| `--user-email EMAIL` | Email of user to assign as creator (default: <EMAIL>) |

### Examples

```bash
# Create only component templates for all 14 chart types
python manage.py create_predefined_templates --components-only

# Create only the predefined page templates
python manage.py create_predefined_templates --pages-only

# Create showcase page with all chart types
python manage.py create_predefined_templates --showcase

# Specify a different user as creator
python manage.py create_predefined_templates --user-email <EMAIL>
```

## What Gets Created

### Component Templates (14 total)

Each chart type gets a realistic component template with:
- **Sample BigQuery SQL queries** for realistic business scenarios
- **API endpoint configurations** 
- **Chart-appropriate data structures**

| Chart Type | Template Name | Use Case |
|------------|---------------|----------|
| Bar Chart | 월별 매출 현황 | Monthly revenue tracking |
| Column Chart | 분기별 매출 추이 | Quarterly revenue trends |
| Line Chart | 일별 방문자 추이 | Daily visitor trends |
| Area Chart | 누적 사용자 증가 | Cumulative user growth |
| Pie Chart | 제품 카테고리별 매출 비율 | Revenue by product category |
| Bubble Chart | 매출-수익률 상관관계 | Revenue vs margin correlation |
| Scatter Chart | 고객 연령-구매액 분포 | Customer age vs purchase amount |
| Heatmap | 월별 판매량 히트맵 | Monthly sales volume heatmap |
| Treemap | 제품 계층별 매출 | Product hierarchy revenue |
| Radar Chart | 고객 만족도 레이더 | Customer satisfaction metrics |
| Box Plot | 배송시간 분포 | Delivery time distribution |
| Radial Bar | 월별 목표 달성률 | Monthly target achievement |
| Gauge | 실시간 매출 게이지 | Real-time sales gauge |
| Solid Gauge | 종합 성과 대시보드 | Comprehensive performance |

### Page Templates (3 total)

Pre-configured dashboard layouts for common business scenarios:

#### 1. 매출 분석 대시보드 (Sales Analytics Dashboard)
- **Permission Level**: Regular User
- **Components**: Monthly sales, quarterly trends, category breakdown, target achievement, real-time gauge
- **Layout**: Optimized for sales team daily monitoring

#### 2. 고객 분석 대시보드 (Customer Analytics Dashboard) 
- **Permission Level**: Regular User
- **Components**: Visitor trends, user growth, age/purchase correlation, satisfaction metrics
- **Layout**: Focused on customer behavior analysis

#### 3. 운영 모니터링 대시보드 (Operations Monitoring Dashboard)
- **Permission Level**: Organization Admin
- **Components**: Performance gauges, delivery metrics, sales heatmap, product hierarchy
- **Layout**: Executive-level operational overview

### Showcase Page

A comprehensive demonstration page containing **all 14 chart types** arranged in a 4-column grid layout. This page is perfect for:
- Testing all chart functionality
- Demonstrating system capabilities
- Training and onboarding
- Visual reference for chart types

## Sample BigQuery SQL Queries

All component templates include realistic BigQuery SQL queries that demonstrate:

- **Time-series data aggregation** (daily, monthly, quarterly)
- **Multi-table joins** (orders, products, customers, etc.)
- **Statistical calculations** (percentiles, averages, correlations)
- **Hierarchical data structures** (categories, sub-categories)
- **Performance metrics** (targets vs actuals, achievement rates)

Example query structure:
```sql
SELECT 
    FORMAT_DATE('%Y-%m', order_date) as month,
    SUM(total_amount) as revenue
FROM `project.dataset.orders`
WHERE order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
GROUP BY month
ORDER BY month
```

## Integration with Frontend

The created templates are immediately available in:

1. **Django Admin Interface** - For template management
2. **Frontend Dashboard** - Via API endpoints
3. **Page Navigation** - Accessible based on user permissions

## Permissions

- **Regular Users**: Can access sales and customer analytics dashboards
- **Organization Admins**: Can access all dashboards including operations monitoring
- **Super Admins**: Full access to template management

## Customization

After running the command, you can:

1. **Modify SQL queries** in Django Admin for your specific data schema
2. **Adjust grid layouts** by editing position configurations
3. **Add/remove components** from page templates
4. **Create new pages** using existing component templates
5. **Clone templates** for variations

## Best Practices

1. **Run with test user**: Always specify a valid `--user-email` that exists in your system
2. **Use in development**: Best used in development/staging environments first
3. **Backup before running**: The command is idempotent but backup your data
4. **Customize gradually**: Start with predefined templates then customize incrementally
5. **Test permissions**: Verify that users can access appropriate dashboards

## Troubleshooting

### Common Issues

**User not found error**:
```bash
# Make sure the user exists first
python manage.py create_test_data  # Creates default users
python manage.py create_predefined_templates
```

**Component template not found warning**:
- Run `--components-only` first if you see this warning
- Check that component names match exactly

**Permission denied in frontend**:
- Verify user roles and page permission levels
- Check that users are assigned to appropriate organizations

## Database Tables Affected

The command creates data in these tables:
- `component_templates` - Chart component definitions
- `page_templates` - Page layout templates  
- `page_template_components` - Template component relationships
- `pages` - Actual dashboard pages
- `page_components` - Page component instances

## Example Workflow

```bash
# 1. Set up initial users and organizations
python manage.py create_test_data

# 2. Create all predefined templates
python manage.py create_predefined_templates --user-email <EMAIL>

# 3. Log in to frontend and explore:
#    - 📊 전체 차트 타입 쇼케이스 (showcase page)
#    - 매출 분석 대시보드
#    - 고객 분석 대시보드  
#    - 운영 모니터링 대시보드

# 4. Customize in Django Admin as needed
```

This comprehensive template system provides a solid foundation for building business intelligence dashboards with realistic examples and best practices built-in.