# Google OAuth2 설정 가이드

## 개요

동적 BI 대시보드 앱에서 Google OAuth2 인증을 구현하기 위해 필요한 설정 단계들을 정리한 문서입니다.

## 1. Google Cloud Console 설정

### 1.1 프로젝트 생성 및 설정

1. **Google Cloud Console 접속**
   - https://console.cloud.google.com/ 접속
   - 새 프로젝트 생성 또는 기존 프로젝트 선택

2. **OAuth 동의 화면 설정**
   - 좌측 메뉴 > APIs & Services > OAuth consent screen
   - User Type: External 선택 (내부 조직용이면 Internal)
   - 앱 정보 입력:
     - App name: "BI Dashboard"
     - User support email: 개발자 이메일
     - Developer contact information: 개발자 이메일
   - Scopes 설정:
     - `../auth/userinfo.email`
     - `../auth/userinfo.profile`
     - `openid`

3. **OAuth 2.0 클라이언트 ID 생성**
   - 좌측 메뉴 > APIs & Services > Credentials
   - "Create Credentials" > "OAuth 2.0 Client IDs"
   - Application type: Web application
   - Name: "BI Dashboard Web Client"
   - Authorized JavaScript origins:
     - `http://localhost:3000` (개발용)
     - `https://yourdomain.com` (프로덕션용)
   - Authorized redirect URIs:
     - `http://localhost:8000/api/auth/google/callback/` (Django 백엔드)
     - `https://yourapi.com/api/auth/google/callback/` (프로덕션)

4. **클라이언트 정보 저장**
   - Client ID와 Client Secret을 안전한 곳에 저장
   - 이 정보는 환경 변수로 관리할 예정

### 1.2 필요한 API 활성화

1. **Google+ API 활성화**
   - APIs & Services > Library
   - "Google+ API" 검색 후 활성화

2. **People API 활성화**
   - "Google People API" 검색 후 활성화

## 2. Django 백엔드 설정

### 2.1 필요한 패키지 설치

```bash
pip install django-oauth-toolkit
pip install google-auth google-auth-oauthlib google-auth-httplib2
pip install requests-oauthlib
```

### 2.2 Django 설정 (settings.py)

```python
# settings.py
INSTALLED_APPS = [
    # ... 기존 앱들
    'oauth2_provider',
    'corsheaders',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'oauth2_provider.middleware.OAuth2TokenMiddleware',
    # ... 기존 미들웨어들
]

# OAuth2 설정
OAUTH2_PROVIDER = {
    'SCOPES': {
        'read': 'Read scope',
        'write': 'Write scope',
    },
    'ACCESS_TOKEN_EXPIRE_SECONDS': 3600,
    'REFRESH_TOKEN_EXPIRE_SECONDS': 3600 * 24 * 7,  # 7일
}

# Google OAuth2 설정
GOOGLE_OAUTH2_CLIENT_ID = os.getenv('GOOGLE_OAUTH2_CLIENT_ID')
GOOGLE_OAUTH2_CLIENT_SECRET = os.getenv('GOOGLE_OAUTH2_CLIENT_SECRET')
GOOGLE_OAUTH2_REDIRECT_URI = os.getenv('GOOGLE_OAUTH2_REDIRECT_URI')

# CORS 설정 (React 앱과 통신용)
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # React 개발 서버
    "https://yourdomain.com",  # 프로덕션 도메인
]

CORS_ALLOW_CREDENTIALS = True
```

### 2.3 환경 변수 설정 (.env)

```bash
# .env 파일
GOOGLE_OAUTH2_CLIENT_ID=your_google_client_id_here
GOOGLE_OAUTH2_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_OAUTH2_REDIRECT_URI=http://localhost:8000/api/auth/google/callback/

# 프로덕션용
# GOOGLE_OAUTH2_REDIRECT_URI=https://yourapi.com/auth/google/callback/
```

### 2.4 URL 설정 (urls.py)

```python
# main urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('o/', include('oauth2_provider.urls', namespace='oauth2_provider')),
    path('auth/', include('authentication.urls')),
    path('api/', include('dashboard.urls')),
]

# authentication/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('google/', views.GoogleOAuth2LoginView.as_view(), name='google_oauth2_login'),
    path('google/callback/', views.GoogleOAuth2CallbackView.as_view(), name='google_oauth2_callback'),
]
```

## 3. React 프론트엔드 설정

### 3.1 필요한 패키지 설치

```bash
npm install @google-cloud/oauth2
# 또는
npm install google-auth-library
```

### 3.2 환경 변수 설정 (.env)

```bash
# React .env 파일
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id_here
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback/
```

### 3.3 Google OAuth2 버튼 구현

```typescript
// components/GoogleLoginButton.tsx
import React from 'react';

interface GoogleLoginButtonProps {
  onSuccess: (token: string) => void;
  onError: (error: string) => void;
}

const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({ onSuccess, onError }) => {
  const handleGoogleLogin = () => {
    const clientId = process.env.REACT_APP_GOOGLE_CLIENT_ID;
    const redirectUri = process.env.REACT_APP_GOOGLE_REDIRECT_URI;
    const scope = 'openid email profile';
    
    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${clientId}&` +
      `redirect_uri=${redirectUri}&` +
      `response_type=code&` +
      `scope=${scope}&` +
      `access_type=offline`;
    
    window.location.href = googleAuthUrl;
  };

  return (
    <button 
      onClick={handleGoogleLogin}
      className="google-login-btn"
    >
      Google로 로그인
    </button>
  );
};

export default GoogleLoginButton;
```

## 4. 보안 고려사항

### 4.1 환경 변수 보안

1. **개발 환경**
   - `.env` 파일을 `.gitignore`에 추가
   - 팀원들과는 `.env.example` 파일로 공유

2. **프로덕션 환경**
   - 클라우드 서비스의 환경 변수 설정 사용
   - AWS Parameter Store, Google Secret Manager 등 활용

### 4.2 HTTPS 설정

1. **개발 환경**
   - 로컬에서도 HTTPS 사용 권장 (mkcert 등 활용)

2. **프로덕션 환경**
   - 반드시 HTTPS 사용
   - SSL 인증서 설정

### 4.3 CORS 설정

```python
# settings.py
CORS_ALLOWED_ORIGINS = [
    "https://yourdomain.com",  # 프로덕션에서는 정확한 도메인만 허용
]

# 개발 환경에서만 모든 origin 허용
if DEBUG:
    CORS_ALLOW_ALL_ORIGINS = True
```

## 5. 테스트 및 디버깅

### 5.1 OAuth2 플로우 테스트

1. **로그인 버튼 클릭 테스트**
   - Google 로그인 페이지로 리다이렉트 확인
   - 권한 동의 화면 표시 확인

2. **콜백 처리 테스트**
   - 인증 코드 수신 확인
   - 토큰 교환 성공 확인
   - 사용자 정보 조회 확인

### 5.2 일반적인 오류 및 해결방법

1. **redirect_uri_mismatch 오류**
   - Google Console에서 설정한 Redirect URI와 코드의 URI가 일치하는지 확인
   - 프로토콜(http/https), 포트 번호까지 정확히 일치해야 함

2. **invalid_client 오류**
   - Client ID와 Client Secret이 올바른지 확인
   - 환경 변수가 제대로 로드되는지 확인

3. **CORS 오류**
   - Django CORS 설정 확인
   - 프론트엔드 도메인이 허용 목록에 있는지 확인

## 6. 배포 시 체크리스트

- [ ] Google Cloud Console에서 프로덕션 도메인 추가
- [ ] 프로덕션 환경 변수 설정
- [ ] HTTPS 인증서 설정
- [ ] CORS 설정을 프로덕션 도메인으로 제한
- [ ] OAuth 동의 화면 검토 및 승인 요청 (필요시)
- [ ] 테스트 계정으로 전체 플로우 테스트

## 7. BigQuery Service Account 설정

### 7.1 Service Account 키 파일 생성

BigQuery 샘플 데이터 생성을 위해서는 Google Cloud Service Account 키가 필요합니다.

**1. Google Cloud Console 접속**
   - https://console.cloud.google.com/ 접속
   - 프로젝트 선택

**2. Service Account 생성**
   - 좌측 메뉴 > IAM & Admin > Service Accounts
   - "CREATE SERVICE ACCOUNT" 클릭
   - Service account details:
     - Name: `bigquery-service-account`
     - Description: `BigQuery data management service account`
   - "CREATE AND CONTINUE" 클릭

**3. 권한 설정**
   - Grant this service account access to project에서:
     - `BigQuery Admin` 역할 추가
     - `BigQuery Data Editor` 역할 추가
     - `BigQuery Job User` 역할 추가
   - "CONTINUE" 클릭 > "DONE" 클릭

**4. JSON 키 파일 다운로드**
   - 생성된 서비스 계정 클릭
   - "KEYS" 탭 선택
   - "ADD KEY" > "Create new key" 클릭
   - Key type: JSON 선택
   - "CREATE" 클릭
   - JSON 키 파일이 자동 다운로드됩니다

**5. 키 파일 배치**
   - 다운로드된 JSON 파일을 안전한 위치에 저장
   - 예: `backend/credentials/service-account.json`
   - ⚠️ **절대 Git에 커밋하지 마세요!**

### 7.2 환경 변수 설정

**개발 환경 (.env)**
```bash
# BigQuery 설정
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json
```

**Docker 환경**
```yaml
# docker-compose.yml
services:
  backend:
    volumes:
      - ./backend/credentials:/app/credentials:ro
    environment:
      GOOGLE_CLOUD_PROJECT: your-project-id
      GOOGLE_APPLICATION_CREDENTIALS: /app/credentials/service-account.json
```

### 7.3 BigQuery API 활성화

**필수 API 활성화**
1. Google Cloud Console > APIs & Services > Library
2. 다음 API들을 검색하여 활성화:
   - **BigQuery API**
   - **BigQuery Storage API** (선택사항, 성능 향상)

### 7.4 샘플 데이터 생성 실행

```bash
# 1. 패키지 설치 (이미 완료됨)
docker-compose exec backend pip install pandas numpy faker

# 2. 샘플 데이터 생성
docker-compose exec backend python manage.py create_sample_bigquery_data \
  --project-id your-project-id \
  --dataset-id sample_data \
  --num-customers 1000 \
  --num-products 200 \
  --num-orders 5000

# 3. 생성 확인
# BigQuery Console에서 확인: https://console.cloud.google.com/bigquery
```

### 7.5 보안 고려사항

**키 파일 보안**
```bash
# .gitignore에 추가
backend/credentials/
*.json
service-account*.json

# 파일 권한 설정
chmod 600 backend/credentials/service-account.json
```

**프로덕션 환경**
- Google Cloud Secret Manager 사용 권장
- IAM 역할 기반 인증 사용
- 최소 권한 원칙 적용

## 8. 참고 자료

- [Google OAuth2 공식 문서](https://developers.google.com/identity/protocols/oauth2)
- [Django OAuth Toolkit 문서](https://django-oauth-toolkit.readthedocs.io/)
- [Google Cloud Console](https://console.cloud.google.com/)
- [BigQuery Service Account 설정](https://cloud.google.com/bigquery/docs/authentication/service-account-file)
- [Google Cloud IAM 권한 관리](https://cloud.google.com/iam/docs/understanding-roles)